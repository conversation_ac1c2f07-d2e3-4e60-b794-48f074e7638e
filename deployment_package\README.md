# 还款登记自动化系统 - 部署包

## 🎯 系统概述

这是一个完整的还款登记自动化系统，包含两个主要组件：
1. **还款凭证处理器** - 自动识别还款凭证图片并提取信息
2. **还款登记自动化机器人** - 自动登录系统完成还款登记操作

## 📁 文件结构

```
deployment_package/
├── payment_receipt_processor.py          # 还款凭证处理器主程序
├── payment_registration_bot_playwright.py # 还款登记机器人主程序
├── config.json                          # 系统配置文件
├── requirements.txt                     # Python依赖包
├── install.bat                         # Windows安装脚本
├── install.sh                          # Linux安装脚本
├── run_processor.bat                   # Windows凭证处理器启动脚本
├── run_processor.sh                    # Linux凭证处理器启动脚本
├── run_registration_bot.bat           # Windows登记机器人启动脚本
├── run_registration_bot.sh            # Linux登记机器人启动脚本
├── data/                              # 数据文件目录
│   └── 民生4期债务人明细.xlsx          # 债务人信息文件（需手动放入）
├── log/                               # 日志文件目录
├── png/                               # 截图文件目录
├── debug_html/                        # 调试HTML文件目录
└── README.md                          # 本文件
```

## 🚀 快速部署

### Windows系统

1. **下载部署包**
   ```bash
   # 解压到目标目录，如 C:\payment_system\
   ```

2. **运行安装脚本**
   ```cmd
   双击运行 install.bat
   ```

3. **配置系统**
   - 编辑 `config.json` 文件，配置用户信息
   - 将债务人明细Excel文件放入 `data/` 目录

4. **运行系统**
   ```cmd
   # 处理还款凭证
   双击运行 run_processor.bat
   
   # 执行还款登记
   双击运行 run_registration_bot.bat
   ```

### Linux系统

1. **下载部署包**
   ```bash
   # 解压到目标目录，如 /opt/payment_system/
   cd /opt/payment_system/
   ```

2. **设置执行权限并安装**
   ```bash
   chmod +x *.sh
   ./install.sh
   ```

3. **配置系统**
   ```bash
   # 编辑配置文件
   nano config.json
   
   # 放入债务人明细文件
   cp 民生4期债务人明细.xlsx data/
   ```

4. **运行系统**
   ```bash
   # 处理还款凭证
   ./run_processor.sh
   
   # 执行还款登记
   ./run_registration_bot.sh
   ```

## ⚙️ 系统配置

### config.json 配置说明

```json
{
  "users": {
    "用户名1": "密码1",
    "用户名2": "密码2"
  },
  "settings": {
    "base_url": "系统访问URL",
    "timeout": 30000,
    "retry_count": 3
  },
  "excel_config": {
    "sheet1_name": "处理结果明细",
    "sheet2_name": "还款对账明细",
    "key_columns": {
      // 列名映射配置
    }
  }
}
```

### 必需的数据文件

1. **债务人明细Excel文件** - 放入 `data/` 目录
2. **还款凭证图片** - 按文件夹组织
3. **处理结果Excel文件** - 放入 `D:\还款登记操作\` 目录（可配置）

## 🔧 系统要求

### 硬件要求
- **CPU**: 2核心或以上
- **内存**: 4GB RAM或以上
- **存储**: 10GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Python**: 3.8或更高版本
- **操作系统**: Windows 10/11 或 Linux (Ubuntu 18.04+, CentOS 7+)
- **浏览器**: Chromium（自动安装）

### 依赖组件
- PaddleOCR (OCR识别引擎)
- Playwright (浏览器自动化)
- Pandas (数据处理)
- DuckDB (轻量级数据库)

## 🔍 功能说明

### 还款凭证处理器功能
- ✅ 自动扫描文件夹下的所有图片
- ✅ 解压压缩文件（ZIP、RAR、7Z）
- ✅ OCR识别还款信息
- ✅ 文件名解析
- ✅ 债务人信息匹配
- ✅ 代存状态判断
- ✅ Excel结果输出

### 还款登记机器人功能
- ✅ 多用户自动登录
- ✅ 批量还款登记
- ✅ 多条记录处理
- ✅ 文件自动上传
- ✅ 错误自动重试
- ✅ 结果高亮标记

## 🐛 常见问题

### 安装问题

**Q: pip安装失败**
```bash
# 尝试使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
```

**Q: Playwright安装失败**
```bash
# 手动安装浏览器
playwright install chromium
```

### 运行问题

**Q: OCR识别效果不佳**
- 确保图片清晰度足够
- 检查PaddleOCR是否正确安装
- 尝试调整图片大小和对比度

**Q: 浏览器无法启动**
- 检查是否安装了Chromium
- Linux系统检查显示环境配置
- 尝试headless模式运行

**Q: 登录失败**
- 检查网络连接
- 验证config.json中的用户名密码
- 确认系统URL可访问

## 📊 日志分析

### 日志文件位置
- **处理器日志**: `log/payment_processor.log`
- **机器人日志**: `log/payment_registration_playwright.log`

### 日志级别
- **INFO**: 正常操作信息
- **WARNING**: 警告信息，需要关注
- **ERROR**: 错误信息，需要处理

## 🔄 更新部署

1. **备份数据**
   ```bash
   cp -r data/ data_backup/
   cp config.json config.json.bak
   ```

2. **更新文件**
   ```bash
   # 替换主程序文件
   # 保留配置和数据文件
   ```

3. **重新安装依赖**
   ```bash
   pip install -r requirements.txt --upgrade
   ```

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件确定错误原因
2. 检查系统要求和配置
3. 参考常见问题解决方案

## 📝 版本信息

- **版本**: v2.0
- **更新日期**: 2025-01-29
- **兼容性**: Python 3.8+, Windows 10+, Linux
- **主要特性**: 智能文件上传区域识别、高亮失败记录、多OCR引擎支持 
@echo off
chcp 65001 >nul
echo =============================================
echo 还款凭证处理器
echo =============================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python，请先运行install.bat安装环境
    pause
    exit /b 1
)

REM 检查主程序文件
if not exist "payment_receipt_processor.py" (
    echo 错误：未找到payment_receipt_processor.py文件
    pause
    exit /b 1
)

echo 正在启动还款凭证处理器...
echo 请确保：
echo 1. 已将图片文件放入指定文件夹
echo 2. 已正确配置文件夹路径
echo 3. 相关OCR服务正常运行
echo.

python payment_receipt_processor.py

echo.
echo 处理完成！请查看生成的Excel文件。
pause 
#!/bin/bash

echo "============================================="
echo "还款凭证处理器"
echo "============================================="
echo

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误：未检测到Python3，请先运行./install.sh安装环境"
    exit 1
fi

# 检查主程序文件
if [ ! -f "payment_receipt_processor.py" ]; then
    echo "错误：未找到payment_receipt_processor.py文件"
    exit 1
fi

echo "正在启动还款凭证处理器..."
echo "请确保："
echo "1. 已将图片文件放入指定文件夹"
echo "2. 已正确配置文件夹路径"
echo "3. 相关OCR服务正常运行"
echo

python3 payment_receipt_processor.py

echo
echo "处理完成！请查看生成的Excel文件。" 
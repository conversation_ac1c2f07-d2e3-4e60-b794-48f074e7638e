@echo off
chcp 65001 >nul
echo =============================================
echo 还款登记自动化系统 - 安装脚本
echo =============================================
echo.

REM 检查Python版本
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python，请先安装Python 3.8+
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

python -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" 2>nul
if errorlevel 1 (
    echo 错误：Python版本过低，需要Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✓ Python环境检查通过

REM 升级pip
echo [2/6] 升级pip...
python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple

REM 安装依赖包
echo [3/6] 安装Python依赖包...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
if errorlevel 1 (
    echo 警告：部分依赖包安装失败，尝试使用官方源...
    pip install -r requirements.txt
)

REM 安装Playwright浏览器
echo [4/6] 安装Playwright浏览器...
playwright install chromium
if errorlevel 1 (
    echo 警告：Playwright浏览器安装失败，请手动执行：
    echo playwright install chromium
)

REM 创建必要目录
echo [5/6] 创建工作目录...
if not exist "log" mkdir log
if not exist "png" mkdir png
if not exist "debug_html" mkdir debug_html
if not exist "data" mkdir data
echo ✓ 目录创建完成

REM 设置权限和环境变量
echo [6/6] 配置环境...
echo ✓ 环境配置完成

echo.
echo =============================================
echo 安装完成！
echo =============================================
echo.
echo 下一步：
echo 1. 将债务人明细Excel文件放入data目录
echo 2. 修改config.json中的用户配置
echo 3. 运行 run_processor.bat 开始处理
echo.
pause 
2025-07-20 19:29:26,478 - INFO - 配置文件加载成功: config.json
2025-07-20 19:30:06,444 - INFO - 配置文件加载成功: config.json
2025-07-20 19:30:06,444 - INFO - 开始运行还款登记自动化流程
2025-07-20 19:30:06,650 - INFO - Excel数据读取成功 - Sheet1: 3行, Sheet2: 3行
2025-07-20 19:30:06,652 - ERROR - 用户数据处理失败: '还款金额'
2025-07-20 19:30:06,652 - ERROR - 自动化流程运行失败: '还款金额'
2025-07-20 19:33:04,306 - INFO - 配置文件加载成功: config.json
2025-07-20 19:33:04,309 - INFO - 开始运行还款登记自动化流程
2025-07-20 19:33:04,541 - INFO - Excel数据读取成功 - Sheet1: 3行, Sheet2: 3行
2025-07-20 19:33:04,544 - INFO - 用户数据处理完成，共3个用户
2025-07-20 19:33:04,544 - INFO - 处理第 1/3 个用户: CY
2025-07-20 19:33:04,544 - INFO - 开始登录用户: CY
2025-07-20 19:33:04,545 - INFO - 导航到页面: https://ams.faamc.com
2025-07-20 19:33:04,545 - INFO - 等待元素: input[name='username']
2025-07-20 19:33:04,545 - INFO - 输入文本: 用户名输入框 = CY
2025-07-20 19:33:04,545 - INFO - 输入文本: 密码输入框 = FA123567
2025-07-20 19:33:04,545 - INFO - 点击元素: 登录按钮
2025-07-20 19:33:04,545 - INFO - 等待元素: .main-content
2025-07-20 19:33:04,546 - INFO - 用户 CY 登录成功
2025-07-20 19:33:04,546 - INFO - 开始处理用户 CY 的 1 个债务人
2025-07-20 19:33:04,546 - INFO - 处理第 1/1 个债务人: 张玉柱
2025-07-20 19:33:04,546 - INFO - 导航到债务人页面: 张玉柱 (c6f552e45f51f8c306ecf2276a37af11)
2025-07-20 19:33:04,546 - INFO - 导航到页面: https://ams.faamc.com/robin/collect/case/detail?debtorNo=c6f552e45f51f8c306ecf2276a37af11&projectCode=dssz20240716001&debtorName=张玉柱&ts=1753011184546
2025-07-20 19:33:04,546 - INFO - 等待元素: .case-detail
2025-07-20 19:33:04,546 - INFO - 点击还款登记按钮
2025-07-20 19:33:04,546 - INFO - 点击元素: 还款登记按钮
2025-07-20 19:33:04,547 - INFO - 等待元素: .modal-dialog
2025-07-20 19:33:04,547 - INFO - 开始填写还款表单: 张玉柱
2025-07-20 19:33:04,547 - INFO - 输入文本: 还款时间-第1行 = 2025-07-15 00:00:00
2025-07-20 19:33:04,547 - INFO - 输入文本: 还款人-第1行 = 张玉柱
2025-07-20 19:33:04,547 - INFO - 输入文本: 还款金额-第1行 = 1000
2025-07-20 19:33:04,547 - INFO - 输入文本: 还款账号-第1行 = 6214
2025-07-20 19:33:04,547 - INFO - 选择选项: 还款来源-第1行 = 本人还款
2025-07-20 19:33:04,548 - INFO - 点击元素: 合作方代存-第1行
2025-07-20 19:33:04,548 - INFO - 上传文件: 代存图片-第1行 = D:\民生4期回款\20250715民生银行信用卡回收明细\张玉柱1000\代还.jpg
2025-07-20 19:33:04,548 - INFO - 上传文件: 还款凭证-第1行 = D:\民生4期回款\20250715民生银行信用卡回收明细\张玉柱1000\张玉柱1000.jpg
2025-07-20 19:33:04,548 - INFO - 输入文本: 冲抵顺序-第1行 = 1
2025-07-20 19:33:04,548 - INFO - 点击元素: 确定按钮
2025-07-20 19:33:07,552 - INFO - 还款表单填写完成
2025-07-20 19:33:07,553 - INFO - 债务人 张玉柱 处理成功
2025-07-20 19:33:09,558 - INFO - 用户 CY 处理完成，成功: 1/1
2025-07-20 19:33:09,558 - INFO - 登出用户: CY
2025-07-20 19:33:09,558 - INFO - 点击元素: 退出按钮
2025-07-20 19:33:09,559 - INFO - 等待元素: input[name='username']
2025-07-20 19:33:09,559 - INFO - 用户登出成功
2025-07-20 19:33:14,565 - INFO - 处理第 2/3 个用户: TX
2025-07-20 19:33:14,566 - INFO - 开始登录用户: TX
2025-07-20 19:33:14,566 - INFO - 导航到页面: https://ams.faamc.com
2025-07-20 19:33:14,566 - INFO - 等待元素: input[name='username']
2025-07-20 19:33:14,566 - INFO - 输入文本: 用户名输入框 = TX
2025-07-20 19:33:14,566 - INFO - 输入文本: 密码输入框 = FA123567
2025-07-20 19:33:14,567 - INFO - 点击元素: 登录按钮
2025-07-20 19:33:14,567 - INFO - 等待元素: .main-content
2025-07-20 19:33:14,567 - INFO - 用户 TX 登录成功
2025-07-20 19:33:14,567 - INFO - 开始处理用户 TX 的 1 个债务人
2025-07-20 19:33:14,567 - INFO - 处理第 1/1 个债务人: 张磊
2025-07-20 19:33:14,567 - INFO - 导航到债务人页面: 张磊 (158ba5d82648caf4480206b1ff562bd4)
2025-07-20 19:33:14,568 - INFO - 导航到页面: https://ams.faamc.com/robin/collect/case/detail?debtorNo=158ba5d82648caf4480206b1ff562bd4&projectCode=dssz20240716001&debtorName=张磊&ts=1753011194567
2025-07-20 19:33:14,568 - INFO - 等待元素: .case-detail
2025-07-20 19:33:14,568 - INFO - 点击还款登记按钮
2025-07-20 19:33:14,568 - INFO - 点击元素: 还款登记按钮
2025-07-20 19:33:14,568 - INFO - 等待元素: .modal-dialog
2025-07-20 19:33:14,568 - INFO - 开始填写还款表单: 张磊
2025-07-20 19:33:14,568 - INFO - 输入文本: 还款时间-第1行 = 2025-07-15 00:00:00
2025-07-20 19:33:14,569 - INFO - 输入文本: 还款人-第1行 = 张磊
2025-07-20 19:33:14,569 - INFO - 输入文本: 还款金额-第1行 = 300
2025-07-20 19:33:14,569 - INFO - 输入文本: 还款账号-第1行 = 6236
2025-07-20 19:33:14,569 - INFO - 选择选项: 还款来源-第1行 = 本人还款
2025-07-20 19:33:14,569 - INFO - 上传文件: 还款凭证-第1行 = D:\民生4期回款\20250715民生银行信用卡回收明细\张磊300.jpg
2025-07-20 19:33:14,569 - INFO - 输入文本: 冲抵顺序-第1行 = 1
2025-07-20 19:33:14,570 - INFO - 点击元素: 确定按钮
2025-07-20 19:33:17,579 - INFO - 还款表单填写完成
2025-07-20 19:33:17,582 - INFO - 债务人 张磊 处理成功
2025-07-20 19:33:19,592 - INFO - 用户 TX 处理完成，成功: 1/1
2025-07-20 19:33:19,592 - INFO - 登出用户: TX
2025-07-20 19:33:19,592 - INFO - 点击元素: 退出按钮
2025-07-20 19:33:19,592 - INFO - 等待元素: input[name='username']
2025-07-20 19:33:19,593 - INFO - 用户登出成功
2025-07-20 19:33:24,609 - INFO - 处理第 3/3 个用户: syyb
2025-07-20 19:33:24,609 - INFO - 开始登录用户: syyb
2025-07-20 19:33:24,610 - INFO - 导航到页面: https://ams.faamc.com
2025-07-20 19:33:24,610 - INFO - 等待元素: input[name='username']
2025-07-20 19:33:24,611 - INFO - 输入文本: 用户名输入框 = syyb
2025-07-20 19:33:24,611 - INFO - 输入文本: 密码输入框 = FA123567
2025-07-20 19:33:24,611 - INFO - 点击元素: 登录按钮
2025-07-20 19:33:24,611 - INFO - 等待元素: .main-content
2025-07-20 19:33:24,612 - INFO - 用户 syyb 登录成功
2025-07-20 19:33:24,612 - INFO - 开始处理用户 syyb 的 1 个债务人
2025-07-20 19:33:24,612 - INFO - 处理第 1/1 个债务人: 张爽
2025-07-20 19:33:24,612 - INFO - 导航到债务人页面: 张爽 (c8ea1ef499e513f4bd18d3e34ddf27ca)
2025-07-20 19:33:24,612 - INFO - 导航到页面: https://ams.faamc.com/robin/collect/case/detail?debtorNo=c8ea1ef499e513f4bd18d3e34ddf27ca&projectCode=dssz20240716001&debtorName=张爽&ts=1753011204612
2025-07-20 19:33:24,612 - INFO - 等待元素: .case-detail
2025-07-20 19:33:24,612 - INFO - 点击还款登记按钮
2025-07-20 19:33:24,613 - INFO - 点击元素: 还款登记按钮
2025-07-20 19:33:24,613 - INFO - 等待元素: .modal-dialog
2025-07-20 19:33:24,613 - INFO - 开始填写还款表单: 张爽
2025-07-20 19:33:24,613 - INFO - 输入文本: 还款时间-第1行 = 2025-07-15 00:00:00
2025-07-20 19:33:24,613 - INFO - 输入文本: 还款人-第1行 = 张爽
2025-07-20 19:33:24,613 - INFO - 输入文本: 还款金额-第1行 = 400
2025-07-20 19:33:24,613 - INFO - 输入文本: 还款账号-第1行 = 6222
2025-07-20 19:33:24,614 - INFO - 选择选项: 还款来源-第1行 = 本人还款
2025-07-20 19:33:24,614 - INFO - 上传文件: 还款凭证-第1行 = D:\民生4期回款\20250715民生银行信用卡回收明细\张爽400.jpg
2025-07-20 19:33:24,614 - INFO - 输入文本: 冲抵顺序-第1行 = 1
2025-07-20 19:33:24,614 - INFO - 点击元素: 确定按钮
2025-07-20 19:33:27,616 - INFO - 还款表单填写完成
2025-07-20 19:33:27,617 - INFO - 债务人 张爽 处理成功
2025-07-20 19:33:29,631 - INFO - 用户 syyb 处理完成，成功: 1/1
2025-07-20 19:33:29,632 - INFO - 登出用户: syyb
2025-07-20 19:33:29,632 - INFO - 点击元素: 退出按钮
2025-07-20 19:33:29,632 - INFO - 等待元素: input[name='username']
2025-07-20 19:33:29,633 - INFO - 用户登出成功
2025-07-20 19:33:29,633 - INFO - 自动化流程完成，成功处理用户: 3/3

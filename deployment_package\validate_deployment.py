#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署验证脚本
检查部署包的完整性和运行环境
"""

import os
import sys
import json
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("1. 检查Python版本...")
    version = sys.version_info
    
    if version.major == 3 and version.minor >= 8:
        print(f"  ✓ Python版本: {version.major}.{version.minor}.{version.micro} (符合要求)")
        return True
    else:
        print(f"  ✗ Python版本: {version.major}.{version.minor}.{version.micro} (需要3.8或更高)")
        return False

def check_required_files():
    """检查必需文件"""
    print("2. 检查必需文件...")
    
    required_files = [
        "payment_receipt_processor.py",
        "payment_registration_bot_playwright.py",
        "config.json",
        "requirements.txt"
    ]
    
    optional_files = [
        "debtors.duckdb",
        "data/民生4期债务人明细.xlsx"
    ]
    
    all_good = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file} (必需)")
            all_good = False
    
    for file in optional_files:
        if os.path.exists(file):
            print(f"  ✓ {file} (可选)")
        else:
            print(f"  - {file} (可选，缺失)")
    
    return all_good

def check_directories():
    """检查目录结构"""
    print("3. 检查目录结构...")
    
    required_dirs = ["data", "log", "png", "debug_html"]
    
    all_good = True
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"  ✓ {dir_name}/")
        else:
            print(f"  - {dir_name}/ (将自动创建)")
            try:
                os.makedirs(dir_name, exist_ok=True)
                print(f"    ✓ 已创建 {dir_name}/")
            except Exception as e:
                print(f"    ✗ 创建失败: {str(e)}")
                all_good = False
    
    return all_good

def check_config_file():
    """检查配置文件"""
    print("4. 检查配置文件...")
    
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必需的配置节
        required_sections = ["users", "settings", "excel_config"]
        
        for section in required_sections:
            if section in config:
                print(f"  ✓ {section} 配置节存在")
                
                if section == "users":
                    user_count = len(config[section])
                    print(f"    - 配置了 {user_count} 个用户")
                
                elif section == "settings":
                    if "base_url" in config[section]:
                        print(f"    - 系统URL: {config[section]['base_url']}")
                    
            else:
                print(f"  ✗ {section} 配置节缺失")
                return False
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"  ✗ 配置文件JSON格式错误: {str(e)}")
        return False
    except FileNotFoundError:
        print(f"  ✗ 配置文件不存在")
        return False
    except Exception as e:
        print(f"  ✗ 读取配置文件失败: {str(e)}")
        return False

def check_dependencies():
    """检查Python依赖包"""
    print("5. 检查Python依赖包...")
    
    critical_packages = [
        "pandas",
        "openpyxl", 
        "playwright",
        "PIL"  # Pillow
    ]
    
    optional_packages = [
        "paddleocr",
        "duckdb",
        "easyocr"
    ]
    
    critical_ok = True
    
    for package in critical_packages:
        try:
            if package == "PIL":
                importlib.import_module("PIL")
            else:
                importlib.import_module(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} (关键依赖)")
            critical_ok = False
    
    for package in optional_packages:
        try:
            importlib.import_module(package)
            print(f"  ✓ {package} (可选)")
        except ImportError:
            print(f"  - {package} (可选，未安装)")
    
    return critical_ok

def check_playwright_browsers():
    """检查Playwright浏览器"""
    print("6. 检查Playwright浏览器...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # 检查Chromium是否可用
            try:
                browser = p.chromium.launch(headless=True)
                browser.close()
                print("  ✓ Chromium浏览器可用")
                return True
            except Exception as e:
                print(f"  ✗ Chromium浏览器不可用: {str(e)}")
                print("    请运行: playwright install chromium")
                return False
                
    except ImportError:
        print("  ✗ Playwright未安装")
        return False
    except Exception as e:
        print(f"  ✗ Playwright检查失败: {str(e)}")
        return False

def check_permissions():
    """检查文件权限"""
    print("7. 检查文件权限...")
    
    # 检查当前目录的读写权限
    try:
        # 测试写入权限
        test_file = "test_write_permission.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("  ✓ 当前目录可写")
        
        # 检查主程序文件的执行权限
        main_files = ["payment_receipt_processor.py", "payment_registration_bot_playwright.py"]
        for file in main_files:
            if os.path.exists(file):
                if os.access(file, os.R_OK):
                    print(f"  ✓ {file} 可读")
                else:
                    print(f"  ✗ {file} 不可读")
                    return False
        
        return True
        
    except Exception as e:
        print(f"  ✗ 权限检查失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("部署验证脚本 - 还款登记自动化系统")
    print("=" * 60)
    print()
    
    checks = [
        check_python_version,
        check_required_files,
        check_directories,
        check_config_file,
        check_dependencies,
        check_playwright_browsers,
        check_permissions
    ]
    
    results = []
    
    for check in checks:
        try:
            result = check()
            results.append(result)
            print()
        except Exception as e:
            print(f"  ✗ 检查过程出错: {str(e)}")
            results.append(False)
            print()
    
    # 汇总结果
    print("=" * 60)
    print("验证结果汇总")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ 所有检查通过 ({passed}/{total})")
        print("\n系统已准备就绪，可以开始使用！")
        print("\n建议的下一步:")
        print("1. 根据需要修改 config.json 中的用户配置")
        print("2. 确保数据文件放在正确的位置")
        print("3. 运行相应的启动脚本")
    else:
        failed = total - passed
        print(f"⚠ 检查完成，有 {failed} 项需要解决 ({passed}/{total})")
        print("\n请解决上述问题后再运行系统。")
        print("\n常见解决方案:")
        print("- 运行 install.bat 或 install.sh 安装依赖")
        print("- 检查文件是否正确放置")
        print("- 确保有足够的系统权限")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main() 
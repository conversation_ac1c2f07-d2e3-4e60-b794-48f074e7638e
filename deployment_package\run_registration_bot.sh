#!/bin/bash

echo "============================================="
echo "还款登记自动化机器人"
echo "============================================="
echo

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误：未检测到Python3，请先运行./install.sh安装环境"
    exit 1
fi

# 检查主程序文件
if [ ! -f "payment_registration_bot_playwright.py" ]; then
    echo "错误：未找到payment_registration_bot_playwright.py文件"
    exit 1
fi

# 检查配置文件
if [ ! -f "config.json" ]; then
    echo "错误：未找到config.json配置文件"
    exit 1
fi

echo "正在启动还款登记自动化机器人..."
echo "请确保："
echo "1. 已将处理好的Excel文件放入指定文件夹"
echo "2. 已正确配置config.json中的用户信息"
echo "3. 网络连接正常，可访问系统"
echo "4. 显示服务器已配置（如果在无头模式下运行）"
echo

# 检查是否有显示环境（对于Linux服务器）
if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
    echo "警告：未检测到显示环境，浏览器将在headless模式下运行"
    echo "如需GUI模式，请配置X11转发或VNC"
fi

python3 payment_registration_bot_playwright.py

echo
echo "还款登记操作完成！请查看生成的结果文件。" 
@echo off
chcp 65001 >nul
echo =============================================
echo 还款登记自动化机器人
echo =============================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python，请先运行install.bat安装环境
    pause
    exit /b 1
)

REM 检查主程序文件
if not exist "payment_registration_bot_playwright.py" (
    echo 错误：未找到payment_registration_bot_playwright.py文件
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config.json" (
    echo 错误：未找到config.json配置文件
    pause
    exit /b 1
)

echo 正在启动还款登记自动化机器人...
echo 请确保：
echo 1. 已将处理好的Excel文件放入D:\还款登记操作文件夹
echo 2. 已正确配置config.json中的用户信息
echo 3. 网络连接正常，可访问系统
echo.

python payment_registration_bot_playwright.py

echo.
echo 还款登记操作完成！请查看生成的结果文件。
pause 
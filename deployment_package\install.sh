#!/bin/bash

echo "============================================="
echo "还款登记自动化系统 - 安装脚本 (Linux)"
echo "============================================="
echo

# 检查Python版本
echo "[1/6] 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误：未检测到Python3，请先安装Python 3.8+"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "CentOS/RHEL: sudo yum install python3 python3-pip"
    exit 1
fi

# 检查Python版本
python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误：Python版本过低，需要Python 3.8或更高版本"
    exit 1
fi

echo "✓ Python环境检查通过"

# 升级pip
echo "[2/6] 升级pip..."
python3 -m pip install --upgrade pip

# 安装系统依赖
echo "[3/6] 安装系统依赖..."
if command -v apt &> /dev/null; then
    # Ubuntu/Debian
    sudo apt update
    sudo apt install -y python3-dev python3-pip libgl1-mesa-glx libglib2.0-0 libgtk-3-0 libxss1 libasound2
elif command -v yum &> /dev/null; then
    # CentOS/RHEL
    sudo yum install -y python3-devel python3-pip mesa-libGL gtk3 libXScrnSaver alsa-lib
fi

# 安装Python依赖
echo "[4/6] 安装Python依赖包..."
python3 -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
if [ $? -ne 0 ]; then
    echo "警告：部分依赖包安装失败，尝试使用官方源..."
    python3 -m pip install -r requirements.txt
fi

# 安装Playwright浏览器
echo "[5/6] 安装Playwright浏览器..."
python3 -m playwright install chromium
if [ $? -ne 0 ]; then
    echo "警告：Playwright浏览器安装失败，请手动执行："
    echo "python3 -m playwright install chromium"
fi

# 创建必要目录
echo "[6/6] 创建工作目录..."
mkdir -p log png debug_html data
chmod 755 log png debug_html data
echo "✓ 目录创建完成"

# 设置执行权限
chmod +x *.sh

echo
echo "============================================="
echo "安装完成！"
echo "============================================="
echo
echo "下一步："
echo "1. 将债务人明细Excel文件放入data目录"
echo "2. 修改config.json中的用户配置"
echo "3. 运行 ./run_processor.sh 开始处理"
echo 
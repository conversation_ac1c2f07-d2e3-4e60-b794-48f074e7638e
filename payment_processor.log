2025-07-30 15:45:33,920 - INFO - 🤖 自动选择OCR引擎...
2025-07-30 15:45:33,920 - INFO - ✅ 检测到豆包Seed-1.6 API，优先使用（AI视觉理解，最优中文识别）
2025-07-30 15:45:33,921 - INFO - 🚀 开始初始化豆包Seed-1.6 OCR引擎...
2025-07-30 15:45:34,933 - INFO - ✅ 豆包Seed-1.6引擎初始化成功
2025-07-30 15:45:34,933 - INFO -    🎯 AI视觉理解，最优中文识别
2025-07-30 15:45:34,933 - INFO -    🇨🇳 专门针对中文场景优化
2025-07-30 15:45:34,934 - INFO -    ⚡ 国内服务，响应速度快
2025-07-30 15:45:34,934 - INFO -    💰 极低成本，高性价比
2025-07-30 15:45:34,934 - INFO - 🎯 当前OCR引擎: 豆包Seed-1.6 AI视觉理解 (最优中文识别)
2025-07-30 15:45:34,974 - INFO - DuckDB数据库连接成功: debtors.duckdb
2025-07-30 15:45:34,992 - INFO - 使用现有DuckDB数据：23,043 条记录
2025-07-30 15:45:34,998 - INFO - 债务人数据库初始化成功：
2025-07-30 15:45:34,998 - INFO -   数据库类型: DuckDB
2025-07-30 15:45:34,998 - INFO -   总记录数: 23043
2025-07-30 15:45:34,998 - INFO -   有身份证号: 23043
2025-07-30 15:45:34,999 - INFO -   有合同号: 23043
2025-07-30 15:45:34,999 - INFO - 处理器初始化完成，OCR引擎: doubao
2025-07-30 15:45:35,003 - INFO - 开始处理文件夹: D:\民生4期回款
2025-07-30 15:45:35,003 - INFO - ================================================================================
2025-07-30 15:45:35,003 - INFO - 🎯 全部处理完成！
2025-07-30 15:45:35,003 - INFO - 📋 共处理 0 个子文件夹
2025-07-30 15:45:35,003 - INFO - 📄 成功生成 0 个Excel文件
2025-07-30 15:45:35,004 - INFO - 开始生成人名统计，共有 0 条处理结果
2025-07-30 15:45:35,004 - INFO - 处理完成，共处理 0 个目录，生成 0 个人员统计
2025-07-30 15:45:35,004 - INFO - 处理的目录列表: []
2025-07-30 15:45:35,004 - INFO - 最终生成 0 个人员统计记录
2025-07-30 16:19:29,305 - INFO - 🤖 自动选择OCR引擎...
2025-07-30 16:19:29,306 - INFO - ✅ 检测到豆包Seed-1.6 API，优先使用（AI视觉理解，最优中文识别）
2025-07-30 16:19:29,306 - INFO - 🚀 开始初始化豆包Seed-1.6 OCR引擎...
2025-07-30 16:19:30,097 - INFO - ✅ 豆包Seed-1.6引擎初始化成功
2025-07-30 16:19:30,097 - INFO -    🎯 AI视觉理解，最优中文识别
2025-07-30 16:19:30,097 - INFO -    🇨🇳 专门针对中文场景优化
2025-07-30 16:19:30,098 - INFO -    ⚡ 国内服务，响应速度快
2025-07-30 16:19:30,098 - INFO -    💰 极低成本，高性价比
2025-07-30 16:19:30,098 - INFO - 🎯 当前OCR引擎: 豆包Seed-1.6 AI视觉理解 (最优中文识别)
2025-07-30 16:19:30,115 - INFO - DuckDB数据库连接成功: debtors.duckdb
2025-07-30 16:19:30,132 - INFO - 使用现有DuckDB数据：23,043 条记录
2025-07-30 16:19:30,136 - INFO - 债务人数据库初始化成功：
2025-07-30 16:19:30,137 - INFO -   数据库类型: DuckDB
2025-07-30 16:19:30,137 - INFO -   总记录数: 23043
2025-07-30 16:19:30,137 - INFO -   有身份证号: 23043
2025-07-30 16:19:30,137 - INFO -   有合同号: 23043
2025-07-30 16:19:30,138 - INFO - 处理器初始化完成，OCR引擎: doubao
2025-07-30 16:19:30,145 - INFO - 开始处理文件夹: D:\民生4期回款
2025-07-30 16:19:30,145 - INFO - ================================================================================
2025-07-30 16:19:30,146 - INFO - 🗂️  开始处理子文件夹: 7.24 隆丰诉保
2025-07-30 16:19:30,147 - INFO - ✅ 目录 7.24 隆丰诉保 中Excel文件验证通过: 还款对账明细（合作方用）(1).xlsx
2025-07-30 16:19:30,147 - INFO - 目录 7.24 隆丰诉保 Excel验证成功
2025-07-30 16:19:30,147 - INFO - 🔍 扫描还款对账明细Excel: 7.24 隆丰诉保
2025-07-30 16:19:30,148 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-30 16:19:30,193 - INFO -    📋 Excel文件包含 21 行数据
2025-07-30 16:19:30,194 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号码', '还款金额': '现金回收', '类型': '还款类型（部分还款/结清）', '身份证号': '身份证号码'}
2025-07-30 16:19:30,194 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,195 - INFO -       债务人姓名: '王春花'
2025-07-30 16:19:30,195 - INFO -       合同号: '13523758.0'
2025-07-30 16:19:30,195 - INFO -       身份证号: '220283198204274521'
2025-07-30 16:19:30,195 - INFO - 🔍 使用身份证号查询: 220283198204274521
2025-07-30 16:19:30,197 - INFO -    数据库查询结果: found=True, debtor_no=e38f5990ec8479e843c316571d330a62
2025-07-30 16:19:30,198 - INFO - ✅ 通过身份证号找到债务人: e38f5990ec8479e843c316571d330a62
2025-07-30 16:19:30,198 - INFO -    ✅ 匹配成功: debtor_no=e38f5990ec8479e843c316571d330a62, 方式=身份证号
2025-07-30 16:19:30,199 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,199 - INFO -       债务人姓名: '姜虹'
2025-07-30 16:19:30,199 - INFO -       合同号: '398418.0'
2025-07-30 16:19:30,200 - INFO -       身份证号: '210282199305072626'
2025-07-30 16:19:30,200 - INFO - 🔍 使用身份证号查询: 210282199305072626
2025-07-30 16:19:30,201 - INFO -    数据库查询结果: found=True, debtor_no=e834c15c3a439f25d678e66e2a20ae7c
2025-07-30 16:19:30,201 - INFO - ✅ 通过身份证号找到债务人: e834c15c3a439f25d678e66e2a20ae7c
2025-07-30 16:19:30,201 - INFO -    ✅ 匹配成功: debtor_no=e834c15c3a439f25d678e66e2a20ae7c, 方式=身份证号
2025-07-30 16:19:30,202 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,202 - INFO -       债务人姓名: '张凤迪'
2025-07-30 16:19:30,203 - INFO -       合同号: '33511976.0'
2025-07-30 16:19:30,203 - INFO -       身份证号: '220122199411054835'
2025-07-30 16:19:30,203 - INFO - 🔍 使用身份证号查询: 220122199411054835
2025-07-30 16:19:30,206 - INFO -    数据库查询结果: found=True, debtor_no=f0abd85393bc4d8046991e57713f92fb
2025-07-30 16:19:30,206 - INFO - ✅ 通过身份证号找到债务人: f0abd85393bc4d8046991e57713f92fb
2025-07-30 16:19:30,206 - INFO -    ✅ 匹配成功: debtor_no=f0abd85393bc4d8046991e57713f92fb, 方式=身份证号
2025-07-30 16:19:30,208 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,208 - INFO -       债务人姓名: '赵辉'
2025-07-30 16:19:30,208 - INFO -       合同号: '12654982.0'
2025-07-30 16:19:30,209 - INFO -       身份证号: '210103196510072436'
2025-07-30 16:19:30,209 - INFO - 🔍 使用身份证号查询: 210103196510072436
2025-07-30 16:19:30,210 - INFO -    数据库查询结果: found=True, debtor_no=00741a402b904a80ca696e2bf7df0982
2025-07-30 16:19:30,210 - INFO - ✅ 通过身份证号找到债务人: 00741a402b904a80ca696e2bf7df0982
2025-07-30 16:19:30,211 - INFO -    ✅ 匹配成功: debtor_no=00741a402b904a80ca696e2bf7df0982, 方式=身份证号
2025-07-30 16:19:30,211 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,212 - INFO -       债务人姓名: '胡广海'
2025-07-30 16:19:30,212 - INFO -       合同号: '30590701.0'
2025-07-30 16:19:30,212 - INFO -       身份证号: '230183198906081253'
2025-07-30 16:19:30,212 - INFO - 🔍 使用身份证号查询: 230183198906081253
2025-07-30 16:19:30,213 - INFO -    数据库查询结果: found=True, debtor_no=6152d199172004935777279c22b15b1d
2025-07-30 16:19:30,214 - INFO - ✅ 通过身份证号找到债务人: 6152d199172004935777279c22b15b1d
2025-07-30 16:19:30,214 - INFO -    ✅ 匹配成功: debtor_no=6152d199172004935777279c22b15b1d, 方式=身份证号
2025-07-30 16:19:30,215 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,215 - INFO -       债务人姓名: '冯淑英'
2025-07-30 16:19:30,215 - INFO -       合同号: '12171338.0'
2025-07-30 16:19:30,215 - INFO -       身份证号: '210113196304253220'
2025-07-30 16:19:30,216 - INFO - 🔍 使用身份证号查询: 210113196304253220
2025-07-30 16:19:30,217 - INFO -    数据库查询结果: found=True, debtor_no=8f701bef9cd62e34551221b24ff30efa
2025-07-30 16:19:30,217 - INFO - ✅ 通过身份证号找到债务人: 8f701bef9cd62e34551221b24ff30efa
2025-07-30 16:19:30,217 - INFO -    ✅ 匹配成功: debtor_no=8f701bef9cd62e34551221b24ff30efa, 方式=身份证号
2025-07-30 16:19:30,218 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,218 - INFO -       债务人姓名: '司立民'
2025-07-30 16:19:30,218 - INFO -       合同号: '36930320.0'
2025-07-30 16:19:30,219 - INFO -       身份证号: '220222197205142718'
2025-07-30 16:19:30,219 - INFO - 🔍 使用身份证号查询: 220222197205142718
2025-07-30 16:19:30,220 - INFO -    数据库查询结果: found=True, debtor_no=23dcacdbbf322661d6edeaeea815ea35
2025-07-30 16:19:30,220 - INFO - ✅ 通过身份证号找到债务人: 23dcacdbbf322661d6edeaeea815ea35
2025-07-30 16:19:30,220 - INFO -    ✅ 匹配成功: debtor_no=23dcacdbbf322661d6edeaeea815ea35, 方式=身份证号
2025-07-30 16:19:30,222 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,222 - INFO -       债务人姓名: '盖相宇'
2025-07-30 16:19:30,222 - INFO -       合同号: '12546339.0'
2025-07-30 16:19:30,223 - INFO -       身份证号: '210902199108085512'
2025-07-30 16:19:30,223 - INFO - 🔍 使用身份证号查询: 210902199108085512
2025-07-30 16:19:30,224 - INFO -    数据库查询结果: found=True, debtor_no=0b98a015305cc4a9600e5363dfaefa08
2025-07-30 16:19:30,225 - INFO - ✅ 通过身份证号找到债务人: 0b98a015305cc4a9600e5363dfaefa08
2025-07-30 16:19:30,225 - INFO -    ✅ 匹配成功: debtor_no=0b98a015305cc4a9600e5363dfaefa08, 方式=身份证号
2025-07-30 16:19:30,226 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,226 - INFO -       债务人姓名: '王婷'
2025-07-30 16:19:30,226 - INFO -       合同号: '19877498.0'
2025-07-30 16:19:30,227 - INFO -       身份证号: '220322199101205643'
2025-07-30 16:19:30,227 - INFO - 🔍 使用身份证号查询: 220322199101205643
2025-07-30 16:19:30,228 - INFO -    数据库查询结果: found=True, debtor_no=68b5b1353c38ac8d223833c8c4435208
2025-07-30 16:19:30,228 - INFO - ✅ 通过身份证号找到债务人: 68b5b1353c38ac8d223833c8c4435208
2025-07-30 16:19:30,228 - INFO -    ✅ 匹配成功: debtor_no=68b5b1353c38ac8d223833c8c4435208, 方式=身份证号
2025-07-30 16:19:30,229 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,230 - INFO -       债务人姓名: '张忠波'
2025-07-30 16:19:30,230 - INFO -       合同号: '25240190.0'
2025-07-30 16:19:30,230 - INFO -       身份证号: '232101198203044090'
2025-07-30 16:19:30,230 - INFO - 🔍 使用身份证号查询: 232101198203044090
2025-07-30 16:19:30,231 - INFO -    数据库查询结果: found=True, debtor_no=652895ff70ed10588238464ee5ff35c1
2025-07-30 16:19:30,232 - INFO - ✅ 通过身份证号找到债务人: 652895ff70ed10588238464ee5ff35c1
2025-07-30 16:19:30,232 - INFO -    ✅ 匹配成功: debtor_no=652895ff70ed10588238464ee5ff35c1, 方式=身份证号
2025-07-30 16:19:30,233 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,233 - INFO -       债务人姓名: '李宁'
2025-07-30 16:19:30,233 - INFO -       合同号: '37673075.0'
2025-07-30 16:19:30,234 - INFO -       身份证号: '152224199410275515'
2025-07-30 16:19:30,234 - INFO - 🔍 使用身份证号查询: 152224199410275515
2025-07-30 16:19:30,235 - INFO -    数据库查询结果: found=True, debtor_no=480ce44d1053c64367cb0d241fd76c5e
2025-07-30 16:19:30,235 - INFO - ✅ 通过身份证号找到债务人: 480ce44d1053c64367cb0d241fd76c5e
2025-07-30 16:19:30,235 - INFO -    ✅ 匹配成功: debtor_no=480ce44d1053c64367cb0d241fd76c5e, 方式=身份证号
2025-07-30 16:19:30,236 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,237 - INFO -       债务人姓名: '陈东梅'
2025-07-30 16:19:30,239 - INFO -       合同号: '29246390.0'
2025-07-30 16:19:30,241 - INFO -       身份证号: '220821199505200022'
2025-07-30 16:19:30,241 - INFO - 🔍 使用身份证号查询: 220821199505200022
2025-07-30 16:19:30,243 - INFO -    数据库查询结果: found=True, debtor_no=0ba87a9f65195e03033fde8de9e6ef3a
2025-07-30 16:19:30,243 - INFO - ✅ 通过身份证号找到债务人: 0ba87a9f65195e03033fde8de9e6ef3a
2025-07-30 16:19:30,243 - INFO -    ✅ 匹配成功: debtor_no=0ba87a9f65195e03033fde8de9e6ef3a, 方式=身份证号
2025-07-30 16:19:30,245 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,245 - INFO -       债务人姓名: '耿凤云'
2025-07-30 16:19:30,245 - INFO -       合同号: '10318762.0'
2025-07-30 16:19:30,245 - INFO -       身份证号: '220102195109260044'
2025-07-30 16:19:30,245 - INFO - 🔍 使用身份证号查询: 220102195109260044
2025-07-30 16:19:30,246 - INFO -    数据库查询结果: found=True, debtor_no=7ca0dc3415e25999388c2b8ba48e2dc2
2025-07-30 16:19:30,247 - INFO - ✅ 通过身份证号找到债务人: 7ca0dc3415e25999388c2b8ba48e2dc2
2025-07-30 16:19:30,247 - INFO -    ✅ 匹配成功: debtor_no=7ca0dc3415e25999388c2b8ba48e2dc2, 方式=身份证号
2025-07-30 16:19:30,248 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,249 - INFO -       债务人姓名: '曲东升'
2025-07-30 16:19:30,249 - INFO -       合同号: '22744411.0'
2025-07-30 16:19:30,249 - INFO -       身份证号: '230105199104183018'
2025-07-30 16:19:30,249 - INFO - 🔍 使用身份证号查询: 230105199104183018
2025-07-30 16:19:30,251 - INFO -    数据库查询结果: found=True, debtor_no=0ad3c5be854347262cba3f5fa3145b56
2025-07-30 16:19:30,251 - INFO - ✅ 通过身份证号找到债务人: 0ad3c5be854347262cba3f5fa3145b56
2025-07-30 16:19:30,251 - INFO -    ✅ 匹配成功: debtor_no=0ad3c5be854347262cba3f5fa3145b56, 方式=身份证号
2025-07-30 16:19:30,252 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,252 - INFO -       债务人姓名: '高俊琴'
2025-07-30 16:19:30,253 - INFO -       合同号: '19303741.0'
2025-07-30 16:19:30,253 - INFO -       身份证号: '21120319640706352X'
2025-07-30 16:19:30,253 - INFO - 🔍 使用身份证号查询: 21120319640706352X
2025-07-30 16:19:30,254 - INFO -    数据库查询结果: found=True, debtor_no=b0ef16780bf0dd3002fe22ae4f6cada5
2025-07-30 16:19:30,255 - INFO - ✅ 通过身份证号找到债务人: b0ef16780bf0dd3002fe22ae4f6cada5
2025-07-30 16:19:30,256 - INFO -    ✅ 匹配成功: debtor_no=b0ef16780bf0dd3002fe22ae4f6cada5, 方式=身份证号
2025-07-30 16:19:30,257 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,257 - INFO -       债务人姓名: '张明兴'
2025-07-30 16:19:30,257 - INFO -       合同号: '33051411.0'
2025-07-30 16:19:30,258 - INFO -       身份证号: '220181198205273135'
2025-07-30 16:19:30,258 - INFO - 🔍 使用身份证号查询: 220181198205273135
2025-07-30 16:19:30,259 - INFO -    数据库查询结果: found=True, debtor_no=1de92547675f04ad45b3dfec791be3fe
2025-07-30 16:19:30,259 - INFO - ✅ 通过身份证号找到债务人: 1de92547675f04ad45b3dfec791be3fe
2025-07-30 16:19:30,259 - INFO -    ✅ 匹配成功: debtor_no=1de92547675f04ad45b3dfec791be3fe, 方式=身份证号
2025-07-30 16:19:30,260 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,260 - INFO -       债务人姓名: '崔海丰'
2025-07-30 16:19:30,261 - INFO -       合同号: '29186232.0'
2025-07-30 16:19:30,261 - INFO -       身份证号: '15222419850916751X'
2025-07-30 16:19:30,261 - INFO - 🔍 使用身份证号查询: 15222419850916751X
2025-07-30 16:19:30,262 - INFO -    数据库查询结果: found=True, debtor_no=0333ff8b79658fba92730433ee8c433e
2025-07-30 16:19:30,262 - INFO - ✅ 通过身份证号找到债务人: 0333ff8b79658fba92730433ee8c433e
2025-07-30 16:19:30,262 - INFO -    ✅ 匹配成功: debtor_no=0333ff8b79658fba92730433ee8c433e, 方式=身份证号
2025-07-30 16:19:30,263 - INFO -    🔍 准备匹配债务人:
2025-07-30 16:19:30,264 - INFO -       债务人姓名: '刘洋'
2025-07-30 16:19:30,264 - INFO -       合同号: '32406040.0'
2025-07-30 16:19:30,264 - INFO -       身份证号: '230407199407290020'
2025-07-30 16:19:30,264 - INFO - 🔍 使用身份证号查询: 230407199407290020
2025-07-30 16:19:30,265 - INFO -    数据库查询结果: found=True, debtor_no=ddfe98c625a3137623b23a17bd675746
2025-07-30 16:19:30,265 - INFO - ✅ 通过身份证号找到债务人: ddfe98c625a3137623b23a17bd675746
2025-07-30 16:19:30,265 - INFO -    ✅ 匹配成功: debtor_no=ddfe98c625a3137623b23a17bd675746, 方式=身份证号
2025-07-30 16:19:30,266 - INFO -    ✅ 成功处理 18 条对账明细记录
2025-07-30 16:19:30,267 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-30 16:19:30,267 - INFO - 🗂️ 扫描还款对账明细: 18 条记录
2025-07-30 16:19:30,267 - INFO - 处理目录: D:\民生4期回款\7.24 隆丰诉保
2025-07-30 16:19:30,268 - INFO - 🔍 扫描还款对账明细Excel: 7.24 隆丰诉保
2025-07-30 16:19:30,269 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-30 16:19:30,269 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-30 16:19:30,284 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:19:30,285 - INFO - ✅ 通过姓名找到唯一匹配: 冯淑英 → debtor_no: 8f701bef9cd62e34551221b24ff30efa
2025-07-30 16:19:30,285 - INFO - ✅ 通过还款对账明细找到debtor_no: 冯淑英 → 8f701bef9cd62e34551221b24ff30efa (姓名唯一匹配)
2025-07-30 16:19:30,285 - INFO - 📸 开始处理图片: 冯淑英3220-6000.jpg
2025-07-30 16:19:30,285 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:19:30,286 - INFO - 🔍 开始OCR识别图片: 冯淑英3220-6000.jpg
2025-07-30 16:19:30,286 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 冯淑英3220-6000.jpg
2025-07-30 16:19:47,453 - INFO - 🔄 使用文件名中的姓名: 冯淑英
2025-07-30 16:19:47,453 - ERROR - ❌ 豆包AI处理异常: 'int' object has no attribute 'strip'
2025-07-30 16:19:47,454 - ERROR - ❌ OCR识别失败，所有字段保持为空
2025-07-30 16:19:47,454 - INFO - 🎯 图片处理完成: 冯淑英3220-6000.jpg
2025-07-30 16:19:47,454 - INFO -    📋 最终结果 - 付款方: 【】, 账户: ***, 来源: 【】
2025-07-30 16:19:47,455 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:19:47,455 - INFO - ✅ 通过姓名找到唯一匹配: 刘洋 → debtor_no: ddfe98c625a3137623b23a17bd675746
2025-07-30 16:19:47,455 - INFO - ✅ 通过还款对账明细找到debtor_no: 刘洋 → ddfe98c625a3137623b23a17bd675746 (姓名唯一匹配)
2025-07-30 16:19:47,455 - INFO - 📸 开始处理图片: 刘洋0020-500.jpg
2025-07-30 16:19:47,456 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:19:47,456 - INFO - 🔍 开始OCR识别图片: 刘洋0020-500.jpg
2025-07-30 16:19:47,456 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 刘洋0020-500.jpg
2025-07-30 16:19:59,354 - INFO - ✅ 豆包AI分析成功: 👤刘洋 💳***5770 💰500.00 ⏰2025-07-23 12:03:38
2025-07-30 16:19:59,354 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:19:59,354 - INFO - 👤 付款方识别成功: 【刘洋】
2025-07-30 16:19:59,355 - INFO - 💳 账户号码识别成功: ***5770
2025-07-30 16:19:59,355 - INFO - 💰 还款金额识别成功: 500.00
2025-07-30 16:19:59,356 - INFO - ⏰ 还款时间识别成功: 2025-07-23 12:03:38 → 2025-07-23
2025-07-30 16:19:59,356 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:19:59,357 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:19:59,357 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:19:59,357 - INFO -    👤 还款人: 【刘洋】
2025-07-30 16:19:59,358 - INFO -    💳 还款账号: ***5770
2025-07-30 16:19:59,358 - INFO -    💰 还款金额: 500.00
2025-07-30 16:19:59,358 - INFO -    ⏰ 还款时间: 2025-07-23
2025-07-30 16:19:59,358 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:19:59,358 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:19:59,359 - INFO - 🎯 图片处理完成: 刘洋0020-500.jpg
2025-07-30 16:19:59,359 - INFO -    📋 最终结果 - 付款方: 【刘洋】, 账户: ***5770, 来源: 【本人还款】
2025-07-30 16:19:59,360 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:19:59,360 - INFO - ✅ 通过姓名找到唯一匹配: 司立民 → debtor_no: 23dcacdbbf322661d6edeaeea815ea35
2025-07-30 16:19:59,360 - INFO - ✅ 通过还款对账明细找到debtor_no: 司立民 → 23dcacdbbf322661d6edeaeea815ea35 (姓名唯一匹配)
2025-07-30 16:19:59,360 - INFO - 📸 开始处理图片: 司立民2718-5000.jpg
2025-07-30 16:19:59,360 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:19:59,361 - INFO - 🔍 开始OCR识别图片: 司立民2718-5000.jpg
2025-07-30 16:19:59,361 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 司立民2718-5000.jpg
2025-07-30 16:20:17,806 - INFO - ✅ 豆包AI分析成功: 👤司美丽 💳***6217 💰5000.00 ⏰2025-07-24 10:54:27
2025-07-30 16:20:17,807 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:20:17,807 - INFO - 👤 付款方识别成功: 【司美丽】
2025-07-30 16:20:17,807 - INFO - 💳 账户号码识别成功: ***6217
2025-07-30 16:20:17,807 - INFO - 💰 还款金额识别成功: 5000.00
2025-07-30 16:20:17,807 - INFO - ⏰ 还款时间识别成功: 2025-07-24 10:54:27 → 2025-07-24
2025-07-30 16:20:17,808 - INFO - 🔄 还款来源: 他人代还 (付款人[司美丽]与文件名[司立民]不匹配)
2025-07-30 16:20:17,808 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:20:17,808 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.24 隆丰诉保\司立民2718-5000.jpg
2025-07-30 16:20:17,809 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-30 16:20:17,809 - WARNING -    姓名匹配: OCR[司美丽] vs 文件名[司立民] = 0.00
2025-07-30 16:20:17,809 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.33)
2025-07-30 16:20:17,809 - WARNING -    金额匹配: OCR[5000.00] vs 文件名[5000] = 1.00
2025-07-30 16:20:17,809 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:20:17,810 - INFO -    👤 还款人: 【司美丽】
2025-07-30 16:20:17,810 - INFO -    💳 还款账号: ***6217
2025-07-30 16:20:17,810 - INFO -    💰 还款金额: 5000.00
2025-07-30 16:20:17,810 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:20:17,810 - INFO -    🔄 还款来源: 【他人代还】
2025-07-30 16:20:17,810 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-30 16:20:17,810 - INFO - 🎯 图片处理完成: 司立民2718-5000.jpg
2025-07-30 16:20:17,811 - INFO -    📋 最终结果 - 付款方: 【司美丽】, 账户: ***6217, 来源: 【他人代还】
2025-07-30 16:20:17,811 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:20:17,811 - INFO - ✅ 通过姓名找到唯一匹配: 姜虹 → debtor_no: e834c15c3a439f25d678e66e2a20ae7c
2025-07-30 16:20:17,811 - INFO - ✅ 通过还款对账明细找到debtor_no: 姜虹 → e834c15c3a439f25d678e66e2a20ae7c (姓名唯一匹配)
2025-07-30 16:20:17,812 - INFO - 📸 开始处理图片: 姜虹2626-1000.jpg
2025-07-30 16:20:17,812 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:20:17,812 - INFO - 🔍 开始OCR识别图片: 姜虹2626-1000.jpg
2025-07-30 16:20:17,812 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 姜虹2626-1000.jpg
2025-07-30 16:20:36,549 - INFO - ✅ 豆包AI分析成功: 👤姜敏 💳***1671 💰1000.00 ⏰2023-05-25 00:00:00
2025-07-30 16:20:36,549 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:20:36,549 - INFO - 👤 付款方识别成功: 【姜敏】
2025-07-30 16:20:36,550 - INFO - 💳 账户号码识别成功: ***1671
2025-07-30 16:20:36,550 - INFO - 💰 还款金额识别成功: 1000.00
2025-07-30 16:20:36,550 - INFO - ⏰ 还款时间识别成功: 2023-05-25 00:00:00 → 2023-05-25
2025-07-30 16:20:36,551 - INFO - 🔄 还款来源: 他人代还 (付款人[姜敏]与文件名[姜虹]不匹配)
2025-07-30 16:20:36,551 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:20:36,552 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.24 隆丰诉保\姜虹2626-1000.jpg
2025-07-30 16:20:36,552 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-30 16:20:36,552 - WARNING -    姓名匹配: OCR[姜敏] vs 文件名[姜虹] = 0.00
2025-07-30 16:20:36,553 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.50)
2025-07-30 16:20:36,553 - WARNING -    金额匹配: OCR[1000.00] vs 文件名[1000] = 1.00
2025-07-30 16:20:36,553 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:20:36,554 - INFO -    👤 还款人: 【姜敏】
2025-07-30 16:20:36,554 - INFO -    💳 还款账号: ***1671
2025-07-30 16:20:36,554 - INFO -    💰 还款金额: 1000.00
2025-07-30 16:20:36,555 - INFO -    ⏰ 还款时间: 2023-05-25
2025-07-30 16:20:36,555 - INFO -    🔄 还款来源: 【他人代还】
2025-07-30 16:20:36,556 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-30 16:20:36,556 - INFO - 🎯 图片处理完成: 姜虹2626-1000.jpg
2025-07-30 16:20:36,556 - INFO -    📋 最终结果 - 付款方: 【姜敏】, 账户: ***1671, 来源: 【他人代还】
2025-07-30 16:20:36,557 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:20:36,557 - INFO - ✅ 通过姓名找到唯一匹配: 崔海丰 → debtor_no: 0333ff8b79658fba92730433ee8c433e
2025-07-30 16:20:36,557 - INFO - ✅ 通过还款对账明细找到debtor_no: 崔海丰 → 0333ff8b79658fba92730433ee8c433e (姓名唯一匹配)
2025-07-30 16:20:36,558 - INFO - 📸 开始处理图片: 崔海丰751X-1000.jpg
2025-07-30 16:20:36,558 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:20:36,558 - INFO - 🔍 开始OCR识别图片: 崔海丰751X-1000.jpg
2025-07-30 16:20:36,558 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 崔海丰751X-1000.jpg
2025-07-30 16:20:52,174 - INFO - ✅ 豆包AI分析成功: 👤崔海兵 💳***0656 💰1000.00 ⏰2025-07-24 16:39:27
2025-07-30 16:20:52,174 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:20:52,174 - INFO - 👤 付款方识别成功: 【崔海兵】
2025-07-30 16:20:52,174 - INFO - 💳 账户号码识别成功: ***0656
2025-07-30 16:20:52,174 - INFO - 💰 还款金额识别成功: 1000.00
2025-07-30 16:20:52,175 - INFO - ⏰ 还款时间识别成功: 2025-07-24 16:39:27 → 2025-07-24
2025-07-30 16:20:52,175 - INFO - 🔄 还款来源: 他人代还 (付款人[崔海兵]与文件名[崔海丰]不匹配)
2025-07-30 16:20:52,175 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:20:52,175 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.24 隆丰诉保\崔海丰751X-1000.jpg
2025-07-30 16:20:52,175 - WARNING -    状态: 部分一致, 置信度: 0.64
2025-07-30 16:20:52,176 - WARNING -    姓名匹配: OCR[崔海兵] vs 文件名[崔海丰] = 0.40
2025-07-30 16:20:52,176 - WARNING -    ⚠️ 姓名不匹配: 相似度匹配(0.67)
2025-07-30 16:20:52,176 - WARNING -    金额匹配: OCR[1000.00] vs 文件名[1000] = 1.00
2025-07-30 16:20:52,176 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:20:52,176 - INFO -    👤 还款人: 【崔海兵】
2025-07-30 16:20:52,177 - INFO -    💳 还款账号: ***0656
2025-07-30 16:20:52,177 - INFO -    💰 还款金额: 1000.00
2025-07-30 16:20:52,177 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:20:52,177 - INFO -    🔄 还款来源: 【他人代还】
2025-07-30 16:20:52,178 - INFO -    ✅ 匹配验证: 部分一致 (置信度: 0.64)
2025-07-30 16:20:52,178 - INFO - 🎯 图片处理完成: 崔海丰751X-1000.jpg
2025-07-30 16:20:52,178 - INFO -    📋 最终结果 - 付款方: 【崔海兵】, 账户: ***0656, 来源: 【他人代还】
2025-07-30 16:20:52,181 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x048'
2025-07-30 16:20:52,182 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\t`'
2025-07-30 16:20:52,182 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00>'
2025-07-30 16:20:52,182 - DEBUG - tag: Orientation (274) - type: long (4) - value: b'\x00\x00\x00\x00'
2025-07-30 16:20:52,182 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:20:52,183 - INFO - ✅ 通过姓名找到唯一匹配: 张凤迪 → debtor_no: f0abd85393bc4d8046991e57713f92fb
2025-07-30 16:20:52,183 - INFO - ✅ 通过还款对账明细找到debtor_no: 张凤迪 → f0abd85393bc4d8046991e57713f92fb (姓名唯一匹配)
2025-07-30 16:20:52,183 - INFO - 📸 开始处理图片: 张凤迪4835-900.jpg
2025-07-30 16:20:52,184 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:20:52,184 - INFO - 🔍 开始OCR识别图片: 张凤迪4835-900.jpg
2025-07-30 16:20:52,184 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张凤迪4835-900.jpg
2025-07-30 16:20:52,185 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x048'
2025-07-30 16:20:52,187 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\t`'
2025-07-30 16:20:52,187 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00>'
2025-07-30 16:20:52,187 - DEBUG - tag: Orientation (274) - type: long (4) - value: b'\x00\x00\x00\x00'
2025-07-30 16:21:15,429 - INFO - 🔄 使用文件名中的姓名: 张凤迪
2025-07-30 16:21:15,429 - INFO - ✅ 豆包AI分析成功: 👤张凤迪 💳***1773 💰900.00 ⏰2025-07-24 09:15:57
2025-07-30 16:21:15,430 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:21:15,430 - INFO - 👤 付款方识别成功: 【张凤迪】
2025-07-30 16:21:15,430 - INFO - 💳 账户号码识别成功: ***1773
2025-07-30 16:21:15,430 - INFO - 💰 还款金额识别成功: 900.00
2025-07-30 16:21:15,430 - INFO - ⏰ 还款时间识别成功: 2025-07-24 09:15:57 → 2025-07-24
2025-07-30 16:21:15,431 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:21:15,431 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:21:15,431 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:21:15,431 - INFO -    👤 还款人: 【张凤迪】
2025-07-30 16:21:15,431 - INFO -    💳 还款账号: ***1773
2025-07-30 16:21:15,432 - INFO -    💰 还款金额: 900.00
2025-07-30 16:21:15,432 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:21:15,432 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:21:15,432 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:21:15,432 - INFO - 🎯 图片处理完成: 张凤迪4835-900.jpg
2025-07-30 16:21:15,432 - INFO -    📋 最终结果 - 付款方: 【张凤迪】, 账户: ***1773, 来源: 【本人还款】
2025-07-30 16:21:15,433 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:21:15,433 - INFO - ✅ 通过姓名找到唯一匹配: 张忠波 → debtor_no: 652895ff70ed10588238464ee5ff35c1
2025-07-30 16:21:15,433 - INFO - ✅ 通过还款对账明细找到debtor_no: 张忠波 → 652895ff70ed10588238464ee5ff35c1 (姓名唯一匹配)
2025-07-30 16:21:15,434 - INFO - 📸 开始处理图片: 张忠波4090-3000.jpg
2025-07-30 16:21:15,434 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:21:15,434 - INFO - 🔍 开始OCR识别图片: 张忠波4090-3000.jpg
2025-07-30 16:21:15,434 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张忠波4090-3000.jpg
2025-07-30 16:21:33,838 - INFO - ✅ 豆包AI分析成功: 👤*伟 💳***8316 💰3000.00 ⏰2025-07-24 11:09:00
2025-07-30 16:21:33,839 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:21:33,839 - INFO - ⭐ 检测到带星号的付款方: 【*伟】，进行星号验证...
2025-07-30 16:21:33,839 - INFO - ✅ 通过姓名找到唯一匹配: 张忠波 → debtor_no: 652895ff70ed10588238464ee5ff35c1
2025-07-30 16:21:33,840 - INFO - ✅ 星号还款人验证通过，将替换: *伟 → 张忠波
2025-07-30 16:21:33,840 - INFO - ✅ 星号付款方替换成功: 【*伟】 → 【张忠波】
2025-07-30 16:21:33,840 - INFO -    替换原因: 满足所有条件：含*号[*伟]，文件名债务人[张忠波]，Excel有debtor_no[652895ff70ed10588238464ee5ff35c1]，时间一致[2025-07-24]
2025-07-30 16:21:33,841 - INFO - 💳 账户号码识别成功: ***8316
2025-07-30 16:21:33,841 - INFO - 💰 还款金额识别成功: 3000.00
2025-07-30 16:21:33,842 - INFO - ⏰ 还款时间识别成功: 2025-07-24 11:09:00 → 2025-07-24
2025-07-30 16:21:33,842 - INFO - 🔄 还款来源: 他人代还 (付款人[*伟]与文件名[张忠波]不匹配)
2025-07-30 16:21:33,842 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:21:33,843 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.24 隆丰诉保\张忠波4090-3000.jpg
2025-07-30 16:21:33,843 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-30 16:21:33,843 - WARNING -    姓名匹配: OCR[*伟] vs 文件名[张忠波] = 0.00
2025-07-30 16:21:33,844 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-30 16:21:33,844 - WARNING -    金额匹配: OCR[3000.00] vs 文件名[3000] = 1.00
2025-07-30 16:21:33,844 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:21:33,844 - INFO -    👤 还款人: 【张忠波】
2025-07-30 16:21:33,845 - INFO -    💳 还款账号: ***8316
2025-07-30 16:21:33,845 - INFO -    💰 还款金额: 3000.00
2025-07-30 16:21:33,845 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:21:33,845 - INFO -    🔄 还款来源: 【他人代还】
2025-07-30 16:21:33,846 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-30 16:21:33,846 - INFO - 🎯 图片处理完成: 张忠波4090-3000.jpg
2025-07-30 16:21:33,846 - INFO -    📋 最终结果 - 付款方: 【张忠波】, 账户: ***8316, 来源: 【他人代还】
2025-07-30 16:21:33,847 - DEBUG - STREAM b'IHDR' 16 13
2025-07-30 16:21:33,847 - DEBUG - STREAM b'sRGB' 41 1
2025-07-30 16:21:33,847 - DEBUG - STREAM b'gAMA' 54 4
2025-07-30 16:21:33,847 - DEBUG - STREAM b'pHYs' 70 9
2025-07-30 16:21:33,847 - DEBUG - STREAM b'IDAT' 91 42064
2025-07-30 16:21:33,848 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:21:33,848 - INFO - ✅ 通过姓名找到唯一匹配: 张明兴 → debtor_no: 1de92547675f04ad45b3dfec791be3fe
2025-07-30 16:21:33,848 - INFO - ✅ 通过还款对账明细找到debtor_no: 张明兴 → 1de92547675f04ad45b3dfec791be3fe (姓名唯一匹配)
2025-07-30 16:21:33,848 - INFO - 📸 开始处理图片: 张明兴3135-3000.png
2025-07-30 16:21:33,849 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:21:33,849 - INFO - 🔍 开始OCR识别图片: 张明兴3135-3000.png
2025-07-30 16:21:33,849 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张明兴3135-3000.png
2025-07-30 16:21:33,850 - DEBUG - STREAM b'IHDR' 16 13
2025-07-30 16:21:33,850 - DEBUG - STREAM b'sRGB' 41 1
2025-07-30 16:21:33,850 - DEBUG - STREAM b'gAMA' 54 4
2025-07-30 16:21:33,850 - DEBUG - STREAM b'pHYs' 70 9
2025-07-30 16:21:33,850 - DEBUG - STREAM b'IDAT' 91 42064
2025-07-30 16:21:56,185 - INFO - 🔄 使用文件名中的姓名: 张明兴
2025-07-30 16:21:56,185 - INFO - ✅ 豆包AI分析成功: 👤张明兴 💳***6217 💰3000.00 ⏰2025-07-24 16:05:38
2025-07-30 16:21:56,185 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:21:56,185 - INFO - 👤 付款方识别成功: 【张明兴】
2025-07-30 16:21:56,186 - INFO - 💳 账户号码识别成功: ***6217
2025-07-30 16:21:56,186 - INFO - 💰 还款金额识别成功: 3000.00
2025-07-30 16:21:56,186 - INFO - ⏰ 还款时间识别成功: 2025-07-24 16:05:38 → 2025-07-24
2025-07-30 16:21:56,186 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:21:56,186 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:21:56,187 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:21:56,187 - INFO -    👤 还款人: 【张明兴】
2025-07-30 16:21:56,187 - INFO -    💳 还款账号: ***6217
2025-07-30 16:21:56,187 - INFO -    💰 还款金额: 3000.00
2025-07-30 16:21:56,187 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:21:56,188 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:21:56,188 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:21:56,188 - INFO - 🎯 图片处理完成: 张明兴3135-3000.png
2025-07-30 16:21:56,188 - INFO -    📋 最终结果 - 付款方: 【张明兴】, 账户: ***6217, 来源: 【本人还款】
2025-07-30 16:21:56,189 - DEBUG - STREAM b'IHDR' 16 13
2025-07-30 16:21:56,189 - DEBUG - STREAM b'sRGB' 41 1
2025-07-30 16:21:56,189 - DEBUG - STREAM b'eXIf' 54 142
2025-07-30 16:21:56,189 - DEBUG - STREAM b'iTXt' 208 495
2025-07-30 16:21:56,190 - DEBUG - STREAM b'IDAT' 715 16384
2025-07-30 16:21:56,191 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:21:56,191 - INFO - ✅ 通过姓名找到唯一匹配: 曲东升 → debtor_no: 0ad3c5be854347262cba3f5fa3145b56
2025-07-30 16:21:56,191 - INFO - ✅ 通过还款对账明细找到debtor_no: 曲东升 → 0ad3c5be854347262cba3f5fa3145b56 (姓名唯一匹配)
2025-07-30 16:21:56,191 - INFO - 📸 开始处理图片: 曲东升3018-5500.png
2025-07-30 16:21:56,191 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:21:56,192 - INFO - 🔍 开始OCR识别图片: 曲东升3018-5500.png
2025-07-30 16:21:56,192 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 曲东升3018-5500.png
2025-07-30 16:21:56,192 - DEBUG - STREAM b'IHDR' 16 13
2025-07-30 16:21:56,192 - DEBUG - STREAM b'sRGB' 41 1
2025-07-30 16:21:56,192 - DEBUG - STREAM b'eXIf' 54 142
2025-07-30 16:21:56,193 - DEBUG - STREAM b'iTXt' 208 495
2025-07-30 16:21:56,193 - DEBUG - STREAM b'IDAT' 715 16384
2025-07-30 16:22:24,425 - INFO - 🔄 使用文件名中的姓名: 曲东升
2025-07-30 16:22:24,425 - INFO - ✅ 豆包AI分析成功: 👤曲东升 💳***7719 💰5500.00 ⏰2025-07-24 14:29:00
2025-07-30 16:22:24,425 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:22:24,425 - INFO - 👤 付款方识别成功: 【曲东升】
2025-07-30 16:22:24,426 - INFO - 💳 账户号码识别成功: ***7719
2025-07-30 16:22:24,426 - INFO - 💰 还款金额识别成功: 5500.00
2025-07-30 16:22:24,426 - INFO - ⏰ 还款时间识别成功: 2025-07-24 14:29:00 → 2025-07-24
2025-07-30 16:22:24,426 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:22:24,426 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:22:24,427 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:22:24,427 - INFO -    👤 还款人: 【曲东升】
2025-07-30 16:22:24,427 - INFO -    💳 还款账号: ***7719
2025-07-30 16:22:24,427 - INFO -    💰 还款金额: 5500.00
2025-07-30 16:22:24,428 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:22:24,428 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:22:24,428 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:22:24,428 - INFO - 🎯 图片处理完成: 曲东升3018-5500.png
2025-07-30 16:22:24,428 - INFO -    📋 最终结果 - 付款方: 【曲东升】, 账户: ***7719, 来源: 【本人还款】
2025-07-30 16:22:24,429 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:22:24,429 - INFO - ✅ 通过姓名找到唯一匹配: 李宁 → debtor_no: 480ce44d1053c64367cb0d241fd76c5e
2025-07-30 16:22:24,429 - INFO - ✅ 通过还款对账明细找到debtor_no: 李宁 → 480ce44d1053c64367cb0d241fd76c5e (姓名唯一匹配)
2025-07-30 16:22:24,430 - INFO - 📸 开始处理图片: 李宁5515-14598.jpg
2025-07-30 16:22:24,430 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:22:24,430 - INFO - 🔍 开始OCR识别图片: 李宁5515-14598.jpg
2025-07-30 16:22:24,430 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李宁5515-14598.jpg
2025-07-30 16:22:50,846 - INFO - ✅ 豆包AI分析成功: 👤康凯 💳***9579 💰14598.00 ⏰2025-07-24 11:21:43
2025-07-30 16:22:50,846 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:22:50,846 - INFO - 👤 付款方识别成功: 【康凯】
2025-07-30 16:22:50,847 - INFO - 💳 账户号码识别成功: ***9579
2025-07-30 16:22:50,847 - INFO - 💰 还款金额识别成功: 14598.00
2025-07-30 16:22:50,847 - INFO - ⏰ 还款时间识别成功: 2025-07-24 11:21:43 → 2025-07-24
2025-07-30 16:22:50,847 - INFO - 🔄 还款来源: 他人代还 (付款人[康凯]与文件名[李宁]不匹配)
2025-07-30 16:22:50,848 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:22:50,848 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.24 隆丰诉保\李宁5515-14598.jpg
2025-07-30 16:22:50,848 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-30 16:22:50,848 - WARNING -    姓名匹配: OCR[康凯] vs 文件名[李宁] = 0.00
2025-07-30 16:22:50,848 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-30 16:22:50,848 - WARNING -    金额匹配: OCR[14598.00] vs 文件名[14598] = 1.00
2025-07-30 16:22:50,849 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:22:50,849 - INFO -    👤 还款人: 【康凯】
2025-07-30 16:22:50,849 - INFO -    💳 还款账号: ***9579
2025-07-30 16:22:50,849 - INFO -    💰 还款金额: 14598.00
2025-07-30 16:22:50,849 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:22:50,850 - INFO -    🔄 还款来源: 【他人代还】
2025-07-30 16:22:50,850 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-30 16:22:50,850 - INFO - 🎯 图片处理完成: 李宁5515-14598.jpg
2025-07-30 16:22:50,850 - INFO -    📋 最终结果 - 付款方: 【康凯】, 账户: ***9579, 来源: 【他人代还】
2025-07-30 16:22:50,850 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:22:50,851 - INFO - ✅ 通过姓名找到唯一匹配: 王婷 → debtor_no: 68b5b1353c38ac8d223833c8c4435208
2025-07-30 16:22:50,851 - INFO - ✅ 通过还款对账明细找到debtor_no: 王婷 → 68b5b1353c38ac8d223833c8c4435208 (姓名唯一匹配)
2025-07-30 16:22:50,851 - INFO - 📸 开始处理图片: 王婷5643-2000.jpg
2025-07-30 16:22:50,851 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:22:50,851 - INFO - 🔍 开始OCR识别图片: 王婷5643-2000.jpg
2025-07-30 16:22:50,851 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王婷5643-2000.jpg
2025-07-30 16:23:16,028 - INFO - ✅ 豆包AI分析成功: 👤王婷 💳***8279 💰2000.00 ⏰2025-07-24 11:06:40
2025-07-30 16:23:16,028 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:23:16,028 - INFO - 👤 付款方识别成功: 【王婷】
2025-07-30 16:23:16,029 - INFO - 💳 账户号码识别成功: ***8279
2025-07-30 16:23:16,029 - INFO - 💰 还款金额识别成功: 2000.00
2025-07-30 16:23:16,029 - INFO - ⏰ 还款时间识别成功: 2025-07-24 11:06:40 → 2025-07-24
2025-07-30 16:23:16,029 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:23:16,030 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:23:16,030 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:23:16,030 - INFO -    👤 还款人: 【王婷】
2025-07-30 16:23:16,030 - INFO -    💳 还款账号: ***8279
2025-07-30 16:23:16,030 - INFO -    💰 还款金额: 2000.00
2025-07-30 16:23:16,031 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:23:16,031 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:23:16,031 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:23:16,031 - INFO - 🎯 图片处理完成: 王婷5643-2000.jpg
2025-07-30 16:23:16,031 - INFO -    📋 最终结果 - 付款方: 【王婷】, 账户: ***8279, 来源: 【本人还款】
2025-07-30 16:23:16,032 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:23:16,032 - INFO - ✅ 通过姓名找到唯一匹配: 王春花 → debtor_no: e38f5990ec8479e843c316571d330a62
2025-07-30 16:23:16,032 - INFO - ✅ 通过还款对账明细找到debtor_no: 王春花 → e38f5990ec8479e843c316571d330a62 (姓名唯一匹配)
2025-07-30 16:23:16,032 - INFO - 📸 开始处理图片: 王春花4521-4500.jpg
2025-07-30 16:23:16,032 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:23:16,033 - INFO - 🔍 开始OCR识别图片: 王春花4521-4500.jpg
2025-07-30 16:23:16,033 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王春花4521-4500.jpg
2025-07-30 16:23:37,626 - INFO - 🔄 使用文件名中的姓名: 王春花
2025-07-30 16:23:37,627 - INFO - ✅ 豆包AI分析成功: 👤王春花 💳***8417 💰4500.00 ⏰2025-07-23 21:59:08
2025-07-30 16:23:37,627 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:23:37,627 - INFO - 👤 付款方识别成功: 【王春花】
2025-07-30 16:23:37,628 - INFO - 💳 账户号码识别成功: ***8417
2025-07-30 16:23:37,628 - INFO - 💰 还款金额识别成功: 4500.00
2025-07-30 16:23:37,628 - INFO - ⏰ 还款时间识别成功: 2025-07-23 21:59:08 → 2025-07-23
2025-07-30 16:23:37,628 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:23:37,628 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:23:37,629 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:23:37,629 - INFO -    👤 还款人: 【王春花】
2025-07-30 16:23:37,629 - INFO -    💳 还款账号: ***8417
2025-07-30 16:23:37,629 - INFO -    💰 还款金额: 4500.00
2025-07-30 16:23:37,630 - INFO -    ⏰ 还款时间: 2025-07-23
2025-07-30 16:23:37,630 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:23:37,630 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:23:37,630 - INFO - 🎯 图片处理完成: 王春花4521-4500.jpg
2025-07-30 16:23:37,630 - INFO -    📋 最终结果 - 付款方: 【王春花】, 账户: ***8417, 来源: 【本人还款】
2025-07-30 16:23:37,631 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:23:37,631 - INFO - ✅ 通过姓名找到唯一匹配: 盖相宇 → debtor_no: 0b98a015305cc4a9600e5363dfaefa08
2025-07-30 16:23:37,631 - INFO - ✅ 通过还款对账明细找到debtor_no: 盖相宇 → 0b98a015305cc4a9600e5363dfaefa08 (姓名唯一匹配)
2025-07-30 16:23:37,632 - INFO - 📸 开始处理图片: 盖相宇5512-1450.jpg
2025-07-30 16:23:37,632 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:23:37,632 - INFO - 🔍 开始OCR识别图片: 盖相宇5512-1450.jpg
2025-07-30 16:23:37,632 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 盖相宇5512-1450.jpg
2025-07-30 16:23:57,803 - ERROR - ❌ 豆包AI处理异常: 'float' object has no attribute 'strip'
2025-07-30 16:23:57,803 - ERROR - ❌ OCR识别失败，所有字段保持为空
2025-07-30 16:23:57,803 - INFO - 🎯 图片处理完成: 盖相宇5512-1450.jpg
2025-07-30 16:23:57,803 - INFO -    📋 最终结果 - 付款方: 【】, 账户: ***, 来源: 【】
2025-07-30 16:23:57,804 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:23:57,804 - INFO - ✅ 通过姓名找到唯一匹配: 耿凤云 → debtor_no: 7ca0dc3415e25999388c2b8ba48e2dc2
2025-07-30 16:23:57,804 - INFO - ✅ 通过还款对账明细找到debtor_no: 耿凤云 → 7ca0dc3415e25999388c2b8ba48e2dc2 (姓名唯一匹配)
2025-07-30 16:23:57,804 - INFO - 📸 开始处理图片: 耿凤云0044-3000.jpg
2025-07-30 16:23:57,805 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:23:57,805 - INFO - 🔍 开始OCR识别图片: 耿凤云0044-3000.jpg
2025-07-30 16:23:57,805 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 耿凤云0044-3000.jpg
2025-07-30 16:24:13,937 - INFO - 🔄 使用文件名中的姓名: 耿凤云
2025-07-30 16:24:13,937 - INFO - ✅ 豆包AI分析成功: 👤耿凤云 💳***7082 💰3000.00 ⏰2025-07-24 14:11:01
2025-07-30 16:24:13,938 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:24:13,938 - INFO - 👤 付款方识别成功: 【耿凤云】
2025-07-30 16:24:13,938 - INFO - 💳 账户号码识别成功: ***7082
2025-07-30 16:24:13,938 - INFO - 💰 还款金额识别成功: 3000.00
2025-07-30 16:24:13,939 - INFO - ⏰ 还款时间识别成功: 2025-07-24 14:11:01 → 2025-07-24
2025-07-30 16:24:13,939 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:24:13,939 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:24:13,939 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:24:13,939 - INFO -    👤 还款人: 【耿凤云】
2025-07-30 16:24:13,940 - INFO -    💳 还款账号: ***7082
2025-07-30 16:24:13,940 - INFO -    💰 还款金额: 3000.00
2025-07-30 16:24:13,940 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:24:13,940 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:24:13,940 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:24:13,941 - INFO - 🎯 图片处理完成: 耿凤云0044-3000.jpg
2025-07-30 16:24:13,941 - INFO -    📋 最终结果 - 付款方: 【耿凤云】, 账户: ***7082, 来源: 【本人还款】
2025-07-30 16:24:13,941 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:24:13,942 - INFO - ✅ 通过姓名找到唯一匹配: 胡广海 → debtor_no: 6152d199172004935777279c22b15b1d
2025-07-30 16:24:13,942 - INFO - ✅ 通过还款对账明细找到debtor_no: 胡广海 → 6152d199172004935777279c22b15b1d (姓名唯一匹配)
2025-07-30 16:24:13,942 - INFO - 📸 开始处理图片: 胡广海1253-4000.jpg
2025-07-30 16:24:13,942 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:24:13,942 - INFO - 🔍 开始OCR识别图片: 胡广海1253-4000.jpg
2025-07-30 16:24:13,943 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 胡广海1253-4000.jpg
2025-07-30 16:24:26,584 - INFO - ✅ 豆包AI分析成功: 👤安忠丽 💳***8186 💰4000.00 ⏰2025-07-24 10:38:43
2025-07-30 16:24:26,584 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:24:26,584 - INFO - 👤 付款方识别成功: 【安忠丽】
2025-07-30 16:24:26,584 - INFO - 💳 账户号码识别成功: ***8186
2025-07-30 16:24:26,584 - INFO - 💰 还款金额识别成功: 4000.00
2025-07-30 16:24:26,585 - INFO - ⏰ 还款时间识别成功: 2025-07-24 10:38:43 → 2025-07-24
2025-07-30 16:24:26,585 - INFO - 🔄 还款来源: 他人代还 (付款人[安忠丽]与文件名[胡广海]不匹配)
2025-07-30 16:24:26,585 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:24:26,585 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.24 隆丰诉保\胡广海1253-4000.jpg
2025-07-30 16:24:26,586 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-30 16:24:26,586 - WARNING -    姓名匹配: OCR[安忠丽] vs 文件名[胡广海] = 0.00
2025-07-30 16:24:26,586 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-30 16:24:26,586 - WARNING -    金额匹配: OCR[4000.00] vs 文件名[4000] = 1.00
2025-07-30 16:24:26,586 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:24:26,587 - INFO -    👤 还款人: 【安忠丽】
2025-07-30 16:24:26,587 - INFO -    💳 还款账号: ***8186
2025-07-30 16:24:26,587 - INFO -    💰 还款金额: 4000.00
2025-07-30 16:24:26,587 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:24:26,587 - INFO -    🔄 还款来源: 【他人代还】
2025-07-30 16:24:26,587 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-30 16:24:26,588 - INFO - 🎯 图片处理完成: 胡广海1253-4000.jpg
2025-07-30 16:24:26,588 - INFO -    📋 最终结果 - 付款方: 【安忠丽】, 账户: ***8186, 来源: 【他人代还】
2025-07-30 16:24:26,588 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:24:26,588 - INFO - ✅ 通过姓名找到唯一匹配: 赵辉 → debtor_no: 00741a402b904a80ca696e2bf7df0982
2025-07-30 16:24:26,589 - INFO - ✅ 通过还款对账明细找到debtor_no: 赵辉 → 00741a402b904a80ca696e2bf7df0982 (姓名唯一匹配)
2025-07-30 16:24:26,589 - INFO - 📸 开始处理图片: 赵辉2346-10000.jpg
2025-07-30 16:24:26,589 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:24:26,589 - INFO - 🔍 开始OCR识别图片: 赵辉2346-10000.jpg
2025-07-30 16:24:26,589 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 赵辉2346-10000.jpg
2025-07-30 16:24:44,979 - INFO - ✅ 豆包AI分析成功: 👤赵辉 💳***2065 💰10000.00 ⏰2025-07-24 10:18:59
2025-07-30 16:24:44,979 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:24:44,979 - INFO - 👤 付款方识别成功: 【赵辉】
2025-07-30 16:24:44,979 - INFO - 💳 账户号码识别成功: ***2065
2025-07-30 16:24:44,980 - INFO - 💰 还款金额识别成功: 10000.00
2025-07-30 16:24:44,980 - INFO - ⏰ 还款时间识别成功: 2025-07-24 10:18:59 → 2025-07-24
2025-07-30 16:24:44,980 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:24:44,980 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:24:44,980 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:24:44,981 - INFO -    👤 还款人: 【赵辉】
2025-07-30 16:24:44,981 - INFO -    💳 还款账号: ***2065
2025-07-30 16:24:44,981 - INFO -    💰 还款金额: 10000.00
2025-07-30 16:24:44,981 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:24:44,982 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:24:44,982 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:24:44,982 - INFO - 🎯 图片处理完成: 赵辉2346-10000.jpg
2025-07-30 16:24:44,982 - INFO -    📋 最终结果 - 付款方: 【赵辉】, 账户: ***2065, 来源: 【本人还款】
2025-07-30 16:24:44,983 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:24:44,983 - INFO - ✅ 通过姓名找到唯一匹配: 陈东梅 → debtor_no: 0ba87a9f65195e03033fde8de9e6ef3a
2025-07-30 16:24:44,983 - INFO - ✅ 通过还款对账明细找到debtor_no: 陈东梅 → 0ba87a9f65195e03033fde8de9e6ef3a (姓名唯一匹配)
2025-07-30 16:24:44,983 - INFO - 📸 开始处理图片: 陈东梅0022-3000.jpg
2025-07-30 16:24:44,983 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:24:44,983 - INFO - 🔍 开始OCR识别图片: 陈东梅0022-3000.jpg
2025-07-30 16:24:44,984 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 陈东梅0022-3000.jpg
2025-07-30 16:25:25,752 - INFO - 🔄 使用文件名中的姓名: 陈东梅
2025-07-30 16:25:25,752 - INFO - ✅ 豆包AI分析成功: 👤陈东梅 💳***8276 💰3000.00 ⏰
2025-07-30 16:25:25,752 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:25:25,753 - INFO - 👤 付款方识别成功: 【陈东梅】
2025-07-30 16:25:25,753 - INFO - 💳 账户号码识别成功: ***8276
2025-07-30 16:25:25,753 - INFO - 💰 还款金额识别成功: 3000.00
2025-07-30 16:25:25,753 - WARNING - ⏰ OCR未识别到还款时间，保持为空
2025-07-30 16:25:25,754 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:25:25,754 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:25:25,754 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:25:25,754 - INFO -    👤 还款人: 【陈东梅】
2025-07-30 16:25:25,754 - INFO -    💳 还款账号: ***8276
2025-07-30 16:25:25,755 - INFO -    💰 还款金额: 3000.00
2025-07-30 16:25:25,755 - INFO -    ⏰ 还款时间: 
2025-07-30 16:25:25,755 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:25:25,755 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:25:25,756 - INFO - 🎯 图片处理完成: 陈东梅0022-3000.jpg
2025-07-30 16:25:25,756 - INFO -    📋 最终结果 - 付款方: 【陈东梅】, 账户: ***8276, 来源: 【本人还款】
2025-07-30 16:25:25,756 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-30 16:25:25,756 - INFO - ✅ 通过姓名找到唯一匹配: 高俊琴 → debtor_no: b0ef16780bf0dd3002fe22ae4f6cada5
2025-07-30 16:25:25,757 - INFO - ✅ 通过还款对账明细找到debtor_no: 高俊琴 → b0ef16780bf0dd3002fe22ae4f6cada5 (姓名唯一匹配)
2025-07-30 16:25:25,757 - INFO - 📸 开始处理图片: 高俊琴352X-15900.jpg
2025-07-30 16:25:25,757 - INFO - 🚀 启动OCR识别流程...
2025-07-30 16:25:25,757 - INFO - 🔍 开始OCR识别图片: 高俊琴352X-15900.jpg
2025-07-30 16:25:25,757 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 高俊琴352X-15900.jpg
2025-07-30 16:25:44,123 - INFO - 🔄 使用文件名中的姓名: 高俊琴
2025-07-30 16:25:44,123 - INFO - ✅ 豆包AI分析成功: 👤高俊琴 💳***0000 💰15900.00 ⏰2025-07-24 14:44:57
2025-07-30 16:25:44,123 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-30 16:25:44,123 - INFO - 👤 付款方识别成功: 【高俊琴】
2025-07-30 16:25:44,124 - INFO - 💳 账户号码识别成功: ***0000
2025-07-30 16:25:44,124 - INFO - 💰 还款金额识别成功: 15900.00
2025-07-30 16:25:44,124 - INFO - ⏰ 还款时间识别成功: 2025-07-24 14:44:57 → 2025-07-24
2025-07-30 16:25:44,124 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-30 16:25:44,124 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-30 16:25:44,125 - INFO - 📊 OCR结果应用完成:
2025-07-30 16:25:44,125 - INFO -    👤 还款人: 【高俊琴】
2025-07-30 16:25:44,125 - INFO -    💳 还款账号: ***0000
2025-07-30 16:25:44,125 - INFO -    💰 还款金额: 15900.00
2025-07-30 16:25:44,126 - INFO -    ⏰ 还款时间: 2025-07-24
2025-07-30 16:25:44,126 - INFO -    🔄 还款来源: 【本人还款】
2025-07-30 16:25:44,126 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-30 16:25:44,126 - INFO - 🎯 图片处理完成: 高俊琴352X-15900.jpg
2025-07-30 16:25:44,126 - INFO -    📋 最终结果 - 付款方: 【高俊琴】, 账户: ***0000, 来源: 【本人还款】
2025-07-30 16:25:44,127 - INFO - 📊 开始验证文件夹 '7.24 隆丰诉保' 的数据一致性...
2025-07-30 16:25:44,151 - ERROR - ❌ 文件夹 '7.24 隆丰诉保' 数据一致性验证失败!
2025-07-30 16:25:44,151 - ERROR -    Excel中的人员未在图片中找到: 司立民, 胡广海, 姜虹, 崔海丰, 李宁
2025-07-30 16:25:44,151 - ERROR -    图片中的人员在Excel中未找到: 康凯, 崔海兵, 姜敏, 司美丽, 安忠丽
2025-07-30 16:25:44,152 - INFO - 🔍 开始验证OCR识别结果与Excel对比...
2025-07-30 16:25:44,178 - INFO - 📋 读取还款对账明细文件: 还款对账明细（合作方用）(1).xlsx
2025-07-30 16:25:44,179 - WARNING - ❌ 还款人未在Excel中找到: 司美丽
2025-07-30 16:25:44,179 - WARNING - ❌ 还款人未在Excel中找到: 姜敏
2025-07-30 16:25:44,179 - WARNING - ❌ 还款人未在Excel中找到: 崔海兵
2025-07-30 16:25:44,179 - WARNING - ❌ 还款人未在Excel中找到: 康凯
2025-07-30 16:25:44,179 - WARNING - ❌ 还款人未在Excel中找到: 安忠丽
2025-07-30 16:25:44,180 - INFO - 📊 Excel验证完成 - 匹配: 11, 未匹配: 5
2025-07-30 16:25:44,180 - WARNING - ⚠️  Excel验证发现问题:
2025-07-30 16:25:44,180 - WARNING -    有 5 个还款人未在Excel中找到
2025-07-30 16:25:44,180 - INFO - 目录 7.24 隆丰诉保 处理完成，共处理 18 个图片
2025-07-30 16:25:44,192 - INFO - 写入主要处理结果: 18 条记录
2025-07-30 16:25:44,196 - INFO - 写入还款对账明细: 18 条记录
2025-07-30 16:25:44,197 - INFO - 🔍 开始执行三重比对分析...
2025-07-30 16:25:44,197 - INFO - 📊 处理结果明细包含 18 个不同债务人
2025-07-30 16:25:44,197 - INFO - 📊 还款对账明细包含 18 个不同债务人
2025-07-30 16:25:44,197 - INFO - 🔍 执行分析1：债务人姓名匹配分析
2025-07-30 16:25:44,198 - INFO - 🔍 执行分析2：还款金额匹配分析
2025-07-30 16:25:44,198 - INFO - 🔍 执行分析3：结清验证分析
2025-07-30 16:25:44,198 - INFO - ✅ 三重比对分析完成
2025-07-30 16:25:44,198 - INFO -    📊 统计结果: {'还款对账明细债务人数': 18, '处理结果明细债务人数': 18, '共同债务人数': 18, '仅在还款对账明细中': 0, '仅在处理结果明细中': 0, '金额不一致债务人数': 2, '需要关注的结清案例': 0}
2025-07-30 16:25:44,201 - INFO - 写入金额匹配分析: 18 条记录
2025-07-30 16:25:44,206 - INFO - 写入结清验证分析: 2 条记录
2025-07-30 16:25:44,207 - INFO - 写入比对分析摘要: 7 条记录
2025-07-30 16:25:44,208 - INFO - 开始生成人名统计，共有 18 条处理结果
2025-07-30 16:25:44,208 - INFO - 处理完成，共处理 1 个目录，生成 18 个人员统计
2025-07-30 16:25:44,208 - INFO - 处理的目录列表: ['7.24 隆丰诉保']
2025-07-30 16:25:44,640 - INFO - 最终生成 18 个人员统计记录
2025-07-30 16:25:44,643 - INFO - 写入人名统计信息: 18 条记录
2025-07-30 16:25:44,645 - INFO - 🎨 开始应用Excel格式设置...
2025-07-30 16:25:44,646 - INFO -    文件路径列索引: 2
2025-07-30 16:25:44,646 - INFO -    总行数: 19
2025-07-30 16:25:44,646 - INFO - 🔗 正在设置文件路径超链接...
2025-07-30 16:25:44,647 - INFO - ✅ 文件路径超链接设置完成，共设置 18 个超链接
2025-07-30 16:25:44,647 - INFO - 🎨 正在设置行颜色标记...
2025-07-30 16:25:44,647 - INFO - 🟡 应用黄色标识: OCR识别失败
2025-07-30 16:25:44,648 - INFO - 🟡 应用黄色标识: OCR识别失败
2025-07-30 16:25:44,649 - INFO - ✅ '处理结果明细'sheet格式设置完成
2025-07-30 16:25:44,649 - INFO - 🔴 正在设置结清验证状态列格式...
2025-07-30 16:25:44,649 - INFO - ✅ 结清验证状态列格式化完成：0 个单元格标红
2025-07-30 16:25:44,649 - INFO - ✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红
2025-07-30 16:25:44,650 - INFO - 📍 找到关键列 - 图片文件数量: 13, debtor_no: 9, 类型: 6
2025-07-30 16:25:44,650 - INFO - ✅ 还款对账明细格式化完成
2025-07-30 16:25:44,650 - INFO -    🔴 图片文件缺失标红: 0 行
2025-07-30 16:25:44,650 - INFO -    🟠 结清验证问题标橙: 0 行
2025-07-30 16:25:44,652 - INFO - 写入目录统计信息: 1 条记录
2025-07-30 16:25:44,653 - INFO - 写入处理摘要信息
2025-07-30 16:25:44,685 - INFO - 增强版Excel文件保存成功: 7.24 隆丰诉保_还款凭证解析_20250730_162544.xlsx
2025-07-30 16:25:44,686 - INFO - ✅ 成功生成Excel文件: 7.24 隆丰诉保_还款凭证解析_20250730_162544.xlsx
2025-07-30 16:25:44,686 - INFO - ✅ 子文件夹 7.24 隆丰诉保 处理完成，生成文件: 7.24 隆丰诉保_还款凭证解析_20250730_162544.xlsx
2025-07-30 16:25:44,686 - INFO - ================================================================================
2025-07-30 16:25:44,686 - INFO - 🎯 全部处理完成！
2025-07-30 16:25:44,687 - INFO - 📋 共处理 1 个子文件夹
2025-07-30 16:25:44,687 - INFO - 📄 成功生成 1 个Excel文件
2025-07-30 16:25:44,687 - INFO - 📄 生成的文件列表:
2025-07-30 16:25:44,687 - INFO -    - 7.24 隆丰诉保_还款凭证解析_20250730_162544.xlsx
2025-07-30 16:25:44,688 - INFO - 开始生成人名统计，共有 18 条处理结果
2025-07-30 16:25:44,689 - INFO - 处理完成，共处理 1 个目录，生成 18 个人员统计
2025-07-30 16:25:44,689 - INFO - 处理的目录列表: ['7.24 隆丰诉保']
2025-07-30 16:25:45,365 - INFO - 最终生成 18 个人员统计记录
2025-07-31 12:43:21,584 - INFO - 🤖 自动选择OCR引擎...
2025-07-31 12:43:21,584 - INFO - ✅ 检测到豆包Seed-1.6 API，优先使用（AI视觉理解，最优中文识别）
2025-07-31 12:43:21,584 - INFO - 🚀 开始初始化豆包Seed-1.6 OCR引擎...
2025-07-31 12:43:22,536 - INFO - ✅ 豆包Seed-1.6引擎初始化成功
2025-07-31 12:43:22,536 - INFO -    🎯 AI视觉理解，最优中文识别
2025-07-31 12:43:22,536 - INFO -    🇨🇳 专门针对中文场景优化
2025-07-31 12:43:22,537 - INFO -    ⚡ 国内服务，响应速度快
2025-07-31 12:43:22,537 - INFO -    💰 极低成本，高性价比
2025-07-31 12:43:22,538 - INFO - 🎯 当前OCR引擎: 豆包Seed-1.6 AI视觉理解 (最优中文识别)
2025-07-31 12:43:22,587 - INFO - DuckDB数据库连接成功: debtors.duckdb
2025-07-31 12:43:22,610 - INFO - 检测到Excel文件已更新，需要重新导入数据
2025-07-31 12:43:22,610 - INFO - 正在导入债务人数据到DuckDB数据库...
2025-07-31 12:43:22,616 - INFO - 已删除旧的数据表
2025-07-31 12:43:24,988 - INFO - DuckDB成功导入债务人数据：23,043 条记录
2025-07-31 12:43:24,989 - INFO - 正在创建数据库索引...
2025-07-31 12:43:25,077 - INFO - 数据库索引创建完成
2025-07-31 12:43:25,079 - INFO - 债务人数据库初始化成功：
2025-07-31 12:43:25,079 - INFO -   数据库类型: DuckDB
2025-07-31 12:43:25,080 - INFO -   总记录数: 23043
2025-07-31 12:43:25,080 - INFO -   有身份证号: 23043
2025-07-31 12:43:25,080 - INFO -   有合同号: 23043
2025-07-31 12:43:25,080 - INFO - 处理器初始化完成，OCR引擎: doubao
2025-07-31 12:43:25,085 - INFO - 开始处理文件夹: D:\民生4期回款
2025-07-31 12:43:25,085 - INFO - ================================================================================
2025-07-31 12:43:25,086 - INFO - 🗂️  开始处理子文件夹: 7.24 隆丰诉保
2025-07-31 12:43:25,087 - INFO - ✅ 目录 7.24 隆丰诉保 中Excel文件验证通过: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:43:25,087 - INFO - 目录 7.24 隆丰诉保 Excel验证成功
2025-07-31 12:43:25,087 - INFO - 🔍 扫描还款对账明细Excel: 7.24 隆丰诉保
2025-07-31 12:43:25,089 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:43:25,134 - INFO -    📋 Excel文件包含 21 行数据
2025-07-31 12:43:25,135 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号码', '还款金额': '现金回收', '类型': '还款类型（部分还款/结清）', '身份证号': '身份证号码'}
2025-07-31 12:43:25,136 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,136 - INFO -       债务人姓名: '王春花'
2025-07-31 12:43:25,136 - INFO -       合同号: '13523758.0'
2025-07-31 12:43:25,136 - INFO -       身份证号: '220283198204274521'
2025-07-31 12:43:25,136 - INFO - 🔍 使用身份证号查询: 220283198204274521
2025-07-31 12:43:25,137 - INFO -    数据库查询结果: found=True, debtor_no=e38f5990ec8479e843c316571d330a62
2025-07-31 12:43:25,137 - INFO - ✅ 通过身份证号找到债务人: e38f5990ec8479e843c316571d330a62
2025-07-31 12:43:25,138 - INFO -    ✅ 匹配成功: debtor_no=e38f5990ec8479e843c316571d330a62, 方式=身份证号
2025-07-31 12:43:25,140 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,140 - INFO -       债务人姓名: '姜虹'
2025-07-31 12:43:25,140 - INFO -       合同号: '398418.0'
2025-07-31 12:43:25,140 - INFO -       身份证号: '210282199305072626'
2025-07-31 12:43:25,140 - INFO - 🔍 使用身份证号查询: 210282199305072626
2025-07-31 12:43:25,142 - INFO -    数据库查询结果: found=True, debtor_no=e834c15c3a439f25d678e66e2a20ae7c
2025-07-31 12:43:25,142 - INFO - ✅ 通过身份证号找到债务人: e834c15c3a439f25d678e66e2a20ae7c
2025-07-31 12:43:25,142 - INFO -    ✅ 匹配成功: debtor_no=e834c15c3a439f25d678e66e2a20ae7c, 方式=身份证号
2025-07-31 12:43:25,143 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,143 - INFO -       债务人姓名: '张凤迪'
2025-07-31 12:43:25,144 - INFO -       合同号: '33511976.0'
2025-07-31 12:43:25,144 - INFO -       身份证号: '220122199411054835'
2025-07-31 12:43:25,144 - INFO - 🔍 使用身份证号查询: 220122199411054835
2025-07-31 12:43:25,145 - INFO -    数据库查询结果: found=True, debtor_no=f0abd85393bc4d8046991e57713f92fb
2025-07-31 12:43:25,145 - INFO - ✅ 通过身份证号找到债务人: f0abd85393bc4d8046991e57713f92fb
2025-07-31 12:43:25,145 - INFO -    ✅ 匹配成功: debtor_no=f0abd85393bc4d8046991e57713f92fb, 方式=身份证号
2025-07-31 12:43:25,147 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,147 - INFO -       债务人姓名: '赵辉'
2025-07-31 12:43:25,147 - INFO -       合同号: '12654982.0'
2025-07-31 12:43:25,147 - INFO -       身份证号: '210103196510072436'
2025-07-31 12:43:25,147 - INFO - 🔍 使用身份证号查询: 210103196510072436
2025-07-31 12:43:25,148 - INFO -    数据库查询结果: found=True, debtor_no=00741a402b904a80ca696e2bf7df0982
2025-07-31 12:43:25,148 - INFO - ✅ 通过身份证号找到债务人: 00741a402b904a80ca696e2bf7df0982
2025-07-31 12:43:25,149 - INFO -    ✅ 匹配成功: debtor_no=00741a402b904a80ca696e2bf7df0982, 方式=身份证号
2025-07-31 12:43:25,150 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,150 - INFO -       债务人姓名: '胡广海'
2025-07-31 12:43:25,150 - INFO -       合同号: '30590701.0'
2025-07-31 12:43:25,150 - INFO -       身份证号: '230183198906081253'
2025-07-31 12:43:25,150 - INFO - 🔍 使用身份证号查询: 230183198906081253
2025-07-31 12:43:25,152 - INFO -    数据库查询结果: found=True, debtor_no=6152d199172004935777279c22b15b1d
2025-07-31 12:43:25,152 - INFO - ✅ 通过身份证号找到债务人: 6152d199172004935777279c22b15b1d
2025-07-31 12:43:25,152 - INFO -    ✅ 匹配成功: debtor_no=6152d199172004935777279c22b15b1d, 方式=身份证号
2025-07-31 12:43:25,153 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,153 - INFO -       债务人姓名: '冯淑英'
2025-07-31 12:43:25,154 - INFO -       合同号: '12171338.0'
2025-07-31 12:43:25,154 - INFO -       身份证号: '210113196304253220'
2025-07-31 12:43:25,154 - INFO - 🔍 使用身份证号查询: 210113196304253220
2025-07-31 12:43:25,155 - INFO -    数据库查询结果: found=True, debtor_no=8f701bef9cd62e34551221b24ff30efa
2025-07-31 12:43:25,155 - INFO - ✅ 通过身份证号找到债务人: 8f701bef9cd62e34551221b24ff30efa
2025-07-31 12:43:25,155 - INFO -    ✅ 匹配成功: debtor_no=8f701bef9cd62e34551221b24ff30efa, 方式=身份证号
2025-07-31 12:43:25,156 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,156 - INFO -       债务人姓名: '司立民'
2025-07-31 12:43:25,156 - INFO -       合同号: '36930320.0'
2025-07-31 12:43:25,157 - INFO -       身份证号: '220222197205142718'
2025-07-31 12:43:25,157 - INFO - 🔍 使用身份证号查询: 220222197205142718
2025-07-31 12:43:25,158 - INFO -    数据库查询结果: found=True, debtor_no=23dcacdbbf322661d6edeaeea815ea35
2025-07-31 12:43:25,158 - INFO - ✅ 通过身份证号找到债务人: 23dcacdbbf322661d6edeaeea815ea35
2025-07-31 12:43:25,158 - INFO -    ✅ 匹配成功: debtor_no=23dcacdbbf322661d6edeaeea815ea35, 方式=身份证号
2025-07-31 12:43:25,159 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,159 - INFO -       债务人姓名: '盖相宇'
2025-07-31 12:43:25,160 - INFO -       合同号: '12546339.0'
2025-07-31 12:43:25,160 - INFO -       身份证号: '210902199108085512'
2025-07-31 12:43:25,160 - INFO - 🔍 使用身份证号查询: 210902199108085512
2025-07-31 12:43:25,161 - INFO -    数据库查询结果: found=True, debtor_no=0b98a015305cc4a9600e5363dfaefa08
2025-07-31 12:43:25,161 - INFO - ✅ 通过身份证号找到债务人: 0b98a015305cc4a9600e5363dfaefa08
2025-07-31 12:43:25,162 - INFO -    ✅ 匹配成功: debtor_no=0b98a015305cc4a9600e5363dfaefa08, 方式=身份证号
2025-07-31 12:43:25,163 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,163 - INFO -       债务人姓名: '王婷'
2025-07-31 12:43:25,163 - INFO -       合同号: '19877498.0'
2025-07-31 12:43:25,164 - INFO -       身份证号: '220322199101205643'
2025-07-31 12:43:25,164 - INFO - 🔍 使用身份证号查询: 220322199101205643
2025-07-31 12:43:25,165 - INFO -    数据库查询结果: found=True, debtor_no=68b5b1353c38ac8d223833c8c4435208
2025-07-31 12:43:25,165 - INFO - ✅ 通过身份证号找到债务人: 68b5b1353c38ac8d223833c8c4435208
2025-07-31 12:43:25,165 - INFO -    ✅ 匹配成功: debtor_no=68b5b1353c38ac8d223833c8c4435208, 方式=身份证号
2025-07-31 12:43:25,167 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,168 - INFO -       债务人姓名: '张忠波'
2025-07-31 12:43:25,168 - INFO -       合同号: '25240190.0'
2025-07-31 12:43:25,168 - INFO -       身份证号: '232101198203044090'
2025-07-31 12:43:25,169 - INFO - 🔍 使用身份证号查询: 232101198203044090
2025-07-31 12:43:25,170 - INFO -    数据库查询结果: found=True, debtor_no=652895ff70ed10588238464ee5ff35c1
2025-07-31 12:43:25,170 - INFO - ✅ 通过身份证号找到债务人: 652895ff70ed10588238464ee5ff35c1
2025-07-31 12:43:25,170 - INFO -    ✅ 匹配成功: debtor_no=652895ff70ed10588238464ee5ff35c1, 方式=身份证号
2025-07-31 12:43:25,172 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,172 - INFO -       债务人姓名: '李宁'
2025-07-31 12:43:25,172 - INFO -       合同号: '37673075.0'
2025-07-31 12:43:25,173 - INFO -       身份证号: '152224199410275515'
2025-07-31 12:43:25,173 - INFO - 🔍 使用身份证号查询: 152224199410275515
2025-07-31 12:43:25,174 - INFO -    数据库查询结果: found=True, debtor_no=480ce44d1053c64367cb0d241fd76c5e
2025-07-31 12:43:25,175 - INFO - ✅ 通过身份证号找到债务人: 480ce44d1053c64367cb0d241fd76c5e
2025-07-31 12:43:25,175 - INFO -    ✅ 匹配成功: debtor_no=480ce44d1053c64367cb0d241fd76c5e, 方式=身份证号
2025-07-31 12:43:25,177 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,177 - INFO -       债务人姓名: '陈东梅'
2025-07-31 12:43:25,177 - INFO -       合同号: '29246390.0'
2025-07-31 12:43:25,177 - INFO -       身份证号: '220821199505200022'
2025-07-31 12:43:25,177 - INFO - 🔍 使用身份证号查询: 220821199505200022
2025-07-31 12:43:25,178 - INFO -    数据库查询结果: found=True, debtor_no=0ba87a9f65195e03033fde8de9e6ef3a
2025-07-31 12:43:25,178 - INFO - ✅ 通过身份证号找到债务人: 0ba87a9f65195e03033fde8de9e6ef3a
2025-07-31 12:43:25,179 - INFO -    ✅ 匹配成功: debtor_no=0ba87a9f65195e03033fde8de9e6ef3a, 方式=身份证号
2025-07-31 12:43:25,180 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,180 - INFO -       债务人姓名: '耿凤云'
2025-07-31 12:43:25,180 - INFO -       合同号: '10318762.0'
2025-07-31 12:43:25,181 - INFO -       身份证号: '220102195109260044'
2025-07-31 12:43:25,181 - INFO - 🔍 使用身份证号查询: 220102195109260044
2025-07-31 12:43:25,182 - INFO -    数据库查询结果: found=True, debtor_no=7ca0dc3415e25999388c2b8ba48e2dc2
2025-07-31 12:43:25,182 - INFO - ✅ 通过身份证号找到债务人: 7ca0dc3415e25999388c2b8ba48e2dc2
2025-07-31 12:43:25,182 - INFO -    ✅ 匹配成功: debtor_no=7ca0dc3415e25999388c2b8ba48e2dc2, 方式=身份证号
2025-07-31 12:43:25,184 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,184 - INFO -       债务人姓名: '曲东升'
2025-07-31 12:43:25,184 - INFO -       合同号: '22744411.0'
2025-07-31 12:43:25,185 - INFO -       身份证号: '230105199104183018'
2025-07-31 12:43:25,185 - INFO - 🔍 使用身份证号查询: 230105199104183018
2025-07-31 12:43:25,186 - INFO -    数据库查询结果: found=True, debtor_no=0ad3c5be854347262cba3f5fa3145b56
2025-07-31 12:43:25,186 - INFO - ✅ 通过身份证号找到债务人: 0ad3c5be854347262cba3f5fa3145b56
2025-07-31 12:43:25,186 - INFO -    ✅ 匹配成功: debtor_no=0ad3c5be854347262cba3f5fa3145b56, 方式=身份证号
2025-07-31 12:43:25,187 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,187 - INFO -       债务人姓名: '高俊琴'
2025-07-31 12:43:25,187 - INFO -       合同号: '19303741.0'
2025-07-31 12:43:25,188 - INFO -       身份证号: '21120319640706352X'
2025-07-31 12:43:25,188 - INFO - 🔍 使用身份证号查询: 21120319640706352X
2025-07-31 12:43:25,189 - INFO -    数据库查询结果: found=True, debtor_no=b0ef16780bf0dd3002fe22ae4f6cada5
2025-07-31 12:43:25,189 - INFO - ✅ 通过身份证号找到债务人: b0ef16780bf0dd3002fe22ae4f6cada5
2025-07-31 12:43:25,189 - INFO -    ✅ 匹配成功: debtor_no=b0ef16780bf0dd3002fe22ae4f6cada5, 方式=身份证号
2025-07-31 12:43:25,190 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,190 - INFO -       债务人姓名: '张明兴'
2025-07-31 12:43:25,190 - INFO -       合同号: '33051411.0'
2025-07-31 12:43:25,191 - INFO -       身份证号: '220181198205273135'
2025-07-31 12:43:25,191 - INFO - 🔍 使用身份证号查询: 220181198205273135
2025-07-31 12:43:25,192 - INFO -    数据库查询结果: found=True, debtor_no=1de92547675f04ad45b3dfec791be3fe
2025-07-31 12:43:25,192 - INFO - ✅ 通过身份证号找到债务人: 1de92547675f04ad45b3dfec791be3fe
2025-07-31 12:43:25,192 - INFO -    ✅ 匹配成功: debtor_no=1de92547675f04ad45b3dfec791be3fe, 方式=身份证号
2025-07-31 12:43:25,193 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,193 - INFO -       债务人姓名: '崔海丰'
2025-07-31 12:43:25,194 - INFO -       合同号: '29186232.0'
2025-07-31 12:43:25,194 - INFO -       身份证号: '15222419850916751X'
2025-07-31 12:43:25,194 - INFO - 🔍 使用身份证号查询: 15222419850916751X
2025-07-31 12:43:25,195 - INFO -    数据库查询结果: found=True, debtor_no=0333ff8b79658fba92730433ee8c433e
2025-07-31 12:43:25,195 - INFO - ✅ 通过身份证号找到债务人: 0333ff8b79658fba92730433ee8c433e
2025-07-31 12:43:25,195 - INFO -    ✅ 匹配成功: debtor_no=0333ff8b79658fba92730433ee8c433e, 方式=身份证号
2025-07-31 12:43:25,196 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:43:25,197 - INFO -       债务人姓名: '刘洋'
2025-07-31 12:43:25,197 - INFO -       合同号: '32406040.0'
2025-07-31 12:43:25,197 - INFO -       身份证号: '230407199407290020'
2025-07-31 12:43:25,198 - INFO - 🔍 使用身份证号查询: 230407199407290020
2025-07-31 12:43:25,199 - INFO -    数据库查询结果: found=True, debtor_no=ddfe98c625a3137623b23a17bd675746
2025-07-31 12:43:25,199 - INFO - ✅ 通过身份证号找到债务人: ddfe98c625a3137623b23a17bd675746
2025-07-31 12:43:25,199 - INFO -    ✅ 匹配成功: debtor_no=ddfe98c625a3137623b23a17bd675746, 方式=身份证号
2025-07-31 12:43:25,201 - INFO -    ✅ 成功处理 18 条对账明细记录
2025-07-31 12:43:25,201 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:43:25,202 - INFO - 🗂️ 扫描还款对账明细: 18 条记录
2025-07-31 12:43:25,202 - INFO - 处理目录: D:\民生4期回款\7.24 隆丰诉保
2025-07-31 12:43:25,203 - INFO - 🔍 扫描还款对账明细Excel: 7.24 隆丰诉保
2025-07-31 12:43:25,204 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:43:25,204 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:43:25,216 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:43:25,216 - INFO - ✅ 通过姓名找到唯一匹配: 冯淑英 → debtor_no: 8f701bef9cd62e34551221b24ff30efa
2025-07-31 12:43:25,217 - INFO - ✅ 通过还款对账明细找到debtor_no: 冯淑英 → 8f701bef9cd62e34551221b24ff30efa (姓名唯一匹配)
2025-07-31 12:43:25,217 - INFO - 📸 开始处理图片: 冯淑英3220-6000.jpg
2025-07-31 12:43:25,217 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:43:25,217 - INFO - 🔍 开始OCR识别图片: 冯淑英3220-6000.jpg
2025-07-31 12:43:25,217 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 冯淑英3220-6000.jpg
2025-07-31 12:44:01,946 - INFO - 🤖 自动选择OCR引擎...
2025-07-31 12:44:01,946 - INFO - ✅ 检测到豆包Seed-1.6 API，优先使用（AI视觉理解，最优中文识别）
2025-07-31 12:44:01,947 - INFO - 🚀 开始初始化豆包Seed-1.6 OCR引擎...
2025-07-31 12:44:02,730 - INFO - ✅ 豆包Seed-1.6引擎初始化成功
2025-07-31 12:44:02,731 - INFO -    🎯 AI视觉理解，最优中文识别
2025-07-31 12:44:02,731 - INFO -    🇨🇳 专门针对中文场景优化
2025-07-31 12:44:02,731 - INFO -    ⚡ 国内服务，响应速度快
2025-07-31 12:44:02,731 - INFO -    💰 极低成本，高性价比
2025-07-31 12:44:02,731 - INFO - 🎯 当前OCR引擎: 豆包Seed-1.6 AI视觉理解 (最优中文识别)
2025-07-31 12:44:02,749 - INFO - DuckDB数据库连接成功: debtors.duckdb
2025-07-31 12:44:02,768 - INFO - 正在导入债务人数据到DuckDB数据库...
2025-07-31 12:44:03,190 - INFO - DuckDB成功导入债务人数据：23,043 条记录
2025-07-31 12:44:03,190 - INFO - 正在创建数据库索引...
2025-07-31 12:44:03,274 - INFO - 数据库索引创建完成
2025-07-31 12:44:03,276 - INFO - 债务人数据库初始化成功：
2025-07-31 12:44:03,277 - INFO -   数据库类型: DuckDB
2025-07-31 12:44:03,277 - INFO -   总记录数: 23043
2025-07-31 12:44:03,277 - INFO -   有身份证号: 23043
2025-07-31 12:44:03,277 - INFO -   有合同号: 23043
2025-07-31 12:44:03,277 - INFO - 处理器初始化完成，OCR引擎: doubao
2025-07-31 12:44:03,280 - INFO - 开始处理文件夹: D:\民生4期回款
2025-07-31 12:44:03,281 - INFO - ================================================================================
2025-07-31 12:44:03,281 - INFO - 🗂️  开始处理子文件夹: 7.24 隆丰诉保
2025-07-31 12:44:03,281 - INFO - ✅ 目录 7.24 隆丰诉保 中Excel文件验证通过: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:44:03,282 - INFO - 目录 7.24 隆丰诉保 Excel验证成功
2025-07-31 12:44:03,282 - INFO - 🔍 扫描还款对账明细Excel: 7.24 隆丰诉保
2025-07-31 12:44:03,283 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:44:03,312 - INFO -    📋 Excel文件包含 21 行数据
2025-07-31 12:44:03,312 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号码', '还款金额': '现金回收', '类型': '还款类型（部分还款/结清）', '身份证号': '身份证号码'}
2025-07-31 12:44:03,313 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,313 - INFO -       债务人姓名: '王春花'
2025-07-31 12:44:03,313 - INFO -       合同号: '13523758.0'
2025-07-31 12:44:03,313 - INFO -       身份证号: '220283198204274521'
2025-07-31 12:44:03,313 - INFO - 🔍 使用身份证号查询: 220283198204274521
2025-07-31 12:44:03,314 - INFO -    数据库查询结果: found=True, debtor_no=e38f5990ec8479e843c316571d330a62
2025-07-31 12:44:03,314 - INFO - ✅ 通过身份证号找到债务人: e38f5990ec8479e843c316571d330a62
2025-07-31 12:44:03,315 - INFO -    ✅ 匹配成功: debtor_no=e38f5990ec8479e843c316571d330a62, 方式=身份证号
2025-07-31 12:44:03,316 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,316 - INFO -       债务人姓名: '姜虹'
2025-07-31 12:44:03,316 - INFO -       合同号: '398418.0'
2025-07-31 12:44:03,317 - INFO -       身份证号: '210282199305072626'
2025-07-31 12:44:03,317 - INFO - 🔍 使用身份证号查询: 210282199305072626
2025-07-31 12:44:03,318 - INFO -    数据库查询结果: found=True, debtor_no=e834c15c3a439f25d678e66e2a20ae7c
2025-07-31 12:44:03,318 - INFO - ✅ 通过身份证号找到债务人: e834c15c3a439f25d678e66e2a20ae7c
2025-07-31 12:44:03,319 - INFO -    ✅ 匹配成功: debtor_no=e834c15c3a439f25d678e66e2a20ae7c, 方式=身份证号
2025-07-31 12:44:03,319 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,320 - INFO -       债务人姓名: '张凤迪'
2025-07-31 12:44:03,320 - INFO -       合同号: '33511976.0'
2025-07-31 12:44:03,320 - INFO -       身份证号: '220122199411054835'
2025-07-31 12:44:03,320 - INFO - 🔍 使用身份证号查询: 220122199411054835
2025-07-31 12:44:03,321 - INFO -    数据库查询结果: found=True, debtor_no=f0abd85393bc4d8046991e57713f92fb
2025-07-31 12:44:03,321 - INFO - ✅ 通过身份证号找到债务人: f0abd85393bc4d8046991e57713f92fb
2025-07-31 12:44:03,321 - INFO -    ✅ 匹配成功: debtor_no=f0abd85393bc4d8046991e57713f92fb, 方式=身份证号
2025-07-31 12:44:03,322 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,323 - INFO -       债务人姓名: '赵辉'
2025-07-31 12:44:03,323 - INFO -       合同号: '12654982.0'
2025-07-31 12:44:03,323 - INFO -       身份证号: '210103196510072436'
2025-07-31 12:44:03,323 - INFO - 🔍 使用身份证号查询: 210103196510072436
2025-07-31 12:44:03,324 - INFO -    数据库查询结果: found=True, debtor_no=00741a402b904a80ca696e2bf7df0982
2025-07-31 12:44:03,324 - INFO - ✅ 通过身份证号找到债务人: 00741a402b904a80ca696e2bf7df0982
2025-07-31 12:44:03,325 - INFO -    ✅ 匹配成功: debtor_no=00741a402b904a80ca696e2bf7df0982, 方式=身份证号
2025-07-31 12:44:03,326 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,326 - INFO -       债务人姓名: '胡广海'
2025-07-31 12:44:03,326 - INFO -       合同号: '30590701.0'
2025-07-31 12:44:03,326 - INFO -       身份证号: '230183198906081253'
2025-07-31 12:44:03,326 - INFO - 🔍 使用身份证号查询: 230183198906081253
2025-07-31 12:44:03,327 - INFO -    数据库查询结果: found=True, debtor_no=6152d199172004935777279c22b15b1d
2025-07-31 12:44:03,327 - INFO - ✅ 通过身份证号找到债务人: 6152d199172004935777279c22b15b1d
2025-07-31 12:44:03,328 - INFO -    ✅ 匹配成功: debtor_no=6152d199172004935777279c22b15b1d, 方式=身份证号
2025-07-31 12:44:03,329 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,329 - INFO -       债务人姓名: '冯淑英'
2025-07-31 12:44:03,329 - INFO -       合同号: '12171338.0'
2025-07-31 12:44:03,329 - INFO -       身份证号: '210113196304253220'
2025-07-31 12:44:03,329 - INFO - 🔍 使用身份证号查询: 210113196304253220
2025-07-31 12:44:03,330 - INFO -    数据库查询结果: found=True, debtor_no=8f701bef9cd62e34551221b24ff30efa
2025-07-31 12:44:03,330 - INFO - ✅ 通过身份证号找到债务人: 8f701bef9cd62e34551221b24ff30efa
2025-07-31 12:44:03,331 - INFO -    ✅ 匹配成功: debtor_no=8f701bef9cd62e34551221b24ff30efa, 方式=身份证号
2025-07-31 12:44:03,332 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,366 - INFO -       债务人姓名: '司立民'
2025-07-31 12:44:03,368 - INFO -       合同号: '36930320.0'
2025-07-31 12:44:03,374 - INFO -       身份证号: '220222197205142718'
2025-07-31 12:44:03,374 - INFO - 🔍 使用身份证号查询: 220222197205142718
2025-07-31 12:44:03,375 - INFO -    数据库查询结果: found=True, debtor_no=23dcacdbbf322661d6edeaeea815ea35
2025-07-31 12:44:03,375 - INFO - ✅ 通过身份证号找到债务人: 23dcacdbbf322661d6edeaeea815ea35
2025-07-31 12:44:03,376 - INFO -    ✅ 匹配成功: debtor_no=23dcacdbbf322661d6edeaeea815ea35, 方式=身份证号
2025-07-31 12:44:03,377 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,377 - INFO -       债务人姓名: '盖相宇'
2025-07-31 12:44:03,377 - INFO -       合同号: '12546339.0'
2025-07-31 12:44:03,377 - INFO -       身份证号: '210902199108085512'
2025-07-31 12:44:03,378 - INFO - 🔍 使用身份证号查询: 210902199108085512
2025-07-31 12:44:03,379 - INFO -    数据库查询结果: found=True, debtor_no=0b98a015305cc4a9600e5363dfaefa08
2025-07-31 12:44:03,379 - INFO - ✅ 通过身份证号找到债务人: 0b98a015305cc4a9600e5363dfaefa08
2025-07-31 12:44:03,379 - INFO -    ✅ 匹配成功: debtor_no=0b98a015305cc4a9600e5363dfaefa08, 方式=身份证号
2025-07-31 12:44:03,381 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,381 - INFO -       债务人姓名: '王婷'
2025-07-31 12:44:03,382 - INFO -       合同号: '19877498.0'
2025-07-31 12:44:03,382 - INFO -       身份证号: '220322199101205643'
2025-07-31 12:44:03,382 - INFO - 🔍 使用身份证号查询: 220322199101205643
2025-07-31 12:44:03,383 - INFO -    数据库查询结果: found=True, debtor_no=68b5b1353c38ac8d223833c8c4435208
2025-07-31 12:44:03,383 - INFO - ✅ 通过身份证号找到债务人: 68b5b1353c38ac8d223833c8c4435208
2025-07-31 12:44:03,383 - INFO -    ✅ 匹配成功: debtor_no=68b5b1353c38ac8d223833c8c4435208, 方式=身份证号
2025-07-31 12:44:03,384 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,385 - INFO -       债务人姓名: '张忠波'
2025-07-31 12:44:03,385 - INFO -       合同号: '25240190.0'
2025-07-31 12:44:03,385 - INFO -       身份证号: '232101198203044090'
2025-07-31 12:44:03,385 - INFO - 🔍 使用身份证号查询: 232101198203044090
2025-07-31 12:44:03,386 - INFO -    数据库查询结果: found=True, debtor_no=652895ff70ed10588238464ee5ff35c1
2025-07-31 12:44:03,386 - INFO - ✅ 通过身份证号找到债务人: 652895ff70ed10588238464ee5ff35c1
2025-07-31 12:44:03,386 - INFO -    ✅ 匹配成功: debtor_no=652895ff70ed10588238464ee5ff35c1, 方式=身份证号
2025-07-31 12:44:03,387 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,387 - INFO -       债务人姓名: '李宁'
2025-07-31 12:44:03,387 - INFO -       合同号: '37673075.0'
2025-07-31 12:44:03,388 - INFO -       身份证号: '152224199410275515'
2025-07-31 12:44:03,388 - INFO - 🔍 使用身份证号查询: 152224199410275515
2025-07-31 12:44:03,389 - INFO -    数据库查询结果: found=True, debtor_no=480ce44d1053c64367cb0d241fd76c5e
2025-07-31 12:44:03,389 - INFO - ✅ 通过身份证号找到债务人: 480ce44d1053c64367cb0d241fd76c5e
2025-07-31 12:44:03,389 - INFO -    ✅ 匹配成功: debtor_no=480ce44d1053c64367cb0d241fd76c5e, 方式=身份证号
2025-07-31 12:44:03,391 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,391 - INFO -       债务人姓名: '陈东梅'
2025-07-31 12:44:03,391 - INFO -       合同号: '29246390.0'
2025-07-31 12:44:03,391 - INFO -       身份证号: '220821199505200022'
2025-07-31 12:44:03,391 - INFO - 🔍 使用身份证号查询: 220821199505200022
2025-07-31 12:44:03,392 - INFO -    数据库查询结果: found=True, debtor_no=0ba87a9f65195e03033fde8de9e6ef3a
2025-07-31 12:44:03,392 - INFO - ✅ 通过身份证号找到债务人: 0ba87a9f65195e03033fde8de9e6ef3a
2025-07-31 12:44:03,392 - INFO -    ✅ 匹配成功: debtor_no=0ba87a9f65195e03033fde8de9e6ef3a, 方式=身份证号
2025-07-31 12:44:03,393 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,393 - INFO -       债务人姓名: '耿凤云'
2025-07-31 12:44:03,394 - INFO -       合同号: '10318762.0'
2025-07-31 12:44:03,394 - INFO -       身份证号: '220102195109260044'
2025-07-31 12:44:03,394 - INFO - 🔍 使用身份证号查询: 220102195109260044
2025-07-31 12:44:03,395 - INFO -    数据库查询结果: found=True, debtor_no=7ca0dc3415e25999388c2b8ba48e2dc2
2025-07-31 12:44:03,395 - INFO - ✅ 通过身份证号找到债务人: 7ca0dc3415e25999388c2b8ba48e2dc2
2025-07-31 12:44:03,395 - INFO -    ✅ 匹配成功: debtor_no=7ca0dc3415e25999388c2b8ba48e2dc2, 方式=身份证号
2025-07-31 12:44:03,396 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,397 - INFO -       债务人姓名: '曲东升'
2025-07-31 12:44:03,397 - INFO -       合同号: '22744411.0'
2025-07-31 12:44:03,397 - INFO -       身份证号: '230105199104183018'
2025-07-31 12:44:03,397 - INFO - 🔍 使用身份证号查询: 230105199104183018
2025-07-31 12:44:03,399 - INFO -    数据库查询结果: found=True, debtor_no=0ad3c5be854347262cba3f5fa3145b56
2025-07-31 12:44:03,399 - INFO - ✅ 通过身份证号找到债务人: 0ad3c5be854347262cba3f5fa3145b56
2025-07-31 12:44:03,400 - INFO -    ✅ 匹配成功: debtor_no=0ad3c5be854347262cba3f5fa3145b56, 方式=身份证号
2025-07-31 12:44:03,401 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,401 - INFO -       债务人姓名: '高俊琴'
2025-07-31 12:44:03,401 - INFO -       合同号: '19303741.0'
2025-07-31 12:44:03,401 - INFO -       身份证号: '21120319640706352X'
2025-07-31 12:44:03,402 - INFO - 🔍 使用身份证号查询: 21120319640706352X
2025-07-31 12:44:03,403 - INFO -    数据库查询结果: found=True, debtor_no=b0ef16780bf0dd3002fe22ae4f6cada5
2025-07-31 12:44:03,403 - INFO - ✅ 通过身份证号找到债务人: b0ef16780bf0dd3002fe22ae4f6cada5
2025-07-31 12:44:03,403 - INFO -    ✅ 匹配成功: debtor_no=b0ef16780bf0dd3002fe22ae4f6cada5, 方式=身份证号
2025-07-31 12:44:03,404 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,405 - INFO -       债务人姓名: '张明兴'
2025-07-31 12:44:03,405 - INFO -       合同号: '33051411.0'
2025-07-31 12:44:03,405 - INFO -       身份证号: '220181198205273135'
2025-07-31 12:44:03,406 - INFO - 🔍 使用身份证号查询: 220181198205273135
2025-07-31 12:44:03,407 - INFO -    数据库查询结果: found=True, debtor_no=1de92547675f04ad45b3dfec791be3fe
2025-07-31 12:44:03,407 - INFO - ✅ 通过身份证号找到债务人: 1de92547675f04ad45b3dfec791be3fe
2025-07-31 12:44:03,407 - INFO -    ✅ 匹配成功: debtor_no=1de92547675f04ad45b3dfec791be3fe, 方式=身份证号
2025-07-31 12:44:03,408 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,408 - INFO -       债务人姓名: '崔海丰'
2025-07-31 12:44:03,409 - INFO -       合同号: '29186232.0'
2025-07-31 12:44:03,409 - INFO -       身份证号: '15222419850916751X'
2025-07-31 12:44:03,409 - INFO - 🔍 使用身份证号查询: 15222419850916751X
2025-07-31 12:44:03,410 - INFO -    数据库查询结果: found=True, debtor_no=0333ff8b79658fba92730433ee8c433e
2025-07-31 12:44:03,410 - INFO - ✅ 通过身份证号找到债务人: 0333ff8b79658fba92730433ee8c433e
2025-07-31 12:44:03,411 - INFO -    ✅ 匹配成功: debtor_no=0333ff8b79658fba92730433ee8c433e, 方式=身份证号
2025-07-31 12:44:03,412 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:44:03,412 - INFO -       债务人姓名: '刘洋'
2025-07-31 12:44:03,413 - INFO -       合同号: '32406040.0'
2025-07-31 12:44:03,413 - INFO -       身份证号: '230407199407290020'
2025-07-31 12:44:03,413 - INFO - 🔍 使用身份证号查询: 230407199407290020
2025-07-31 12:44:03,415 - INFO -    数据库查询结果: found=True, debtor_no=ddfe98c625a3137623b23a17bd675746
2025-07-31 12:44:03,415 - INFO - ✅ 通过身份证号找到债务人: ddfe98c625a3137623b23a17bd675746
2025-07-31 12:44:03,415 - INFO -    ✅ 匹配成功: debtor_no=ddfe98c625a3137623b23a17bd675746, 方式=身份证号
2025-07-31 12:44:03,416 - INFO -    ✅ 成功处理 18 条对账明细记录
2025-07-31 12:44:03,417 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:44:03,417 - INFO - 🗂️ 扫描还款对账明细: 18 条记录
2025-07-31 12:44:03,419 - INFO - 处理目录: D:\民生4期回款\7.24 隆丰诉保
2025-07-31 12:44:03,420 - INFO - 🔍 扫描还款对账明细Excel: 7.24 隆丰诉保
2025-07-31 12:44:03,424 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:44:03,424 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:44:03,431 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:44:03,431 - INFO - ✅ 通过姓名找到唯一匹配: 冯淑英 → debtor_no: 8f701bef9cd62e34551221b24ff30efa
2025-07-31 12:44:03,432 - INFO - ✅ 通过还款对账明细找到debtor_no: 冯淑英 → 8f701bef9cd62e34551221b24ff30efa (姓名唯一匹配)
2025-07-31 12:44:03,432 - INFO - 📸 开始处理图片: 冯淑英3220-6000.jpg
2025-07-31 12:44:03,432 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:44:03,432 - INFO - 🔍 开始OCR识别图片: 冯淑英3220-6000.jpg
2025-07-31 12:44:03,432 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 冯淑英3220-6000.jpg
2025-07-31 12:46:30,634 - INFO - 🤖 自动选择OCR引擎...
2025-07-31 12:46:30,634 - INFO - ✅ 检测到豆包Seed-1.6 API，优先使用（AI视觉理解，最优中文识别）
2025-07-31 12:46:30,634 - INFO - 🚀 开始初始化豆包Seed-1.6 OCR引擎...
2025-07-31 12:46:31,405 - INFO - ✅ 豆包Seed-1.6引擎初始化成功
2025-07-31 12:46:31,406 - INFO -    🎯 AI视觉理解，最优中文识别
2025-07-31 12:46:31,406 - INFO -    🇨🇳 专门针对中文场景优化
2025-07-31 12:46:31,406 - INFO -    ⚡ 国内服务，响应速度快
2025-07-31 12:46:31,406 - INFO -    💰 极低成本，高性价比
2025-07-31 12:46:31,406 - INFO - 🎯 当前OCR引擎: 豆包Seed-1.6 AI视觉理解 (最优中文识别)
2025-07-31 12:46:31,420 - INFO - DuckDB数据库连接成功: debtors.duckdb
2025-07-31 12:46:31,435 - INFO - 使用现有DuckDB数据：23,043 条记录
2025-07-31 12:46:31,438 - INFO - 债务人数据库初始化成功：
2025-07-31 12:46:31,438 - INFO -   数据库类型: DuckDB
2025-07-31 12:46:31,439 - INFO -   总记录数: 23043
2025-07-31 12:46:31,439 - INFO -   有身份证号: 23043
2025-07-31 12:46:31,439 - INFO -   有合同号: 23043
2025-07-31 12:46:31,439 - INFO - 处理器初始化完成，OCR引擎: doubao
2025-07-31 12:46:31,444 - INFO - 开始处理文件夹: D:\民生4期回款
2025-07-31 12:46:31,444 - INFO - ================================================================================
2025-07-31 12:46:31,444 - INFO - 🗂️  开始处理子文件夹: 7.29 隆丰电催
2025-07-31 12:46:31,445 - INFO - ✅ 目录 7.29 隆丰电催 中Excel文件验证通过: 还款对账明细（合作方用）.xlsx
2025-07-31 12:46:31,445 - INFO - 目录 7.29 隆丰电催 Excel验证成功
2025-07-31 12:46:31,445 - INFO - 🔍 扫描还款对账明细Excel: 7.29 隆丰电催
2025-07-31 12:46:31,447 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:46:31,475 - INFO -    📋 Excel文件包含 15 行数据
2025-07-31 12:46:31,476 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号', '还款金额': '还款金额', '类型': '还款类型（部分还款/结清）'}
2025-07-31 12:46:31,476 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,477 - INFO -       债务人姓名: '董有森'
2025-07-31 12:46:31,477 - INFO -       合同号: '10036678'
2025-07-31 12:46:31,477 - INFO -       身份证号: ''
2025-07-31 12:46:31,477 - INFO - 🔍 使用合同号查询: 10036678
2025-07-31 12:46:31,481 - INFO -    数据库查询结果: found=True, debtor_no=89de5c088c0f1a31f42649ad0087a33a
2025-07-31 12:46:31,481 - INFO - ✅ 通过合同号找到债务人: 89de5c088c0f1a31f42649ad0087a33a
2025-07-31 12:46:31,482 - INFO -    ✅ 匹配成功: debtor_no=89de5c088c0f1a31f42649ad0087a33a, 方式=合同号
2025-07-31 12:46:31,484 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,484 - INFO -       债务人姓名: '龚莹莹'
2025-07-31 12:46:31,484 - INFO -       合同号: '18460403'
2025-07-31 12:46:31,484 - INFO -       身份证号: ''
2025-07-31 12:46:31,485 - INFO - 🔍 使用合同号查询: 18460403
2025-07-31 12:46:31,488 - INFO -    数据库查询结果: found=True, debtor_no=91be375a708772449c67d75c109858cf
2025-07-31 12:46:31,489 - INFO - ✅ 通过合同号找到债务人: 91be375a708772449c67d75c109858cf
2025-07-31 12:46:31,489 - INFO -    ✅ 匹配成功: debtor_no=91be375a708772449c67d75c109858cf, 方式=合同号
2025-07-31 12:46:31,490 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,490 - INFO -       债务人姓名: '郝宇'
2025-07-31 12:46:31,491 - INFO -       合同号: '8371958'
2025-07-31 12:46:31,491 - INFO -       身份证号: ''
2025-07-31 12:46:31,491 - INFO - 🔍 使用合同号查询: 8371958
2025-07-31 12:46:31,494 - INFO -    数据库查询结果: found=True, debtor_no=d2255984333b7ae1a349dcc2d8613455
2025-07-31 12:46:31,495 - INFO - ✅ 通过合同号找到债务人: d2255984333b7ae1a349dcc2d8613455
2025-07-31 12:46:31,495 - INFO -    ✅ 匹配成功: debtor_no=d2255984333b7ae1a349dcc2d8613455, 方式=合同号
2025-07-31 12:46:31,496 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,497 - INFO -       债务人姓名: '李丹丹'
2025-07-31 12:46:31,497 - INFO -       合同号: '20442867'
2025-07-31 12:46:31,497 - INFO -       身份证号: ''
2025-07-31 12:46:31,497 - INFO - 🔍 使用合同号查询: 20442867
2025-07-31 12:46:31,499 - INFO -    数据库查询结果: found=True, debtor_no=81b0cf17d283ab4c5d4241ae515e980d
2025-07-31 12:46:31,499 - INFO - ✅ 通过合同号找到债务人: 81b0cf17d283ab4c5d4241ae515e980d
2025-07-31 12:46:31,500 - INFO -    ✅ 匹配成功: debtor_no=81b0cf17d283ab4c5d4241ae515e980d, 方式=合同号
2025-07-31 12:46:31,501 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,501 - INFO -       债务人姓名: '宋晶晶'
2025-07-31 12:46:31,501 - INFO -       合同号: '28036883'
2025-07-31 12:46:31,501 - INFO -       身份证号: ''
2025-07-31 12:46:31,502 - INFO - 🔍 使用合同号查询: 28036883
2025-07-31 12:46:31,505 - INFO -    数据库查询结果: found=True, debtor_no=4747d358ace295adee618427b659e998
2025-07-31 12:46:31,506 - INFO - ✅ 通过合同号找到债务人: 4747d358ace295adee618427b659e998
2025-07-31 12:46:31,506 - INFO -    ✅ 匹配成功: debtor_no=4747d358ace295adee618427b659e998, 方式=合同号
2025-07-31 12:46:31,507 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,507 - INFO -       债务人姓名: '王雪'
2025-07-31 12:46:31,507 - INFO -       合同号: '10118841'
2025-07-31 12:46:31,508 - INFO -       身份证号: ''
2025-07-31 12:46:31,508 - INFO - 🔍 使用合同号查询: 10118841
2025-07-31 12:46:31,513 - INFO -    数据库查询结果: found=True, debtor_no=337a5b8d71fed21eba5d21e41affa7f0
2025-07-31 12:46:31,513 - INFO - ✅ 通过合同号找到债务人: 337a5b8d71fed21eba5d21e41affa7f0
2025-07-31 12:46:31,513 - INFO -    ✅ 匹配成功: debtor_no=337a5b8d71fed21eba5d21e41affa7f0, 方式=合同号
2025-07-31 12:46:31,514 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,514 - INFO -       债务人姓名: '邢阳'
2025-07-31 12:46:31,514 - INFO -       合同号: '38693028'
2025-07-31 12:46:31,514 - INFO -       身份证号: ''
2025-07-31 12:46:31,515 - INFO - 🔍 使用合同号查询: 38693028
2025-07-31 12:46:31,518 - INFO -    数据库查询结果: found=True, debtor_no=c11c910d88ebfa123b7ee9fbcf38a787
2025-07-31 12:46:31,518 - INFO - ✅ 通过合同号找到债务人: c11c910d88ebfa123b7ee9fbcf38a787
2025-07-31 12:46:31,519 - INFO -    ✅ 匹配成功: debtor_no=c11c910d88ebfa123b7ee9fbcf38a787, 方式=合同号
2025-07-31 12:46:31,520 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,520 - INFO -       债务人姓名: '袁双'
2025-07-31 12:46:31,521 - INFO -       合同号: '16234714'
2025-07-31 12:46:31,521 - INFO -       身份证号: ''
2025-07-31 12:46:31,521 - INFO - 🔍 使用合同号查询: 16234714
2025-07-31 12:46:31,523 - INFO -    数据库查询结果: found=True, debtor_no=95dea243d44e4087097fadd9264a869c
2025-07-31 12:46:31,523 - INFO - ✅ 通过合同号找到债务人: 95dea243d44e4087097fadd9264a869c
2025-07-31 12:46:31,523 - INFO -    ✅ 匹配成功: debtor_no=95dea243d44e4087097fadd9264a869c, 方式=合同号
2025-07-31 12:46:31,525 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,525 - INFO -       债务人姓名: '于长军'
2025-07-31 12:46:31,525 - INFO -       合同号: '11905833'
2025-07-31 12:46:31,525 - INFO -       身份证号: ''
2025-07-31 12:46:31,526 - INFO - 🔍 使用合同号查询: 11905833
2025-07-31 12:46:31,529 - INFO -    数据库查询结果: found=True, debtor_no=a9503193e8652de05772253306599f7c
2025-07-31 12:46:31,529 - INFO - ✅ 通过合同号找到债务人: a9503193e8652de05772253306599f7c
2025-07-31 12:46:31,529 - INFO -    ✅ 匹配成功: debtor_no=a9503193e8652de05772253306599f7c, 方式=合同号
2025-07-31 12:46:31,530 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,530 - INFO -       债务人姓名: '李士峰'
2025-07-31 12:46:31,531 - INFO -       合同号: '26803940'
2025-07-31 12:46:31,531 - INFO -       身份证号: ''
2025-07-31 12:46:31,531 - INFO - 🔍 使用合同号查询: 26803940
2025-07-31 12:46:31,536 - INFO -    数据库查询结果: found=True, debtor_no=7525324b8800b1b734b433e72d3addba
2025-07-31 12:46:31,536 - INFO - ✅ 通过合同号找到债务人: 7525324b8800b1b734b433e72d3addba
2025-07-31 12:46:31,536 - INFO -    ✅ 匹配成功: debtor_no=7525324b8800b1b734b433e72d3addba, 方式=合同号
2025-07-31 12:46:31,537 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,538 - INFO -       债务人姓名: '王瑞'
2025-07-31 12:46:31,538 - INFO -       合同号: '11889020'
2025-07-31 12:46:31,538 - INFO -       身份证号: ''
2025-07-31 12:46:31,538 - INFO - 🔍 使用合同号查询: 11889020
2025-07-31 12:46:31,541 - INFO -    数据库查询结果: found=True, debtor_no=dac60eb33e95d6bb3c4522390a64fc53
2025-07-31 12:46:31,541 - INFO - ✅ 通过合同号找到债务人: dac60eb33e95d6bb3c4522390a64fc53
2025-07-31 12:46:31,541 - INFO -    ✅ 匹配成功: debtor_no=dac60eb33e95d6bb3c4522390a64fc53, 方式=合同号
2025-07-31 12:46:31,542 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,543 - INFO -       债务人姓名: '李培玉'
2025-07-31 12:46:31,543 - INFO -       合同号: '21802467'
2025-07-31 12:46:31,543 - INFO -       身份证号: ''
2025-07-31 12:46:31,543 - INFO - 🔍 使用合同号查询: 21802467
2025-07-31 12:46:31,545 - INFO -    数据库查询结果: found=True, debtor_no=4ff7c9f14f7f80439ee155f9fea036c5
2025-07-31 12:46:31,545 - INFO - ✅ 通过合同号找到债务人: 4ff7c9f14f7f80439ee155f9fea036c5
2025-07-31 12:46:31,545 - INFO -    ✅ 匹配成功: debtor_no=4ff7c9f14f7f80439ee155f9fea036c5, 方式=合同号
2025-07-31 12:46:31,546 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,546 - INFO -       债务人姓名: '唐玉函'
2025-07-31 12:46:31,546 - INFO -       合同号: '12004260'
2025-07-31 12:46:31,547 - INFO -       身份证号: ''
2025-07-31 12:46:31,547 - INFO - 🔍 使用合同号查询: 12004260
2025-07-31 12:46:31,550 - INFO -    数据库查询结果: found=True, debtor_no=0eba24f4ae33329fa9f32d34d5211c7a
2025-07-31 12:46:31,550 - INFO - ✅ 通过合同号找到债务人: 0eba24f4ae33329fa9f32d34d5211c7a
2025-07-31 12:46:31,551 - INFO -    ✅ 匹配成功: debtor_no=0eba24f4ae33329fa9f32d34d5211c7a, 方式=合同号
2025-07-31 12:46:31,552 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,552 - INFO -       债务人姓名: '张明生'
2025-07-31 12:46:31,553 - INFO -       合同号: '15131800'
2025-07-31 12:46:31,553 - INFO -       身份证号: ''
2025-07-31 12:46:31,553 - INFO - 🔍 使用合同号查询: 15131800
2025-07-31 12:46:31,558 - INFO -    数据库查询结果: found=True, debtor_no=7719874011fbadb9857fd829d85dd78b
2025-07-31 12:46:31,558 - INFO - ✅ 通过合同号找到债务人: 7719874011fbadb9857fd829d85dd78b
2025-07-31 12:46:31,559 - INFO -    ✅ 匹配成功: debtor_no=7719874011fbadb9857fd829d85dd78b, 方式=合同号
2025-07-31 12:46:31,559 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:46:31,560 - INFO -       债务人姓名: '张艳梅'
2025-07-31 12:46:31,560 - INFO -       合同号: '22230150'
2025-07-31 12:46:31,560 - INFO -       身份证号: ''
2025-07-31 12:46:31,560 - INFO - 🔍 使用合同号查询: 22230150
2025-07-31 12:46:31,564 - INFO -    数据库查询结果: found=True, debtor_no=c6d8b1499dc39aaf018a71008d624435
2025-07-31 12:46:31,564 - INFO - ✅ 通过合同号找到债务人: c6d8b1499dc39aaf018a71008d624435
2025-07-31 12:46:31,564 - INFO -    ✅ 匹配成功: debtor_no=c6d8b1499dc39aaf018a71008d624435, 方式=合同号
2025-07-31 12:46:31,565 - INFO -    ✅ 成功处理 15 条对账明细记录
2025-07-31 12:46:31,566 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:46:31,566 - INFO - 🗂️ 扫描还款对账明细: 15 条记录
2025-07-31 12:46:31,567 - INFO - 处理目录: D:\民生4期回款\7.29 隆丰电催
2025-07-31 12:46:31,568 - INFO - 🔍 扫描还款对账明细Excel: 7.29 隆丰电催
2025-07-31 12:46:31,568 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:46:31,569 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:46:31,575 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:46:31,575 - INFO - ✅ 通过姓名找到唯一匹配: 唐玉函 → debtor_no: 0eba24f4ae33329fa9f32d34d5211c7a
2025-07-31 12:46:31,575 - INFO - ✅ 通过还款对账明细找到debtor_no: 唐玉函 → 0eba24f4ae33329fa9f32d34d5211c7a (姓名唯一匹配)
2025-07-31 12:46:31,575 - INFO - 📸 开始处理图片: 唐玉函2000（身份证尾号1722）.jpg
2025-07-31 12:46:31,575 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:46:31,576 - INFO - 🔍 开始OCR识别图片: 唐玉函2000（身份证尾号1722）.jpg
2025-07-31 12:46:31,576 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 唐玉函2000（身份证尾号1722）.jpg
2025-07-31 12:46:45,421 - INFO - ✅ 豆包AI分析成功: 👤唐玉函 💳***9876 💰2000.00 ⏰2025-07-29 12:13:34
2025-07-31 12:46:45,421 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:46:45,422 - INFO - 👤 付款方识别成功: 【唐玉函】
2025-07-31 12:46:45,422 - INFO - 💳 账户号码识别成功: ***9876
2025-07-31 12:46:45,422 - INFO - 💰 还款金额识别成功: 2000.00
2025-07-31 12:46:45,423 - INFO - ⏰ 还款时间识别成功: 2025-07-29 12:13:34 → 2025-07-29
2025-07-31 12:46:45,423 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:46:45,423 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:46:45,423 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:46:45,424 - INFO -    👤 还款人: 【唐玉函】
2025-07-31 12:46:45,424 - INFO -    💳 还款账号: ***9876
2025-07-31 12:46:45,424 - INFO -    💰 还款金额: 2000.00
2025-07-31 12:46:45,425 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:46:45,425 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:46:45,425 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:46:45,425 - INFO - 🎯 图片处理完成: 唐玉函2000（身份证尾号1722）.jpg
2025-07-31 12:46:45,425 - INFO -    📋 最终结果 - 付款方: 【唐玉函】, 账户: ***9876, 来源: 【本人还款】
2025-07-31 12:46:45,428 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:46:45,428 - INFO - ✅ 通过姓名找到唯一匹配: 宋晶晶 → debtor_no: 4747d358ace295adee618427b659e998
2025-07-31 12:46:45,428 - INFO - ✅ 通过还款对账明细找到debtor_no: 宋晶晶 → 4747d358ace295adee618427b659e998 (姓名唯一匹配)
2025-07-31 12:46:45,428 - INFO - 📸 开始处理图片: 宋晶晶700.jpg
2025-07-31 12:46:45,429 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:46:45,429 - INFO - 🔍 开始OCR识别图片: 宋晶晶700.jpg
2025-07-31 12:46:45,429 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 宋晶晶700.jpg
2025-07-31 12:47:16,112 - INFO - ✅ 豆包AI分析成功: 👤陈冠羽 💳***2376 💰700 ⏰2025-07-28 19:14:31
2025-07-31 12:47:16,112 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:47:16,113 - INFO - 👤 付款方识别成功: 【陈冠羽】
2025-07-31 12:47:16,115 - INFO - 💳 账户号码识别成功: ***2376
2025-07-31 12:47:16,116 - INFO - 💰 还款金额识别成功: 700
2025-07-31 12:47:16,116 - INFO - ⏰ 还款时间识别成功: 2025-07-28 19:14:31 → 2025-07-28
2025-07-31 12:47:16,117 - INFO - 🔄 还款来源: 他人代还 (付款人[陈冠羽]与文件名[宋晶晶]不匹配)
2025-07-31 12:47:16,118 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:47:16,118 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.29 隆丰电催\宋晶晶700.jpg
2025-07-31 12:47:16,119 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:47:16,119 - WARNING -    姓名匹配: OCR[陈冠羽] vs 文件名[宋晶晶] = 0.00
2025-07-31 12:47:16,120 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-31 12:47:16,120 - WARNING -    金额匹配: OCR[700] vs 文件名[700] = 1.00
2025-07-31 12:47:16,120 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:47:16,121 - INFO -    👤 还款人: 【陈冠羽】
2025-07-31 12:47:16,121 - INFO -    💳 还款账号: ***2376
2025-07-31 12:47:16,121 - INFO -    💰 还款金额: 700
2025-07-31 12:47:16,122 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:47:16,122 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:47:16,122 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:47:16,123 - INFO - 🎯 图片处理完成: 宋晶晶700.jpg
2025-07-31 12:47:16,123 - INFO -    📋 最终结果 - 付款方: 【陈冠羽】, 账户: ***2376, 来源: 【他人代还】
2025-07-31 12:47:16,125 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:47:16,125 - INFO - ✅ 通过姓名找到唯一匹配: 张明生 → debtor_no: 7719874011fbadb9857fd829d85dd78b
2025-07-31 12:47:16,126 - INFO - ✅ 通过还款对账明细找到debtor_no: 张明生 → 7719874011fbadb9857fd829d85dd78b (姓名唯一匹配)
2025-07-31 12:47:16,126 - INFO - 📸 开始处理图片: 张明生600.jpg
2025-07-31 12:47:16,126 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:47:16,126 - INFO - 🔍 开始OCR识别图片: 张明生600.jpg
2025-07-31 12:47:16,127 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张明生600.jpg
2025-07-31 12:47:28,343 - INFO - ✅ 豆包AI分析成功: 👤*明生 💳***1684 💰600.00 ⏰2025-07-29 19:35:00
2025-07-31 12:47:28,344 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:47:28,344 - INFO - ⭐ 检测到带星号的付款方: 【*明生】，进行星号验证...
2025-07-31 12:47:28,344 - INFO - ✅ 通过姓名找到唯一匹配: 张明生 → debtor_no: 7719874011fbadb9857fd829d85dd78b
2025-07-31 12:47:28,344 - INFO - ✅ 星号还款人验证通过，将替换: *明生 → 张明生
2025-07-31 12:47:28,345 - INFO - ✅ 星号付款方替换成功: 【*明生】 → 【张明生】
2025-07-31 12:47:28,345 - INFO -    替换原因: 满足所有条件：含*号[*明生]，文件名债务人[张明生]，Excel有debtor_no[7719874011fbadb9857fd829d85dd78b]，时间一致[2025-07-29]
2025-07-31 12:47:28,345 - INFO - 💳 账户号码识别成功: ***1684
2025-07-31 12:47:28,345 - INFO - 💰 还款金额识别成功: 600.00
2025-07-31 12:47:28,345 - INFO - ⏰ 还款时间识别成功: 2025-07-29 19:35:00 → 2025-07-29
2025-07-31 12:47:28,346 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:47:28,346 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:47:28,346 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:47:28,346 - INFO -    👤 还款人: 【张明生】
2025-07-31 12:47:28,347 - INFO -    💳 还款账号: ***1684
2025-07-31 12:47:28,347 - INFO -    💰 还款金额: 600.00
2025-07-31 12:47:28,347 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:47:28,348 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:47:28,348 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.88)
2025-07-31 12:47:28,348 - INFO - 🎯 图片处理完成: 张明生600.jpg
2025-07-31 12:47:28,349 - INFO -    📋 最终结果 - 付款方: 【张明生】, 账户: ***1684, 来源: 【本人还款】
2025-07-31 12:47:28,350 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:47:28,351 - INFO - ✅ 通过姓名找到唯一匹配: 张艳梅 → debtor_no: c6d8b1499dc39aaf018a71008d624435
2025-07-31 12:47:28,351 - INFO - ✅ 通过还款对账明细找到debtor_no: 张艳梅 → c6d8b1499dc39aaf018a71008d624435 (姓名唯一匹配)
2025-07-31 12:47:28,351 - INFO - 📸 开始处理图片: 张艳梅500.jpg
2025-07-31 12:47:28,351 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:47:28,351 - INFO - 🔍 开始OCR识别图片: 张艳梅500.jpg
2025-07-31 12:47:28,351 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张艳梅500.jpg
2025-07-31 12:47:38,226 - INFO - 🔄 使用文件名中的姓名: 张艳梅
2025-07-31 12:47:38,226 - INFO - ✅ 豆包AI分析成功: 👤张艳梅 💳***5183 💰500.00 ⏰2025-07-28 16:43:34
2025-07-31 12:47:38,227 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:47:38,228 - INFO - 👤 付款方识别成功: 【张艳梅】
2025-07-31 12:47:38,229 - INFO - 💳 账户号码识别成功: ***5183
2025-07-31 12:47:38,229 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:47:38,230 - INFO - ⏰ 还款时间识别成功: 2025-07-28 16:43:34 → 2025-07-28
2025-07-31 12:47:38,232 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:47:38,233 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:47:38,234 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:47:38,234 - INFO -    👤 还款人: 【张艳梅】
2025-07-31 12:47:38,235 - INFO -    💳 还款账号: ***5183
2025-07-31 12:47:38,236 - INFO -    💰 还款金额: 500.00
2025-07-31 12:47:38,236 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:47:38,237 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:47:38,238 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:47:38,238 - INFO - 🎯 图片处理完成: 张艳梅500.jpg
2025-07-31 12:47:38,239 - INFO -    📋 最终结果 - 付款方: 【张艳梅】, 账户: ***5183, 来源: 【本人还款】
2025-07-31 12:47:38,242 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:47:38,242 - INFO - ✅ 通过姓名找到唯一匹配: 李丹丹 → debtor_no: 81b0cf17d283ab4c5d4241ae515e980d
2025-07-31 12:47:38,243 - INFO - ✅ 通过还款对账明细找到debtor_no: 李丹丹 → 81b0cf17d283ab4c5d4241ae515e980d (姓名唯一匹配)
2025-07-31 12:47:38,243 - INFO - 📸 开始处理图片: 李丹丹18454.jpg
2025-07-31 12:47:38,243 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:47:38,244 - INFO - 🔍 开始OCR识别图片: 李丹丹18454.jpg
2025-07-31 12:47:38,244 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李丹丹18454.jpg
2025-07-31 12:47:53,665 - INFO - ✅ 豆包AI分析成功: 👤李丹丹 💳***2071 💰18454.00 ⏰2025-07-28 17:10:55
2025-07-31 12:47:53,665 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:47:53,665 - INFO - 👤 付款方识别成功: 【李丹丹】
2025-07-31 12:47:53,666 - INFO - 💳 账户号码识别成功: ***2071
2025-07-31 12:47:53,666 - INFO - 💰 还款金额识别成功: 18454.00
2025-07-31 12:47:53,666 - INFO - ⏰ 还款时间识别成功: 2025-07-28 17:10:55 → 2025-07-28
2025-07-31 12:47:53,666 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:47:53,666 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:47:53,667 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:47:53,667 - INFO -    👤 还款人: 【李丹丹】
2025-07-31 12:47:53,667 - INFO -    💳 还款账号: ***2071
2025-07-31 12:47:53,667 - INFO -    💰 还款金额: 18454.00
2025-07-31 12:47:53,667 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:47:53,668 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:47:53,668 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:47:53,668 - INFO - 🎯 图片处理完成: 李丹丹18454.jpg
2025-07-31 12:47:53,668 - INFO -    📋 最终结果 - 付款方: 【李丹丹】, 账户: ***2071, 来源: 【本人还款】
2025-07-31 12:47:53,670 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:47:53,670 - INFO - ✅ 通过姓名找到唯一匹配: 李士峰 → debtor_no: 7525324b8800b1b734b433e72d3addba
2025-07-31 12:47:53,670 - INFO - ✅ 通过还款对账明细找到debtor_no: 李士峰 → 7525324b8800b1b734b433e72d3addba (姓名唯一匹配)
2025-07-31 12:47:53,670 - INFO - 📸 开始处理图片: 李士峰900.jpg
2025-07-31 12:47:53,670 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:47:53,671 - INFO - 🔍 开始OCR识别图片: 李士峰900.jpg
2025-07-31 12:47:53,671 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李士峰900.jpg
2025-07-31 12:48:04,189 - INFO - ✅ 豆包AI分析成功: 👤李士峰 💳***5706 💰900.00 ⏰2025-07-29 09:39:23
2025-07-31 12:48:04,189 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:48:04,189 - INFO - 👤 付款方识别成功: 【李士峰】
2025-07-31 12:48:04,190 - INFO - 💳 账户号码识别成功: ***5706
2025-07-31 12:48:04,190 - INFO - 💰 还款金额识别成功: 900.00
2025-07-31 12:48:04,190 - INFO - ⏰ 还款时间识别成功: 2025-07-29 09:39:23 → 2025-07-29
2025-07-31 12:48:04,190 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:48:04,191 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:48:04,191 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:48:04,191 - INFO -    👤 还款人: 【李士峰】
2025-07-31 12:48:04,191 - INFO -    💳 还款账号: ***5706
2025-07-31 12:48:04,191 - INFO -    💰 还款金额: 900.00
2025-07-31 12:48:04,192 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:48:04,192 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:48:04,192 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:48:04,192 - INFO - 🎯 图片处理完成: 李士峰900.jpg
2025-07-31 12:48:04,192 - INFO -    📋 最终结果 - 付款方: 【李士峰】, 账户: ***5706, 来源: 【本人还款】
2025-07-31 12:48:04,194 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:48:04,194 - INFO - ✅ 通过姓名找到唯一匹配: 王瑞 → debtor_no: dac60eb33e95d6bb3c4522390a64fc53
2025-07-31 12:48:04,194 - INFO - ✅ 通过还款对账明细找到debtor_no: 王瑞 → dac60eb33e95d6bb3c4522390a64fc53 (姓名唯一匹配)
2025-07-31 12:48:04,195 - INFO - 📸 开始处理图片: 王瑞500.jpg
2025-07-31 12:48:04,195 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:48:04,195 - INFO - 🔍 开始OCR识别图片: 王瑞500.jpg
2025-07-31 12:48:04,195 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王瑞500.jpg
2025-07-31 12:48:25,849 - INFO - 🔄 使用文件名中的姓名: 王瑞
2025-07-31 12:48:25,850 - INFO - ✅ 豆包AI分析成功: 👤王瑞 💳***6979 💰500.00 ⏰2025-07-29 09:58:58
2025-07-31 12:48:25,850 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:48:25,850 - INFO - 👤 付款方识别成功: 【王瑞】
2025-07-31 12:48:25,850 - INFO - 💳 账户号码识别成功: ***6979
2025-07-31 12:48:25,851 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:48:25,851 - INFO - ⏰ 还款时间识别成功: 2025-07-29 09:58:58 → 2025-07-29
2025-07-31 12:48:25,851 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:48:25,851 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:48:25,852 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:48:25,852 - INFO -    👤 还款人: 【王瑞】
2025-07-31 12:48:25,852 - INFO -    💳 还款账号: ***6979
2025-07-31 12:48:25,852 - INFO -    💰 还款金额: 500.00
2025-07-31 12:48:25,852 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:48:25,852 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:48:25,853 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:48:25,853 - INFO - 🎯 图片处理完成: 王瑞500.jpg
2025-07-31 12:48:25,853 - INFO -    📋 最终结果 - 付款方: 【王瑞】, 账户: ***6979, 来源: 【本人还款】
2025-07-31 12:48:25,854 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:48:25,854 - INFO - ✅ 通过姓名找到唯一匹配: 王雪 → debtor_no: 337a5b8d71fed21eba5d21e41affa7f0
2025-07-31 12:48:25,855 - INFO - ✅ 通过还款对账明细找到debtor_no: 王雪 → 337a5b8d71fed21eba5d21e41affa7f0 (姓名唯一匹配)
2025-07-31 12:48:25,855 - INFO - 📸 开始处理图片: 王雪500.jpg
2025-07-31 12:48:25,855 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:48:25,855 - INFO - 🔍 开始OCR识别图片: 王雪500.jpg
2025-07-31 12:48:25,855 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王雪500.jpg
2025-07-31 12:48:39,145 - INFO - ✅ 豆包AI分析成功: 👤*兴惟 💳***0000 💰500.00 ⏰2025-07-28 19:32:36
2025-07-31 12:48:39,145 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:48:39,146 - INFO - ⭐ 检测到带星号的付款方: 【*兴惟】，进行星号验证...
2025-07-31 12:48:39,147 - INFO - ✅ 通过姓名找到唯一匹配: 王雪 → debtor_no: 337a5b8d71fed21eba5d21e41affa7f0
2025-07-31 12:48:39,148 - INFO - ✅ 星号还款人验证通过，将替换: *兴惟 → 王雪
2025-07-31 12:48:39,148 - INFO - ✅ 星号付款方替换成功: 【*兴惟】 → 【王雪】
2025-07-31 12:48:39,149 - INFO -    替换原因: 满足所有条件：含*号[*兴惟]，文件名债务人[王雪]，Excel有debtor_no[337a5b8d71fed21eba5d21e41affa7f0]，时间一致[2025-07-28]
2025-07-31 12:48:39,149 - INFO - 💳 账户号码识别成功: ***0000
2025-07-31 12:48:39,150 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:48:39,151 - INFO - ⏰ 还款时间识别成功: 2025-07-28 19:32:36 → 2025-07-28
2025-07-31 12:48:39,151 - INFO - 🔄 还款来源: 他人代还 (付款人[*兴惟]与文件名[王雪]不匹配)
2025-07-31 12:48:39,152 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:48:39,152 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.29 隆丰电催\王雪500.jpg
2025-07-31 12:48:39,153 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:48:39,153 - WARNING -    姓名匹配: OCR[*兴惟] vs 文件名[王雪] = 0.00
2025-07-31 12:48:39,154 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-31 12:48:39,154 - WARNING -    金额匹配: OCR[500.00] vs 文件名[500] = 1.00
2025-07-31 12:48:39,154 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:48:39,155 - INFO -    👤 还款人: 【王雪】
2025-07-31 12:48:39,155 - INFO -    💳 还款账号: ***0000
2025-07-31 12:48:39,156 - INFO -    💰 还款金额: 500.00
2025-07-31 12:48:39,156 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:48:39,156 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:48:39,157 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:48:39,157 - INFO - 🎯 图片处理完成: 王雪500.jpg
2025-07-31 12:48:39,158 - INFO -    📋 最终结果 - 付款方: 【王雪】, 账户: ***0000, 来源: 【他人代还】
2025-07-31 12:48:39,160 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:48:39,161 - INFO - ✅ 通过姓名找到唯一匹配: 董有森 → debtor_no: 89de5c088c0f1a31f42649ad0087a33a
2025-07-31 12:48:39,161 - INFO - ✅ 通过还款对账明细找到debtor_no: 董有森 → 89de5c088c0f1a31f42649ad0087a33a (姓名唯一匹配)
2025-07-31 12:48:39,162 - INFO - 📸 开始处理图片: 董有森300.jpg
2025-07-31 12:48:39,162 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:48:39,162 - INFO - 🔍 开始OCR识别图片: 董有森300.jpg
2025-07-31 12:48:39,163 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 董有森300.jpg
2025-07-31 12:48:53,814 - INFO - ✅ 豆包AI分析成功: 👤*红军 💳***7618 💰300.00 ⏰
2025-07-31 12:48:53,814 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:48:53,814 - INFO - ⭐ 检测到带星号的付款方: 【*红军】，进行星号验证...
2025-07-31 12:48:53,814 - INFO - ✅ 通过姓名找到唯一匹配: 董有森 → debtor_no: 89de5c088c0f1a31f42649ad0087a33a
2025-07-31 12:48:53,814 - INFO - ⭐ 保持星号付款方: 【*红军】
2025-07-31 12:48:53,815 - INFO -    不替换原因: OCR未识别到还款时间
2025-07-31 12:48:53,815 - INFO - 💳 账户号码识别成功: ***7618
2025-07-31 12:48:53,815 - INFO - 💰 还款金额识别成功: 300.00
2025-07-31 12:48:53,815 - WARNING - ⏰ OCR未识别到还款时间，保持为空
2025-07-31 12:48:53,815 - INFO - 🔄 还款来源: 他人代还 (付款人[*红军]与文件名[董有森]不匹配)
2025-07-31 12:48:53,816 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:48:53,816 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.29 隆丰电催\董有森300.jpg
2025-07-31 12:48:53,816 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:48:53,817 - WARNING -    姓名匹配: OCR[*红军] vs 文件名[董有森] = 0.00
2025-07-31 12:48:53,817 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-31 12:48:53,817 - WARNING -    金额匹配: OCR[300.00] vs 文件名[300] = 1.00
2025-07-31 12:48:53,817 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:48:53,817 - INFO -    👤 还款人: 【*红军】
2025-07-31 12:48:53,818 - INFO -    💳 还款账号: ***7618
2025-07-31 12:48:53,818 - INFO -    💰 还款金额: 300.00
2025-07-31 12:48:53,818 - INFO -    ⏰ 还款时间: 
2025-07-31 12:48:53,818 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:48:53,818 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:48:53,819 - INFO - 🎯 图片处理完成: 董有森300.jpg
2025-07-31 12:48:53,819 - INFO -    📋 最终结果 - 付款方: 【*红军】, 账户: ***7618, 来源: 【他人代还】
2025-07-31 12:48:53,820 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:48:53,821 - INFO - ✅ 通过姓名找到唯一匹配: 袁双 → debtor_no: 95dea243d44e4087097fadd9264a869c
2025-07-31 12:48:53,821 - INFO - ✅ 通过还款对账明细找到debtor_no: 袁双 → 95dea243d44e4087097fadd9264a869c (姓名唯一匹配)
2025-07-31 12:48:53,821 - INFO - 📸 开始处理图片: 袁双722.jpg
2025-07-31 12:48:53,821 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:48:53,821 - INFO - 🔍 开始OCR识别图片: 袁双722.jpg
2025-07-31 12:48:53,822 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 袁双722.jpg
2025-07-31 12:49:01,672 - INFO - ✅ 豆包AI分析成功: 👤*双 💳***4371 💰722.00 ⏰2025-07-28 22:31:24
2025-07-31 12:49:01,673 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:49:01,673 - INFO - ⭐ 检测到带星号的付款方: 【*双】，进行星号验证...
2025-07-31 12:49:01,673 - INFO - ✅ 通过姓名找到唯一匹配: 袁双 → debtor_no: 95dea243d44e4087097fadd9264a869c
2025-07-31 12:49:01,673 - INFO - ✅ 星号还款人验证通过，将替换: *双 → 袁双
2025-07-31 12:49:01,673 - INFO - ✅ 星号付款方替换成功: 【*双】 → 【袁双】
2025-07-31 12:49:01,674 - INFO -    替换原因: 满足所有条件：含*号[*双]，文件名债务人[袁双]，Excel有debtor_no[95dea243d44e4087097fadd9264a869c]，时间一致[2025-07-28]
2025-07-31 12:49:01,674 - INFO - 💳 账户号码识别成功: ***4371
2025-07-31 12:49:01,674 - INFO - 💰 还款金额识别成功: 722.00
2025-07-31 12:49:01,674 - INFO - ⏰ 还款时间识别成功: 2025-07-28 22:31:24 → 2025-07-28
2025-07-31 12:49:01,675 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:49:01,675 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:49:01,675 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:49:01,675 - INFO -    👤 还款人: 【袁双】
2025-07-31 12:49:01,675 - INFO -    💳 还款账号: ***4371
2025-07-31 12:49:01,676 - INFO -    💰 还款金额: 722.00
2025-07-31 12:49:01,676 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:49:01,676 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:49:01,676 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.88)
2025-07-31 12:49:01,676 - INFO - 🎯 图片处理完成: 袁双722.jpg
2025-07-31 12:49:01,677 - INFO -    📋 最终结果 - 付款方: 【袁双】, 账户: ***4371, 来源: 【本人还款】
2025-07-31 12:49:01,679 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:49:01,679 - INFO - ✅ 通过姓名找到唯一匹配: 邢阳 → debtor_no: c11c910d88ebfa123b7ee9fbcf38a787
2025-07-31 12:49:01,679 - INFO - ✅ 通过还款对账明细找到debtor_no: 邢阳 → c11c910d88ebfa123b7ee9fbcf38a787 (姓名唯一匹配)
2025-07-31 12:49:01,679 - INFO - 📸 开始处理图片: 邢阳2000.jpg
2025-07-31 12:49:01,680 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:49:01,680 - INFO - 🔍 开始OCR识别图片: 邢阳2000.jpg
2025-07-31 12:49:01,680 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 邢阳2000.jpg
2025-07-31 12:49:14,442 - INFO - ✅ 豆包AI分析成功: 👤*阳 💳***2278 💰2000.00 ⏰2025-07-28 17:23:56
2025-07-31 12:49:14,442 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:49:14,442 - INFO - ⭐ 检测到带星号的付款方: 【*阳】，进行星号验证...
2025-07-31 12:49:14,443 - INFO - ✅ 通过姓名找到唯一匹配: 邢阳 → debtor_no: c11c910d88ebfa123b7ee9fbcf38a787
2025-07-31 12:49:14,443 - INFO - ✅ 星号还款人验证通过，将替换: *阳 → 邢阳
2025-07-31 12:49:14,443 - INFO - ✅ 星号付款方替换成功: 【*阳】 → 【邢阳】
2025-07-31 12:49:14,443 - INFO -    替换原因: 满足所有条件：含*号[*阳]，文件名债务人[邢阳]，Excel有debtor_no[c11c910d88ebfa123b7ee9fbcf38a787]，时间一致[2025-07-28]
2025-07-31 12:49:14,444 - INFO - 💳 账户号码识别成功: ***2278
2025-07-31 12:49:14,444 - INFO - 💰 还款金额识别成功: 2000.00
2025-07-31 12:49:14,444 - INFO - ⏰ 还款时间识别成功: 2025-07-28 17:23:56 → 2025-07-28
2025-07-31 12:49:14,444 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:49:14,445 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:49:14,445 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:49:14,445 - INFO -    👤 还款人: 【邢阳】
2025-07-31 12:49:14,445 - INFO -    💳 还款账号: ***2278
2025-07-31 12:49:14,445 - INFO -    💰 还款金额: 2000.00
2025-07-31 12:49:14,445 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:49:14,446 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:49:14,446 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.88)
2025-07-31 12:49:14,446 - INFO - 🎯 图片处理完成: 邢阳2000.jpg
2025-07-31 12:49:14,446 - INFO -    📋 最终结果 - 付款方: 【邢阳】, 账户: ***2278, 来源: 【本人还款】
2025-07-31 12:49:14,447 - DEBUG - STREAM b'IHDR' 16 13
2025-07-31 12:49:14,447 - DEBUG - STREAM b'sRGB' 41 1
2025-07-31 12:49:14,447 - DEBUG - STREAM b'gAMA' 54 4
2025-07-31 12:49:14,447 - DEBUG - STREAM b'pHYs' 70 9
2025-07-31 12:49:14,448 - DEBUG - STREAM b'IDAT' 91 65445
2025-07-31 12:49:14,451 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:49:14,452 - INFO - ✅ 通过姓名找到唯一匹配: 郝宇 → debtor_no: d2255984333b7ae1a349dcc2d8613455
2025-07-31 12:49:14,452 - INFO - ✅ 通过还款对账明细找到debtor_no: 郝宇 → d2255984333b7ae1a349dcc2d8613455 (姓名唯一匹配)
2025-07-31 12:49:14,452 - INFO - 📸 开始处理图片: 郝宇1500.png
2025-07-31 12:49:14,452 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:49:14,453 - INFO - 🔍 开始OCR识别图片: 郝宇1500.png
2025-07-31 12:49:14,453 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 郝宇1500.png
2025-07-31 12:49:14,453 - DEBUG - STREAM b'IHDR' 16 13
2025-07-31 12:49:14,454 - DEBUG - STREAM b'sRGB' 41 1
2025-07-31 12:49:14,454 - DEBUG - STREAM b'gAMA' 54 4
2025-07-31 12:49:14,454 - DEBUG - STREAM b'pHYs' 70 9
2025-07-31 12:49:14,454 - DEBUG - STREAM b'IDAT' 91 65445
2025-07-31 12:49:28,961 - INFO - ✅ 豆包AI分析成功: 👤沈阳小宇宠物布有限公司 💳***0962 💰1500.00 ⏰2025-07-28 17:47:00
2025-07-31 12:49:28,962 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:49:28,962 - INFO - 👤 付款方识别成功: 【沈阳小宇宠物布有限公司】
2025-07-31 12:49:28,962 - INFO - 💳 账户号码识别成功: ***0962
2025-07-31 12:49:28,962 - INFO - 💰 还款金额识别成功: 1500.00
2025-07-31 12:49:28,962 - INFO - ⏰ 还款时间识别成功: 2025-07-28 17:47:00 → 2025-07-28
2025-07-31 12:49:28,963 - INFO - 🔄 还款来源: 他人代还 (付款人[沈阳小宇宠物布有限公司]与文件名[郝宇]不匹配)
2025-07-31 12:49:28,963 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:49:28,963 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.29 隆丰电催\郝宇1500.png
2025-07-31 12:49:28,963 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:49:28,964 - WARNING -    姓名匹配: OCR[沈阳小宇宠物布有限公司] vs 文件名[郝宇] = 0.00
2025-07-31 12:49:28,964 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.15)
2025-07-31 12:49:28,964 - WARNING -    金额匹配: OCR[1500.00] vs 文件名[1500] = 1.00
2025-07-31 12:49:28,964 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:49:28,964 - INFO -    👤 还款人: 【沈阳小宇宠物布有限公司】
2025-07-31 12:49:28,965 - INFO -    💳 还款账号: ***0962
2025-07-31 12:49:28,965 - INFO -    💰 还款金额: 1500.00
2025-07-31 12:49:28,965 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:49:28,965 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:49:28,965 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:49:28,965 - INFO - 🎯 图片处理完成: 郝宇1500.png
2025-07-31 12:49:28,965 - INFO -    📋 最终结果 - 付款方: 【沈阳小宇宠物布有限公司】, 账户: ***0962, 来源: 【他人代还】
2025-07-31 12:49:28,967 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:49:28,967 - INFO - ✅ 通过姓名找到唯一匹配: 龚莹莹 → debtor_no: 91be375a708772449c67d75c109858cf
2025-07-31 12:49:28,967 - INFO - ✅ 通过还款对账明细找到debtor_no: 龚莹莹 → 91be375a708772449c67d75c109858cf (姓名唯一匹配)
2025-07-31 12:49:28,967 - INFO - 📸 开始处理图片: 龚莹莹495.jpg
2025-07-31 12:49:28,967 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:49:28,967 - INFO - 🔍 开始OCR识别图片: 龚莹莹495.jpg
2025-07-31 12:49:28,968 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 龚莹莹495.jpg
2025-07-31 12:49:49,230 - INFO - 🔄 使用文件名中的姓名: 龚莹莹
2025-07-31 12:49:49,230 - INFO - ✅ 豆包AI分析成功: 👤龚莹莹 💳***1157 💰495.00 ⏰2025-07-28 16:34:02
2025-07-31 12:49:49,231 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:49:49,231 - INFO - 👤 付款方识别成功: 【龚莹莹】
2025-07-31 12:49:49,231 - INFO - 💳 账户号码识别成功: ***1157
2025-07-31 12:49:49,231 - INFO - 💰 还款金额识别成功: 495.00
2025-07-31 12:49:49,232 - INFO - ⏰ 还款时间识别成功: 2025-07-28 16:34:02 → 2025-07-28
2025-07-31 12:49:49,232 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:49:49,232 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:49:49,232 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:49:49,233 - INFO -    👤 还款人: 【龚莹莹】
2025-07-31 12:49:49,233 - INFO -    💳 还款账号: ***1157
2025-07-31 12:49:49,233 - INFO -    💰 还款金额: 495.00
2025-07-31 12:49:49,234 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:49:49,234 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:49:49,234 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:49:49,234 - INFO - 🎯 图片处理完成: 龚莹莹495.jpg
2025-07-31 12:49:49,234 - INFO -    📋 最终结果 - 付款方: 【龚莹莹】, 账户: ***1157, 来源: 【本人还款】
2025-07-31 12:49:49,235 - INFO - 🔍 扫描还款对账明细Excel: 于长军1214
2025-07-31 12:49:49,235 - INFO -    ⚠️ 未找到还款对账明细Excel文件
2025-07-31 12:49:49,236 - INFO - 处理子文件夹（按文件夹名解析）: 于长军1214
2025-07-31 12:49:49,236 - INFO - 找到代存图片: D:\民生4期回款\7.29 隆丰电催\于长军1214\代还.jpg
2025-07-31 12:49:49,236 - INFO - ✅ 通过姓名找到唯一匹配: 于长军 → debtor_no: a9503193e8652de05772253306599f7c
2025-07-31 12:49:49,236 - INFO - ✅ 子目录通过还款对账明细找到debtor_no: 于长军 → a9503193e8652de05772253306599f7c (姓名唯一匹配)
2025-07-31 12:49:49,237 - INFO - 🚀 子目录OCR识别流程: 于长军1214.jpg
2025-07-31 12:49:49,237 - INFO - 🔍 开始OCR识别图片: 于长军1214.jpg
2025-07-31 12:49:49,237 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 于长军1214.jpg
2025-07-31 12:50:02,947 - INFO - 🔄 使用文件名中的姓名: 于长军
2025-07-31 12:50:02,947 - INFO - ✅ 豆包AI分析成功: 👤于长军 💳***1995 💰1214.00 ⏰2025-07-29 09:03:08
2025-07-31 12:50:02,947 - INFO - ✅ 子目录OCR识别成功，应用识别结果...
2025-07-31 12:50:02,947 - INFO - 👤 子目录付款方识别成功: 【于长军】
2025-07-31 12:50:02,947 - INFO - 💳 子目录账户识别成功: ***1995
2025-07-31 12:50:02,947 - INFO - 💰 子目录还款金额识别成功: 1214.00
2025-07-31 12:50:02,948 - INFO - ⏰ 子目录还款时间识别成功: 2025-07-29 09:03:08 → 2025-07-29
2025-07-31 12:50:02,948 - INFO - 🔄 子目录还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:50:02,948 - INFO - 📊 子目录OCR结果应用完成:
2025-07-31 12:50:02,948 - INFO -    👤 还款人: 【于长军】
2025-07-31 12:50:02,948 - INFO -    💳 还款账号: ***1995
2025-07-31 12:50:02,948 - INFO -    💰 还款金额: 1214.00
2025-07-31 12:50:02,948 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:50:02,949 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:50:02,949 - INFO - 基于文件夹名称创建记录: 于长军1214 -> 于长军 1214元
2025-07-31 12:50:02,949 - INFO - 🔍 扫描还款对账明细Excel: 李培玉500
2025-07-31 12:50:02,949 - INFO -    ⚠️ 未找到还款对账明细Excel文件
2025-07-31 12:50:02,949 - INFO - 处理子文件夹（按文件夹名解析）: 李培玉500
2025-07-31 12:50:02,950 - INFO - 找到代存图片: D:\民生4期回款\7.29 隆丰电催\李培玉500\代还.jpg
2025-07-31 12:50:02,950 - INFO - ✅ 通过姓名找到唯一匹配: 李培玉 → debtor_no: 4ff7c9f14f7f80439ee155f9fea036c5
2025-07-31 12:50:02,950 - INFO - ✅ 子目录通过还款对账明细找到debtor_no: 李培玉 → 4ff7c9f14f7f80439ee155f9fea036c5 (姓名唯一匹配)
2025-07-31 12:50:02,950 - INFO - 🚀 子目录OCR识别流程: 李培玉500.jpg
2025-07-31 12:50:02,950 - INFO - 🔍 开始OCR识别图片: 李培玉500.jpg
2025-07-31 12:50:02,950 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李培玉500.jpg
2025-07-31 12:50:19,647 - INFO - 🔄 使用文件名中的姓名: 李培玉
2025-07-31 12:50:19,647 - INFO - ✅ 豆包AI分析成功: 👤李培玉 💳***1157 💰500.00 ⏰2025-07-29 10:38:47
2025-07-31 12:50:19,648 - INFO - ✅ 子目录OCR识别成功，应用识别结果...
2025-07-31 12:50:19,648 - INFO - 👤 子目录付款方识别成功: 【李培玉】
2025-07-31 12:50:19,648 - INFO - 💳 子目录账户识别成功: ***1157
2025-07-31 12:50:19,648 - INFO - 💰 子目录还款金额识别成功: 500.00
2025-07-31 12:50:19,648 - INFO - ⏰ 子目录还款时间识别成功: 2025-07-29 10:38:47 → 2025-07-29
2025-07-31 12:50:19,649 - INFO - 🔄 子目录还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:50:19,649 - INFO - 📊 子目录OCR结果应用完成:
2025-07-31 12:50:19,649 - INFO -    👤 还款人: 【李培玉】
2025-07-31 12:50:19,649 - INFO -    💳 还款账号: ***1157
2025-07-31 12:50:19,650 - INFO -    💰 还款金额: 500.00
2025-07-31 12:50:19,650 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:50:19,650 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:50:19,650 - INFO - 基于文件夹名称创建记录: 李培玉500 -> 李培玉 500元
2025-07-31 12:50:19,650 - INFO - 📊 开始验证文件夹 '7.29 隆丰电催' 的数据一致性...
2025-07-31 12:50:19,671 - ERROR - ❌ 文件夹 '7.29 隆丰电催' 数据一致性验证失败!
2025-07-31 12:50:19,671 - ERROR -    Excel中的人员未在图片中找到: 董有森, 宋晶晶, 郝宇
2025-07-31 12:50:19,672 - ERROR -    图片中的人员在Excel中未找到: *红军, 沈阳小宇宠物布有限公司, 陈冠羽
2025-07-31 12:50:19,672 - INFO - 🔍 开始验证OCR识别结果与Excel对比...
2025-07-31 12:50:19,691 - INFO - 📋 读取还款对账明细文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:50:19,692 - WARNING - ❌ 还款人未在Excel中找到: 陈冠羽
2025-07-31 12:50:19,693 - WARNING - ❌ 还款人未在Excel中找到: *红军
2025-07-31 12:50:19,693 - WARNING - ❌ 还款人未在Excel中找到: 沈阳小宇宠物布有限公司
2025-07-31 12:50:19,693 - INFO - 📊 Excel验证完成 - 匹配: 12, 未匹配: 3
2025-07-31 12:50:19,693 - WARNING - ⚠️  Excel验证发现问题:
2025-07-31 12:50:19,694 - WARNING -    有 3 个还款人未在Excel中找到
2025-07-31 12:50:19,694 - INFO - 目录 7.29 隆丰电催 处理完成，共处理 15 个图片
2025-07-31 12:50:19,695 - INFO - 🟠 发现结清验证问题: 81b0cf17d283ab4c5d4241ae515e980d - 还款18454.0，剩余23067.44
2025-07-31 12:50:19,695 - INFO - ✅ 设置结清验证状态: 81b0cf17d283ab4c5d4241ae515e980d - 结清但还款金额小于剩余本金，需要关注
2025-07-31 12:50:19,703 - INFO - 写入主要处理结果: 15 条记录
2025-07-31 12:50:19,708 - INFO - 写入还款对账明细: 15 条记录
2025-07-31 12:50:19,708 - INFO - 🔍 开始执行三重比对分析...
2025-07-31 12:50:19,708 - INFO - 📊 处理结果明细包含 15 个不同债务人
2025-07-31 12:50:19,709 - INFO - 📊 还款对账明细包含 15 个不同债务人
2025-07-31 12:50:19,709 - INFO - 🔍 执行分析1：债务人姓名匹配分析
2025-07-31 12:50:19,709 - INFO - 🔍 执行分析2：还款金额匹配分析
2025-07-31 12:50:19,709 - INFO - 🔍 执行分析3：结清验证分析
2025-07-31 12:50:19,710 - INFO - ✅ 三重比对分析完成
2025-07-31 12:50:19,710 - INFO -    📊 统计结果: {'还款对账明细债务人数': 15, '处理结果明细债务人数': 15, '共同债务人数': 15, '仅在还款对账明细中': 0, '仅在处理结果明细中': 0, '金额不一致债务人数': 0, '需要关注的结清案例': 1}
2025-07-31 12:50:19,713 - INFO - 写入金额匹配分析: 15 条记录
2025-07-31 12:50:19,717 - INFO - 写入结清验证分析: 1 条记录
2025-07-31 12:50:19,718 - INFO - 写入比对分析摘要: 7 条记录
2025-07-31 12:50:19,718 - INFO - 开始生成人名统计，共有 15 条处理结果
2025-07-31 12:50:19,719 - INFO - 处理完成，共处理 1 个目录，生成 15 个人员统计
2025-07-31 12:50:19,719 - INFO - 处理的目录列表: ['7.29 隆丰电催']
2025-07-31 12:50:19,911 - ERROR - 从Excel提取信息失败 7.29 隆丰电催/*红军: nothing to repeat at position 0
2025-07-31 12:50:20,034 - INFO - 最终生成 15 个人员统计记录
2025-07-31 12:50:20,037 - INFO - 写入人名统计信息: 15 条记录
2025-07-31 12:50:20,040 - INFO - 🎨 开始应用Excel格式设置...
2025-07-31 12:50:20,040 - INFO -    文件路径列索引: 2
2025-07-31 12:50:20,040 - INFO -    总行数: 16
2025-07-31 12:50:20,041 - INFO - 🔗 正在设置文件路径超链接...
2025-07-31 12:50:20,041 - INFO - ✅ 文件路径超链接设置完成，共设置 15 个超链接
2025-07-31 12:50:20,041 - INFO - 🎨 正在设置行颜色标记...
2025-07-31 12:50:20,042 - INFO - ✅ '处理结果明细'sheet格式设置完成
2025-07-31 12:50:20,043 - INFO - 🔴 正在设置结清验证状态列格式...
2025-07-31 12:50:20,043 - INFO - ✅ 结清验证状态列格式化完成：1 个单元格标红
2025-07-31 12:50:20,043 - INFO - ✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红
2025-07-31 12:50:20,044 - INFO - 📍 找到关键列 - 图片文件数量: 13, debtor_no: 9, 类型: 6
2025-07-31 12:50:20,045 - INFO -    🔴 标红第10行: 于长军 (图片文件数量为0)
2025-07-31 12:50:20,046 - INFO -    🔴 标红第13行: 李培玉 (图片文件数量为0)
2025-07-31 12:50:20,046 - INFO - ✅ 还款对账明细格式化完成
2025-07-31 12:50:20,047 - INFO -    🔴 图片文件缺失标红: 2 行
2025-07-31 12:50:20,047 - INFO -    🟠 结清验证问题标橙: 0 行
2025-07-31 12:50:20,047 - WARNING - ⚠️ 发现 2 条记录存在图片缺失或金额不一致，已在Excel中标红显示
2025-07-31 12:50:20,047 - WARNING -    💡 这些记录可能需要检查：图片缺失或金额差异
2025-07-31 12:50:20,049 - INFO - 写入目录统计信息: 1 条记录
2025-07-31 12:50:20,050 - INFO - 写入处理摘要信息
2025-07-31 12:50:20,080 - INFO - 增强版Excel文件保存成功: 7.29 隆丰电催_还款凭证解析_20250731_125019.xlsx
2025-07-31 12:50:20,080 - INFO - ✅ 成功生成Excel文件: 7.29 隆丰电催_还款凭证解析_20250731_125019.xlsx
2025-07-31 12:50:20,081 - INFO - ✅ 子文件夹 7.29 隆丰电催 处理完成，生成文件: 7.29 隆丰电催_还款凭证解析_20250731_125019.xlsx
2025-07-31 12:50:20,081 - INFO - ================================================================================
2025-07-31 12:50:20,081 - INFO - 🗂️  开始处理子文件夹: 7.29 隆丰诉保
2025-07-31 12:50:20,082 - INFO - ✅ 目录 7.29 隆丰诉保 中Excel文件验证通过: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:50:20,082 - INFO - 目录 7.29 隆丰诉保 Excel验证成功
2025-07-31 12:50:20,082 - INFO - 🔍 扫描还款对账明细Excel: 7.29 隆丰诉保
2025-07-31 12:50:20,083 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:50:20,103 - INFO -    📋 Excel文件包含 25 行数据
2025-07-31 12:50:20,104 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号码', '还款金额': '现金回收', '类型': '还款类型（部分还款/结清）', '身份证号': '身份证号码'}
2025-07-31 12:50:20,104 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:50:20,104 - INFO -       债务人姓名: '司立民'
2025-07-31 12:50:20,104 - INFO -       合同号: '36930320.0'
2025-07-31 12:50:20,105 - INFO -       身份证号: '2.2022219720514272e+17'
2025-07-31 12:50:20,105 - INFO - 🔍 使用身份证号查询: 2.2022219720514272e+17
2025-07-31 12:50:20,107 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,108 - INFO -    ❌ 身份证号查询无结果: 2.2022219720514272e+17
2025-07-31 12:50:20,108 - INFO - 🔍 使用合同号查询: 36930320.0
2025-07-31 12:50:20,113 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,113 - INFO -    ❌ 合同号查询无结果: 36930320.0
2025-07-31 12:50:20,116 - INFO - ✅ 通过姓名唯一匹配找到债务人: 23dcacdbbf322661d6edeaeea815ea35
2025-07-31 12:50:20,116 - INFO -    ✅ 匹配成功: debtor_no=23dcacdbbf322661d6edeaeea815ea35, 方式=姓名唯一匹配
2025-07-31 12:50:20,116 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:50:20,117 - INFO -       债务人姓名: '郑东艳'
2025-07-31 12:50:20,117 - INFO -       合同号: '12585079.0'
2025-07-31 12:50:20,117 - INFO -       身份证号: '2.1011119810417382e+17'
2025-07-31 12:50:20,117 - INFO - 🔍 使用身份证号查询: 2.1011119810417382e+17
2025-07-31 12:50:20,118 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,118 - INFO -    ❌ 身份证号查询无结果: 2.1011119810417382e+17
2025-07-31 12:50:20,118 - INFO - 🔍 使用合同号查询: 12585079.0
2025-07-31 12:50:20,124 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,124 - INFO -    ❌ 合同号查询无结果: 12585079.0
2025-07-31 12:50:20,127 - INFO - ✅ 通过姓名唯一匹配找到债务人: 6a807ed32916f2a3d031444323ffa917
2025-07-31 12:50:20,127 - INFO -    ✅ 匹配成功: debtor_no=6a807ed32916f2a3d031444323ffa917, 方式=姓名唯一匹配
2025-07-31 12:50:20,128 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:50:20,128 - INFO -       债务人姓名: '鄂晓鹏'
2025-07-31 12:50:20,128 - INFO -       合同号: '19823455.0'
2025-07-31 12:50:20,128 - INFO -       身份证号: '2.101131982062368e+17'
2025-07-31 12:50:20,128 - INFO - 🔍 使用身份证号查询: 2.101131982062368e+17
2025-07-31 12:50:20,129 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,129 - INFO -    ❌ 身份证号查询无结果: 2.101131982062368e+17
2025-07-31 12:50:20,129 - INFO - 🔍 使用合同号查询: 19823455.0
2025-07-31 12:50:20,133 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,134 - INFO -    ❌ 合同号查询无结果: 19823455.0
2025-07-31 12:50:20,135 - INFO - ✅ 通过姓名唯一匹配找到债务人: 823be76e7610e3614a8c62045453666b
2025-07-31 12:50:20,135 - INFO -    ✅ 匹配成功: debtor_no=823be76e7610e3614a8c62045453666b, 方式=姓名唯一匹配
2025-07-31 12:50:20,136 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:50:20,136 - INFO -       债务人姓名: '吴春玲'
2025-07-31 12:50:20,137 - INFO -       合同号: '30479697.0'
2025-07-31 12:50:20,137 - INFO -       身份证号: '2.2018319811121702e+17'
2025-07-31 12:50:20,137 - INFO - 🔍 使用身份证号查询: 2.2018319811121702e+17
2025-07-31 12:50:20,138 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,138 - INFO -    ❌ 身份证号查询无结果: 2.2018319811121702e+17
2025-07-31 12:50:20,138 - INFO - 🔍 使用合同号查询: 30479697.0
2025-07-31 12:50:20,143 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,143 - INFO -    ❌ 合同号查询无结果: 30479697.0
2025-07-31 12:50:20,145 - INFO - ✅ 通过姓名唯一匹配找到债务人: 932b0856eed9c31e728875b7651c6a5b
2025-07-31 12:50:20,145 - INFO -    ✅ 匹配成功: debtor_no=932b0856eed9c31e728875b7651c6a5b, 方式=姓名唯一匹配
2025-07-31 12:50:20,146 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:50:20,146 - INFO -       债务人姓名: '温广强'
2025-07-31 12:50:20,146 - INFO -       合同号: '40152813.0'
2025-07-31 12:50:20,147 - INFO -       身份证号: '2.2018319871120346e+17'
2025-07-31 12:50:20,147 - INFO - 🔍 使用身份证号查询: 2.2018319871120346e+17
2025-07-31 12:50:20,147 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,147 - INFO -    ❌ 身份证号查询无结果: 2.2018319871120346e+17
2025-07-31 12:50:20,148 - INFO - 🔍 使用合同号查询: 40152813.0
2025-07-31 12:50:20,152 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,152 - INFO -    ❌ 合同号查询无结果: 40152813.0
2025-07-31 12:50:20,154 - INFO - ✅ 通过姓名唯一匹配找到债务人: bdd75a54df8799986accbf3f83dd72a5
2025-07-31 12:50:20,155 - INFO -    ✅ 匹配成功: debtor_no=bdd75a54df8799986accbf3f83dd72a5, 方式=姓名唯一匹配
2025-07-31 12:50:20,157 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:50:20,157 - INFO -       债务人姓名: '姜虹'
2025-07-31 12:50:20,158 - INFO -       合同号: '201708174750045.0'
2025-07-31 12:50:20,158 - INFO -       身份证号: '2.1028219930507264e+17'
2025-07-31 12:50:20,158 - INFO - 🔍 使用身份证号查询: 2.1028219930507264e+17
2025-07-31 12:50:20,159 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,159 - INFO -    ❌ 身份证号查询无结果: 2.1028219930507264e+17
2025-07-31 12:50:20,160 - INFO - 🔍 使用合同号查询: 201708174750045.0
2025-07-31 12:50:20,164 - INFO -    数据库查询结果: found=False, debtor_no=
2025-07-31 12:50:20,164 - INFO -    ❌ 合同号查询无结果: 201708174750045.0
2025-07-31 12:50:20,166 - WARNING - ⚠️ 姓名'姜虹'匹配到多个债务人，无法确定唯一性
2025-07-31 12:50:20,166 - WARNING - ❌ 未找到匹配的债务人:
2025-07-31 12:50:20,166 - WARNING -    债务人姓名: '姜虹'
2025-07-31 12:50:20,167 - WARNING -    合同号: '201708174750045.0'
2025-07-31 12:50:20,167 - WARNING -    身份证号: '2.1028219930507264e+17'
2025-07-31 12:50:20,167 - WARNING -    ❌ 匹配失败: 姜虹
2025-07-31 12:50:20,169 - INFO -    ✅ 成功处理 6 条对账明细记录
2025-07-31 12:50:20,170 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:50:20,170 - INFO - 🗂️ 扫描还款对账明细: 6 条记录
2025-07-31 12:50:20,170 - INFO - 处理目录: D:\民生4期回款\7.29 隆丰诉保
2025-07-31 12:50:20,171 - INFO - 🔍 扫描还款对账明细Excel: 7.29 隆丰诉保
2025-07-31 12:50:20,172 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:50:20,173 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:50:20,175 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:50:20,175 - INFO - ✅ 通过姓名找到唯一匹配: 司立民 → debtor_no: 23dcacdbbf322661d6edeaeea815ea35
2025-07-31 12:50:20,175 - INFO - ✅ 通过还款对账明细找到debtor_no: 司立民 → 23dcacdbbf322661d6edeaeea815ea35 (姓名唯一匹配)
2025-07-31 12:50:20,176 - INFO - 📸 开始处理图片: 司立民2718-5000.jpg
2025-07-31 12:50:20,176 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:50:20,176 - INFO - 🔍 开始OCR识别图片: 司立民2718-5000.jpg
2025-07-31 12:50:20,176 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 司立民2718-5000.jpg
2025-07-31 12:50:27,997 - INFO - 🔄 使用文件名中的姓名: 司立民
2025-07-31 12:50:27,997 - INFO - ✅ 豆包AI分析成功: 👤司立民 💳***5496 💰5000.00 ⏰2025-07-29 07:14:19
2025-07-31 12:50:27,997 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:50:27,997 - INFO - 👤 付款方识别成功: 【司立民】
2025-07-31 12:50:27,998 - INFO - 💳 账户号码识别成功: ***5496
2025-07-31 12:50:27,998 - INFO - 💰 还款金额识别成功: 5000.00
2025-07-31 12:50:27,998 - INFO - ⏰ 还款时间识别成功: 2025-07-29 07:14:19 → 2025-07-29
2025-07-31 12:50:27,998 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:50:27,998 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:50:27,999 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:50:27,999 - INFO -    👤 还款人: 【司立民】
2025-07-31 12:50:27,999 - INFO -    💳 还款账号: ***5496
2025-07-31 12:50:27,999 - INFO -    💰 还款金额: 5000.00
2025-07-31 12:50:28,000 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:50:28,000 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:50:28,000 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:50:28,000 - INFO - 🎯 图片处理完成: 司立民2718-5000.jpg
2025-07-31 12:50:28,000 - INFO -    📋 最终结果 - 付款方: 【司立民】, 账户: ***5496, 来源: 【本人还款】
2025-07-31 12:50:28,002 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:50:28,002 - INFO - ✅ 通过姓名找到唯一匹配: 吴春玲 → debtor_no: 932b0856eed9c31e728875b7651c6a5b
2025-07-31 12:50:28,002 - INFO - ✅ 通过还款对账明细找到debtor_no: 吴春玲 → 932b0856eed9c31e728875b7651c6a5b (姓名唯一匹配)
2025-07-31 12:50:28,003 - INFO - 📸 开始处理图片: 吴春玲7020-3000.jpg
2025-07-31 12:50:28,003 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:50:28,003 - INFO - 🔍 开始OCR识别图片: 吴春玲7020-3000.jpg
2025-07-31 12:50:28,004 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 吴春玲7020-3000.jpg
2025-07-31 12:50:38,641 - INFO - ✅ 豆包AI分析成功: 👤吴春丽 💳***7729 💰3000.00 ⏰2025-07-29 09:15:34
2025-07-31 12:50:38,641 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:50:38,641 - INFO - 👤 付款方识别成功: 【吴春丽】
2025-07-31 12:50:38,641 - INFO - 💳 账户号码识别成功: ***7729
2025-07-31 12:50:38,641 - INFO - 💰 还款金额识别成功: 3000.00
2025-07-31 12:50:38,642 - INFO - ⏰ 还款时间识别成功: 2025-07-29 09:15:34 → 2025-07-29
2025-07-31 12:50:38,642 - INFO - 🔄 还款来源: 他人代还 (付款人[吴春丽]与文件名[吴春玲]不匹配)
2025-07-31 12:50:38,642 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:50:38,642 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.29 隆丰诉保\吴春玲7020-3000.jpg
2025-07-31 12:50:38,643 - WARNING -    状态: 部分一致, 置信度: 0.64
2025-07-31 12:50:38,643 - WARNING -    姓名匹配: OCR[吴春丽] vs 文件名[吴春玲] = 0.40
2025-07-31 12:50:38,643 - WARNING -    ⚠️ 姓名不匹配: 相似度匹配(0.67)
2025-07-31 12:50:38,643 - WARNING -    金额匹配: OCR[3000.00] vs 文件名[3000] = 1.00
2025-07-31 12:50:38,643 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:50:38,644 - INFO -    👤 还款人: 【吴春丽】
2025-07-31 12:50:38,644 - INFO -    💳 还款账号: ***7729
2025-07-31 12:50:38,644 - INFO -    💰 还款金额: 3000.00
2025-07-31 12:50:38,644 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:50:38,644 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:50:38,644 - INFO -    ✅ 匹配验证: 部分一致 (置信度: 0.64)
2025-07-31 12:50:38,645 - INFO - 🎯 图片处理完成: 吴春玲7020-3000.jpg
2025-07-31 12:50:38,645 - INFO -    📋 最终结果 - 付款方: 【吴春丽】, 账户: ***7729, 来源: 【他人代还】
2025-07-31 12:50:38,648 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x03\xe8'
2025-07-31 12:50:38,648 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\x01\xd0'
2025-07-31 12:50:38,649 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00J'
2025-07-31 12:50:38,649 - DEBUG - tag: Orientation (274) - type: short (3) - value: b'\x00\x00'
2025-07-31 12:50:38,649 - DEBUG - tag: GPSInfoIFD (34853) - type: long (4) - value: b'\x00\x00\x00\\'
2025-07-31 12:50:38,649 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:50:38,649 - INFO - ✅ 通过姓名找到唯一匹配: 姜虹 → debtor_no: 
2025-07-31 12:50:38,649 - INFO - ✅ 通过还款对账明细找到debtor_no: 姜虹 →  (姓名唯一匹配)
2025-07-31 12:50:38,650 - INFO - 📸 开始处理图片: 姜虹2626-1000.jpg
2025-07-31 12:50:38,650 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:50:38,650 - INFO - 🔍 开始OCR识别图片: 姜虹2626-1000.jpg
2025-07-31 12:50:38,650 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 姜虹2626-1000.jpg
2025-07-31 12:50:38,650 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x03\xe8'
2025-07-31 12:50:38,651 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\x01\xd0'
2025-07-31 12:50:38,651 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00J'
2025-07-31 12:50:38,651 - DEBUG - tag: Orientation (274) - type: short (3) - value: b'\x00\x00'
2025-07-31 12:50:38,651 - DEBUG - tag: GPSInfoIFD (34853) - type: long (4) - value: b'\x00\x00\x00\\'
2025-07-31 12:50:53,673 - INFO - ✅ 豆包AI分析成功: 👤姜虹 💳***26 💰1000.00 ⏰2025-07-29 13:31:35
2025-07-31 12:50:53,673 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:50:53,674 - INFO - 👤 付款方识别成功: 【姜虹】
2025-07-31 12:50:53,674 - INFO - 💳 账户号码识别成功: ***26
2025-07-31 12:50:53,674 - INFO - 💰 还款金额识别成功: 1000.00
2025-07-31 12:50:53,674 - INFO - ⏰ 还款时间识别成功: 2025-07-29 13:31:35 → 2025-07-29
2025-07-31 12:50:53,674 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:50:53,675 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:50:53,675 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:50:53,675 - INFO -    👤 还款人: 【姜虹】
2025-07-31 12:50:53,675 - INFO -    💳 还款账号: ***26
2025-07-31 12:50:53,675 - INFO -    💰 还款金额: 1000.00
2025-07-31 12:50:53,676 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:50:53,676 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:50:53,676 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:50:53,676 - INFO - 🎯 图片处理完成: 姜虹2626-1000.jpg
2025-07-31 12:50:53,676 - INFO -    📋 最终结果 - 付款方: 【姜虹】, 账户: ***26, 来源: 【本人还款】
2025-07-31 12:50:53,678 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:50:53,678 - INFO - ✅ 通过姓名找到唯一匹配: 温广强 → debtor_no: bdd75a54df8799986accbf3f83dd72a5
2025-07-31 12:50:53,678 - INFO - ✅ 通过还款对账明细找到debtor_no: 温广强 → bdd75a54df8799986accbf3f83dd72a5 (姓名唯一匹配)
2025-07-31 12:50:53,678 - INFO - 📸 开始处理图片: 温广强3458-750.jpg
2025-07-31 12:50:53,679 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:50:53,679 - INFO - 🔍 开始OCR识别图片: 温广强3458-750.jpg
2025-07-31 12:50:53,679 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 温广强3458-750.jpg
2025-07-31 12:51:06,609 - INFO - ✅ 豆包AI分析成功: 👤*广辉 💳***1546 💰750.00 ⏰2025-07-29 17:00:00
2025-07-31 12:51:06,609 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:51:06,610 - INFO - ⭐ 检测到带星号的付款方: 【*广辉】，进行星号验证...
2025-07-31 12:51:06,610 - INFO - ✅ 通过姓名找到唯一匹配: 温广强 → debtor_no: bdd75a54df8799986accbf3f83dd72a5
2025-07-31 12:51:06,610 - INFO - ✅ 星号还款人验证通过，将替换: *广辉 → 温广强
2025-07-31 12:51:06,610 - INFO - ✅ 星号付款方替换成功: 【*广辉】 → 【温广强】
2025-07-31 12:51:06,610 - INFO -    替换原因: 满足所有条件：含*号[*广辉]，文件名债务人[温广强]，Excel有debtor_no[bdd75a54df8799986accbf3f83dd72a5]，时间一致[2025-07-29]
2025-07-31 12:51:06,610 - INFO - 💳 账户号码识别成功: ***1546
2025-07-31 12:51:06,610 - INFO - 💰 还款金额识别成功: 750.00
2025-07-31 12:51:06,611 - INFO - ⏰ 还款时间识别成功: 2025-07-29 17:00:00 → 2025-07-29
2025-07-31 12:51:06,611 - INFO - 🔄 还款来源: 他人代还 (付款人[*广辉]与文件名[温广强]不匹配)
2025-07-31 12:51:06,611 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:51:06,611 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.29 隆丰诉保\温广强3458-750.jpg
2025-07-31 12:51:06,611 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:51:06,611 - WARNING -    姓名匹配: OCR[*广辉] vs 文件名[温广强] = 0.00
2025-07-31 12:51:06,611 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.40)
2025-07-31 12:51:06,611 - WARNING -    金额匹配: OCR[750.00] vs 文件名[750] = 1.00
2025-07-31 12:51:06,612 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:51:06,612 - INFO -    👤 还款人: 【温广强】
2025-07-31 12:51:06,612 - INFO -    💳 还款账号: ***1546
2025-07-31 12:51:06,612 - INFO -    💰 还款金额: 750.00
2025-07-31 12:51:06,612 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:51:06,612 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:51:06,612 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:51:06,612 - INFO - 🎯 图片处理完成: 温广强3458-750.jpg
2025-07-31 12:51:06,612 - INFO -    📋 最终结果 - 付款方: 【温广强】, 账户: ***1546, 来源: 【他人代还】
2025-07-31 12:51:06,614 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:51:06,614 - INFO - ✅ 通过姓名找到唯一匹配: 郑东艳 → debtor_no: 6a807ed32916f2a3d031444323ffa917
2025-07-31 12:51:06,614 - INFO - ✅ 通过还款对账明细找到debtor_no: 郑东艳 → 6a807ed32916f2a3d031444323ffa917 (姓名唯一匹配)
2025-07-31 12:51:06,614 - INFO - 📸 开始处理图片: 郑东艳3820-2203.jpg
2025-07-31 12:51:06,614 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:51:06,615 - INFO - 🔍 开始OCR识别图片: 郑东艳3820-2203.jpg
2025-07-31 12:51:06,615 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 郑东艳3820-2203.jpg
2025-07-31 12:51:19,580 - INFO - ✅ 豆包AI分析成功: 👤刘宇晗 💳***2352 💰2203.00 ⏰2025-07-28 18:09:28
2025-07-31 12:51:19,580 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:51:19,580 - INFO - 👤 付款方识别成功: 【刘宇晗】
2025-07-31 12:51:19,581 - INFO - 💳 账户号码识别成功: ***2352
2025-07-31 12:51:19,581 - INFO - 💰 还款金额识别成功: 2203.00
2025-07-31 12:51:19,581 - INFO - ⏰ 还款时间识别成功: 2025-07-28 18:09:28 → 2025-07-28
2025-07-31 12:51:19,581 - INFO - 🔄 还款来源: 他人代还 (付款人[刘宇晗]与文件名[郑东艳]不匹配)
2025-07-31 12:51:19,581 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:51:19,582 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.29 隆丰诉保\郑东艳3820-2203.jpg
2025-07-31 12:51:19,582 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:51:19,582 - WARNING -    姓名匹配: OCR[刘宇晗] vs 文件名[郑东艳] = 0.00
2025-07-31 12:51:19,582 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-31 12:51:19,582 - WARNING -    金额匹配: OCR[2203.00] vs 文件名[2203] = 1.00
2025-07-31 12:51:19,583 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:51:19,583 - INFO -    👤 还款人: 【刘宇晗】
2025-07-31 12:51:19,583 - INFO -    💳 还款账号: ***2352
2025-07-31 12:51:19,583 - INFO -    💰 还款金额: 2203.00
2025-07-31 12:51:19,583 - INFO -    ⏰ 还款时间: 2025-07-28
2025-07-31 12:51:19,584 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:51:19,584 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:51:19,584 - INFO - 🎯 图片处理完成: 郑东艳3820-2203.jpg
2025-07-31 12:51:19,584 - INFO -    📋 最终结果 - 付款方: 【刘宇晗】, 账户: ***2352, 来源: 【他人代还】
2025-07-31 12:51:19,586 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:51:19,586 - INFO - ✅ 通过姓名找到唯一匹配: 鄂晓鹏 → debtor_no: 823be76e7610e3614a8c62045453666b
2025-07-31 12:51:19,586 - INFO - ✅ 通过还款对账明细找到debtor_no: 鄂晓鹏 → 823be76e7610e3614a8c62045453666b (姓名唯一匹配)
2025-07-31 12:51:19,586 - INFO - 📸 开始处理图片: 鄂晓鹏6821-900.jpg
2025-07-31 12:51:19,586 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:51:19,587 - INFO - 🔍 开始OCR识别图片: 鄂晓鹏6821-900.jpg
2025-07-31 12:51:19,587 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 鄂晓鹏6821-900.jpg
2025-07-31 12:51:31,911 - INFO - ✅ 豆包AI分析成功: 👤鄂晓鹏 💳***6742 💰900.00 ⏰2025-07-29 00:00:00
2025-07-31 12:51:31,911 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:51:31,911 - INFO - 👤 付款方识别成功: 【鄂晓鹏】
2025-07-31 12:51:31,911 - INFO - 💳 账户号码识别成功: ***6742
2025-07-31 12:51:31,912 - INFO - 💰 还款金额识别成功: 900.00
2025-07-31 12:51:31,912 - INFO - ⏰ 还款时间识别成功: 2025-07-29 00:00:00 → 2025-07-29
2025-07-31 12:51:31,912 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:51:31,912 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:51:31,913 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:51:31,913 - INFO -    👤 还款人: 【鄂晓鹏】
2025-07-31 12:51:31,913 - INFO -    💳 还款账号: ***6742
2025-07-31 12:51:31,913 - INFO -    💰 还款金额: 900.00
2025-07-31 12:51:31,913 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:51:31,914 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:51:31,914 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:51:31,914 - INFO - 🎯 图片处理完成: 鄂晓鹏6821-900.jpg
2025-07-31 12:51:31,914 - INFO -    📋 最终结果 - 付款方: 【鄂晓鹏】, 账户: ***6742, 来源: 【本人还款】
2025-07-31 12:51:31,914 - INFO - 📊 开始验证文件夹 '7.29 隆丰诉保' 的数据一致性...
2025-07-31 12:51:31,937 - ERROR - ❌ 文件夹 '7.29 隆丰诉保' 数据一致性验证失败!
2025-07-31 12:51:31,937 - ERROR -    Excel中的人员未在图片中找到: 郑东艳, 吴春玲
2025-07-31 12:51:31,937 - ERROR -    图片中的人员在Excel中未找到: 刘宇晗, 吴春丽
2025-07-31 12:51:31,938 - INFO - 🔍 开始验证OCR识别结果与Excel对比...
2025-07-31 12:51:31,960 - INFO - 📋 读取还款对账明细文件: 还款对账明细（合作方用）(1).xlsx
2025-07-31 12:51:31,961 - WARNING - ❌ 还款人未在Excel中找到: 吴春丽
2025-07-31 12:51:31,961 - WARNING - ❌ 还款人未在Excel中找到: 刘宇晗
2025-07-31 12:51:31,961 - INFO - 📊 Excel验证完成 - 匹配: 4, 未匹配: 2
2025-07-31 12:51:31,962 - WARNING - ⚠️  Excel验证发现问题:
2025-07-31 12:51:31,962 - WARNING -    有 2 个还款人未在Excel中找到
2025-07-31 12:51:31,962 - INFO - 目录 7.29 隆丰诉保 处理完成，共处理 6 个图片
2025-07-31 12:51:31,966 - INFO - 写入主要处理结果: 6 条记录
2025-07-31 12:51:31,971 - INFO - 写入还款对账明细: 6 条记录
2025-07-31 12:51:31,971 - INFO - 🔍 开始执行三重比对分析...
2025-07-31 12:51:31,972 - INFO - 📊 处理结果明细包含 5 个不同债务人
2025-07-31 12:51:31,972 - INFO - 📊 还款对账明细包含 5 个不同债务人
2025-07-31 12:51:31,972 - INFO - 🔍 执行分析1：债务人姓名匹配分析
2025-07-31 12:51:31,972 - INFO - 🔍 执行分析2：还款金额匹配分析
2025-07-31 12:51:31,973 - INFO - 🔍 执行分析3：结清验证分析
2025-07-31 12:51:31,973 - INFO - ✅ 三重比对分析完成
2025-07-31 12:51:31,973 - INFO -    📊 统计结果: {'还款对账明细债务人数': 5, '处理结果明细债务人数': 5, '共同债务人数': 5, '仅在还款对账明细中': 0, '仅在处理结果明细中': 0, '金额不一致债务人数': 0, '需要关注的结清案例': 0}
2025-07-31 12:51:31,976 - INFO - 写入金额匹配分析: 5 条记录
2025-07-31 12:51:31,979 - INFO - 写入结清验证分析: 1 条记录
2025-07-31 12:51:31,980 - INFO - 写入比对分析摘要: 7 条记录
2025-07-31 12:51:31,980 - INFO - 开始生成人名统计，共有 6 条处理结果
2025-07-31 12:51:31,980 - INFO - 处理完成，共处理 1 个目录，生成 6 个人员统计
2025-07-31 12:51:31,980 - INFO - 处理的目录列表: ['7.29 隆丰诉保']
2025-07-31 12:51:32,049 - WARNING - 未找到债务人信息: 姜虹
2025-07-31 12:51:32,125 - INFO - 最终生成 6 个人员统计记录
2025-07-31 12:51:32,127 - INFO - 写入人名统计信息: 6 条记录
2025-07-31 12:51:32,128 - INFO - 🎨 开始应用Excel格式设置...
2025-07-31 12:51:32,128 - INFO -    文件路径列索引: 2
2025-07-31 12:51:32,128 - INFO -    总行数: 7
2025-07-31 12:51:32,128 - INFO - 🔗 正在设置文件路径超链接...
2025-07-31 12:51:32,128 - INFO - ✅ 文件路径超链接设置完成，共设置 6 个超链接
2025-07-31 12:51:32,129 - INFO - 🎨 正在设置行颜色标记...
2025-07-31 12:51:32,129 - INFO - ✅ '处理结果明细'sheet格式设置完成
2025-07-31 12:51:32,129 - INFO - 🔴 正在设置结清验证状态列格式...
2025-07-31 12:51:32,129 - INFO - ✅ 结清验证状态列格式化完成：0 个单元格标红
2025-07-31 12:51:32,130 - INFO - ✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红
2025-07-31 12:51:32,130 - INFO - 📍 找到关键列 - 图片文件数量: 13, debtor_no: 9, 类型: 6
2025-07-31 12:51:32,131 - INFO - ✅ 还款对账明细格式化完成
2025-07-31 12:51:32,131 - INFO -    🔴 图片文件缺失标红: 0 行
2025-07-31 12:51:32,131 - INFO -    🟠 结清验证问题标橙: 0 行
2025-07-31 12:51:32,133 - INFO - 写入目录统计信息: 1 条记录
2025-07-31 12:51:32,134 - INFO - 写入处理摘要信息
2025-07-31 12:51:32,134 - INFO - ⚠️  共有 1 个记录未找到债务人信息
2025-07-31 12:51:32,134 - ERROR - ============================================================
2025-07-31 12:51:32,134 - ERROR - ❌ 以下记录未找到债务人信息（已在Excel中标红）:
2025-07-31 12:51:32,134 - ERROR - ============================================================
2025-07-31 12:51:32,134 - ERROR -   1. 【姜虹】
2025-07-31 12:51:32,135 - ERROR -      文件夹: 7.29 隆丰诉保
2025-07-31 12:51:32,135 - ERROR -      身份证号: 210282199305072640
2025-07-31 12:51:32,135 - ERROR -      合同号: 201708174750045
2025-07-31 12:51:32,135 - ERROR - 
2025-07-31 12:51:32,135 - ERROR - ============================================================
2025-07-31 12:51:32,136 - ERROR - 💡 建议检查以上 1 个记录的身份证号和合同号是否正确
2025-07-31 12:51:32,136 - ERROR - 💡 或确认这些债务人是否在债务人明细Excel文件中
2025-07-31 12:51:32,136 - ERROR - ============================================================
2025-07-31 12:51:32,161 - INFO - 增强版Excel文件保存成功: 7.29 隆丰诉保_还款凭证解析_20250731_125131.xlsx
2025-07-31 12:51:32,161 - INFO - ✅ 成功生成Excel文件: 7.29 隆丰诉保_还款凭证解析_20250731_125131.xlsx
2025-07-31 12:51:32,162 - INFO - ✅ 子文件夹 7.29 隆丰诉保 处理完成，生成文件: 7.29 隆丰诉保_还款凭证解析_20250731_125131.xlsx
2025-07-31 12:51:32,162 - INFO - ================================================================================
2025-07-31 12:51:32,162 - INFO - 🗂️  开始处理子文件夹: 7.30 隆丰电催
2025-07-31 12:51:32,163 - INFO - ✅ 目录 7.30 隆丰电催 中Excel文件验证通过: 还款对账明细（合作方用）.xlsx
2025-07-31 12:51:32,163 - INFO - 目录 7.30 隆丰电催 Excel验证成功
2025-07-31 12:51:32,163 - INFO - 🔍 扫描还款对账明细Excel: 7.30 隆丰电催
2025-07-31 12:51:32,164 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:51:32,182 - INFO -    📋 Excel文件包含 16 行数据
2025-07-31 12:51:32,183 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号', '还款金额': '还款金额', '类型': '还款类型（部分还款/结清）'}
2025-07-31 12:51:32,183 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,183 - INFO -       债务人姓名: '孙斌'
2025-07-31 12:51:32,184 - INFO -       合同号: '28042431'
2025-07-31 12:51:32,184 - INFO -       身份证号: ''
2025-07-31 12:51:32,184 - INFO - 🔍 使用合同号查询: 28042431
2025-07-31 12:51:32,190 - INFO -    数据库查询结果: found=True, debtor_no=cc851c85b37e06876847564829991013
2025-07-31 12:51:32,190 - INFO - ✅ 通过合同号找到债务人: cc851c85b37e06876847564829991013
2025-07-31 12:51:32,190 - INFO -    ✅ 匹配成功: debtor_no=cc851c85b37e06876847564829991013, 方式=合同号
2025-07-31 12:51:32,191 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,191 - INFO -       债务人姓名: '宛义'
2025-07-31 12:51:32,191 - INFO -       合同号: '31219532'
2025-07-31 12:51:32,192 - INFO -       身份证号: ''
2025-07-31 12:51:32,192 - INFO - 🔍 使用合同号查询: 31219532
2025-07-31 12:51:32,193 - INFO -    数据库查询结果: found=True, debtor_no=5df1f25a98ddad31f8efba3b5503f98a
2025-07-31 12:51:32,193 - INFO - ✅ 通过合同号找到债务人: 5df1f25a98ddad31f8efba3b5503f98a
2025-07-31 12:51:32,193 - INFO -    ✅ 匹配成功: debtor_no=5df1f25a98ddad31f8efba3b5503f98a, 方式=合同号
2025-07-31 12:51:32,194 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,194 - INFO -       债务人姓名: '于明'
2025-07-31 12:51:32,194 - INFO -       合同号: '27697875'
2025-07-31 12:51:32,194 - INFO -       身份证号: ''
2025-07-31 12:51:32,195 - INFO - 🔍 使用合同号查询: 27697875
2025-07-31 12:51:32,196 - INFO -    数据库查询结果: found=True, debtor_no=273f7d8e4cef1d2e9f713deed83bb3d1
2025-07-31 12:51:32,197 - INFO - ✅ 通过合同号找到债务人: 273f7d8e4cef1d2e9f713deed83bb3d1
2025-07-31 12:51:32,197 - INFO -    ✅ 匹配成功: debtor_no=273f7d8e4cef1d2e9f713deed83bb3d1, 方式=合同号
2025-07-31 12:51:32,198 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,198 - INFO -       债务人姓名: '韩波'
2025-07-31 12:51:32,198 - INFO -       合同号: '26685650'
2025-07-31 12:51:32,198 - INFO -       身份证号: ''
2025-07-31 12:51:32,198 - INFO - 🔍 使用合同号查询: 26685650
2025-07-31 12:51:32,199 - INFO -    数据库查询结果: found=True, debtor_no=96709d2b20440e53e29de465a2473027
2025-07-31 12:51:32,199 - INFO - ✅ 通过合同号找到债务人: 96709d2b20440e53e29de465a2473027
2025-07-31 12:51:32,200 - INFO -    ✅ 匹配成功: debtor_no=96709d2b20440e53e29de465a2473027, 方式=合同号
2025-07-31 12:51:32,200 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,200 - INFO -       债务人姓名: '杨婷'
2025-07-31 12:51:32,200 - INFO -       合同号: '27111285'
2025-07-31 12:51:32,201 - INFO -       身份证号: ''
2025-07-31 12:51:32,201 - INFO - 🔍 使用合同号查询: 27111285
2025-07-31 12:51:32,203 - INFO -    数据库查询结果: found=True, debtor_no=b15ae9f40070669105226af9226031ad
2025-07-31 12:51:32,203 - INFO - ✅ 通过合同号找到债务人: b15ae9f40070669105226af9226031ad
2025-07-31 12:51:32,203 - INFO -    ✅ 匹配成功: debtor_no=b15ae9f40070669105226af9226031ad, 方式=合同号
2025-07-31 12:51:32,204 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,204 - INFO -       债务人姓名: '康嫚丽'
2025-07-31 12:51:32,205 - INFO -       合同号: '18606845'
2025-07-31 12:51:32,205 - INFO -       身份证号: ''
2025-07-31 12:51:32,205 - INFO - 🔍 使用合同号查询: 18606845
2025-07-31 12:51:32,209 - INFO -    数据库查询结果: found=True, debtor_no=398b41251b11abfd579437e6464d556a
2025-07-31 12:51:32,209 - INFO - ✅ 通过合同号找到债务人: 398b41251b11abfd579437e6464d556a
2025-07-31 12:51:32,210 - INFO -    ✅ 匹配成功: debtor_no=398b41251b11abfd579437e6464d556a, 方式=合同号
2025-07-31 12:51:32,211 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,211 - INFO -       债务人姓名: '郑光'
2025-07-31 12:51:32,211 - INFO -       合同号: '21189827'
2025-07-31 12:51:32,212 - INFO -       身份证号: ''
2025-07-31 12:51:32,212 - INFO - 🔍 使用合同号查询: 21189827
2025-07-31 12:51:32,216 - INFO -    数据库查询结果: found=True, debtor_no=91196f77579c29e12d57ddc4b71b59d4
2025-07-31 12:51:32,216 - INFO - ✅ 通过合同号找到债务人: 91196f77579c29e12d57ddc4b71b59d4
2025-07-31 12:51:32,217 - INFO -    ✅ 匹配成功: debtor_no=91196f77579c29e12d57ddc4b71b59d4, 方式=合同号
2025-07-31 12:51:32,218 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,218 - INFO -       债务人姓名: '苑子龙'
2025-07-31 12:51:32,218 - INFO -       合同号: '35054071'
2025-07-31 12:51:32,219 - INFO -       身份证号: ''
2025-07-31 12:51:32,219 - INFO - 🔍 使用合同号查询: 35054071
2025-07-31 12:51:32,223 - INFO -    数据库查询结果: found=True, debtor_no=459e47d5a0d13e30fbf761f2d1e0c371
2025-07-31 12:51:32,223 - INFO - ✅ 通过合同号找到债务人: 459e47d5a0d13e30fbf761f2d1e0c371
2025-07-31 12:51:32,223 - INFO -    ✅ 匹配成功: debtor_no=459e47d5a0d13e30fbf761f2d1e0c371, 方式=合同号
2025-07-31 12:51:32,224 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,224 - INFO -       债务人姓名: '郭丞旭'
2025-07-31 12:51:32,224 - INFO -       合同号: '27605587'
2025-07-31 12:51:32,225 - INFO -       身份证号: ''
2025-07-31 12:51:32,225 - INFO - 🔍 使用合同号查询: 27605587
2025-07-31 12:51:32,227 - INFO -    数据库查询结果: found=True, debtor_no=d0e0a4ad5a89f35f3fa8afe6e0633cfd
2025-07-31 12:51:32,227 - INFO - ✅ 通过合同号找到债务人: d0e0a4ad5a89f35f3fa8afe6e0633cfd
2025-07-31 12:51:32,228 - INFO -    ✅ 匹配成功: debtor_no=d0e0a4ad5a89f35f3fa8afe6e0633cfd, 方式=合同号
2025-07-31 12:51:32,228 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,228 - INFO -       债务人姓名: '丁梓宸'
2025-07-31 12:51:32,229 - INFO -       合同号: '10861118'
2025-07-31 12:51:32,229 - INFO -       身份证号: ''
2025-07-31 12:51:32,229 - INFO - 🔍 使用合同号查询: 10861118
2025-07-31 12:51:32,232 - INFO -    数据库查询结果: found=True, debtor_no=93489ad8837b5b1fb76ab53ed4ca0bcc
2025-07-31 12:51:32,233 - INFO - ✅ 通过合同号找到债务人: 93489ad8837b5b1fb76ab53ed4ca0bcc
2025-07-31 12:51:32,233 - INFO -    ✅ 匹配成功: debtor_no=93489ad8837b5b1fb76ab53ed4ca0bcc, 方式=合同号
2025-07-31 12:51:32,233 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,234 - INFO -       债务人姓名: '李慧'
2025-07-31 12:51:32,234 - INFO -       合同号: '20401426'
2025-07-31 12:51:32,234 - INFO -       身份证号: ''
2025-07-31 12:51:32,234 - INFO - 🔍 使用合同号查询: 20401426
2025-07-31 12:51:32,239 - INFO -    数据库查询结果: found=True, debtor_no=a78878fa94cbea5ffe3bd6d369f7fe3d
2025-07-31 12:51:32,239 - INFO - ✅ 通过合同号找到债务人: a78878fa94cbea5ffe3bd6d369f7fe3d
2025-07-31 12:51:32,239 - INFO -    ✅ 匹配成功: debtor_no=a78878fa94cbea5ffe3bd6d369f7fe3d, 方式=合同号
2025-07-31 12:51:32,240 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,240 - INFO -       债务人姓名: '王福军'
2025-07-31 12:51:32,241 - INFO -       合同号: '38876733'
2025-07-31 12:51:32,241 - INFO -       身份证号: ''
2025-07-31 12:51:32,241 - INFO - 🔍 使用合同号查询: 38876733
2025-07-31 12:51:32,245 - INFO -    数据库查询结果: found=True, debtor_no=c2a7240643b5ed1c9df209bedf94cd7b
2025-07-31 12:51:32,246 - INFO - ✅ 通过合同号找到债务人: c2a7240643b5ed1c9df209bedf94cd7b
2025-07-31 12:51:32,246 - INFO -    ✅ 匹配成功: debtor_no=c2a7240643b5ed1c9df209bedf94cd7b, 方式=合同号
2025-07-31 12:51:32,247 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,247 - INFO -       债务人姓名: '李智'
2025-07-31 12:51:32,247 - INFO -       合同号: '20876254'
2025-07-31 12:51:32,247 - INFO -       身份证号: ''
2025-07-31 12:51:32,247 - INFO - 🔍 使用合同号查询: 20876254
2025-07-31 12:51:32,250 - INFO -    数据库查询结果: found=True, debtor_no=00a49c356f253a1039f48647317ea1f6
2025-07-31 12:51:32,250 - INFO - ✅ 通过合同号找到债务人: 00a49c356f253a1039f48647317ea1f6
2025-07-31 12:51:32,251 - INFO -    ✅ 匹配成功: debtor_no=00a49c356f253a1039f48647317ea1f6, 方式=合同号
2025-07-31 12:51:32,251 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,251 - INFO -       债务人姓名: '韩旭'
2025-07-31 12:51:32,251 - INFO -       合同号: '35494437'
2025-07-31 12:51:32,252 - INFO -       身份证号: ''
2025-07-31 12:51:32,252 - INFO - 🔍 使用合同号查询: 35494437
2025-07-31 12:51:32,256 - INFO -    数据库查询结果: found=True, debtor_no=ff67bb5c37497015db383f94b5132cc5
2025-07-31 12:51:32,256 - INFO - ✅ 通过合同号找到债务人: ff67bb5c37497015db383f94b5132cc5
2025-07-31 12:51:32,256 - INFO -    ✅ 匹配成功: debtor_no=ff67bb5c37497015db383f94b5132cc5, 方式=合同号
2025-07-31 12:51:32,257 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,258 - INFO -       债务人姓名: '徐祝'
2025-07-31 12:51:32,258 - INFO -       合同号: '35670982'
2025-07-31 12:51:32,258 - INFO -       身份证号: ''
2025-07-31 12:51:32,258 - INFO - 🔍 使用合同号查询: 35670982
2025-07-31 12:51:32,261 - INFO -    数据库查询结果: found=True, debtor_no=68c27962c64ef173459ee362261334a8
2025-07-31 12:51:32,261 - INFO - ✅ 通过合同号找到债务人: 68c27962c64ef173459ee362261334a8
2025-07-31 12:51:32,261 - INFO -    ✅ 匹配成功: debtor_no=68c27962c64ef173459ee362261334a8, 方式=合同号
2025-07-31 12:51:32,262 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:51:32,262 - INFO -       债务人姓名: '王红英'
2025-07-31 12:51:32,263 - INFO -       合同号: '22620268'
2025-07-31 12:51:32,263 - INFO -       身份证号: ''
2025-07-31 12:51:32,263 - INFO - 🔍 使用合同号查询: 22620268
2025-07-31 12:51:32,265 - INFO -    数据库查询结果: found=True, debtor_no=4e5d73e2b5fc658ba4891d750f70034d
2025-07-31 12:51:32,265 - INFO - ✅ 通过合同号找到债务人: 4e5d73e2b5fc658ba4891d750f70034d
2025-07-31 12:51:32,265 - INFO -    ✅ 匹配成功: debtor_no=4e5d73e2b5fc658ba4891d750f70034d, 方式=合同号
2025-07-31 12:51:32,266 - INFO -    ✅ 成功处理 16 条对账明细记录
2025-07-31 12:51:32,266 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:51:32,266 - INFO - 🗂️ 扫描还款对账明细: 16 条记录
2025-07-31 12:51:32,267 - INFO - 处理目录: D:\民生4期回款\7.30 隆丰电催
2025-07-31 12:51:32,267 - INFO - 🔍 扫描还款对账明细Excel: 7.30 隆丰电催
2025-07-31 12:51:32,268 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:51:32,268 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:51:32,271 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:51:32,271 - INFO - ✅ 通过姓名找到唯一匹配: 丁梓宸 → debtor_no: 93489ad8837b5b1fb76ab53ed4ca0bcc
2025-07-31 12:51:32,272 - INFO - ✅ 通过还款对账明细找到debtor_no: 丁梓宸 → 93489ad8837b5b1fb76ab53ed4ca0bcc (姓名唯一匹配)
2025-07-31 12:51:32,272 - INFO - 📸 开始处理图片: 丁梓宸600（身份证尾号3036）.jpg
2025-07-31 12:51:32,272 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:51:32,273 - INFO - 🔍 开始OCR识别图片: 丁梓宸600（身份证尾号3036）.jpg
2025-07-31 12:51:32,273 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 丁梓宸600（身份证尾号3036）.jpg
2025-07-31 12:51:41,984 - INFO - ✅ 豆包AI分析成功: 👤*梓宸 💳***1158 💰600.00 ⏰2025-07-30 12:27:00
2025-07-31 12:51:41,985 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:51:41,985 - INFO - ⭐ 检测到带星号的付款方: 【*梓宸】，进行星号验证...
2025-07-31 12:51:41,986 - INFO - ✅ 通过姓名找到唯一匹配: 丁梓宸 → debtor_no: 93489ad8837b5b1fb76ab53ed4ca0bcc
2025-07-31 12:51:41,986 - INFO - ✅ 星号还款人验证通过，将替换: *梓宸 → 丁梓宸
2025-07-31 12:51:41,986 - INFO - ✅ 星号付款方替换成功: 【*梓宸】 → 【丁梓宸】
2025-07-31 12:51:41,987 - INFO -    替换原因: 满足所有条件：含*号[*梓宸]，文件名债务人[丁梓宸]，Excel有debtor_no[93489ad8837b5b1fb76ab53ed4ca0bcc]，时间一致[2025-07-30]
2025-07-31 12:51:41,987 - INFO - 💳 账户号码识别成功: ***1158
2025-07-31 12:51:41,987 - INFO - 💰 还款金额识别成功: 600.00
2025-07-31 12:51:41,987 - INFO - ⏰ 还款时间识别成功: 2025-07-30 12:27:00 → 2025-07-30
2025-07-31 12:51:41,987 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:51:41,988 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:51:41,988 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.30 隆丰电催\丁梓宸600（身份证尾号3036）.jpg
2025-07-31 12:51:41,988 - WARNING -    状态: 不一致, 置信度: 0.56
2025-07-31 12:51:41,988 - WARNING -    姓名匹配: OCR[*梓宸] vs 文件名[丁梓宸] = 0.80
2025-07-31 12:51:41,988 - WARNING -    金额匹配: OCR[600.00] vs 文件名[3036] = 0.20
2025-07-31 12:51:41,989 - WARNING -    ⚠️ 金额不匹配: 数值差异(80.24%)
2025-07-31 12:51:41,989 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:51:41,989 - INFO -    👤 还款人: 【丁梓宸】
2025-07-31 12:51:41,989 - INFO -    💳 还款账号: ***1158
2025-07-31 12:51:41,989 - INFO -    💰 还款金额: 600.00
2025-07-31 12:51:41,989 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:51:41,990 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:51:41,990 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.56)
2025-07-31 12:51:41,990 - INFO - 🎯 图片处理完成: 丁梓宸600（身份证尾号3036）.jpg
2025-07-31 12:51:41,990 - INFO -    📋 最终结果 - 付款方: 【丁梓宸】, 账户: ***1158, 来源: 【本人还款】
2025-07-31 12:51:41,992 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:51:41,992 - INFO - ✅ 通过姓名找到唯一匹配: 于明 → debtor_no: 273f7d8e4cef1d2e9f713deed83bb3d1
2025-07-31 12:51:41,992 - INFO - ✅ 通过还款对账明细找到debtor_no: 于明 → 273f7d8e4cef1d2e9f713deed83bb3d1 (姓名唯一匹配)
2025-07-31 12:51:41,992 - INFO - 📸 开始处理图片: 于明500.jpg
2025-07-31 12:51:41,992 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:51:41,992 - INFO - 🔍 开始OCR识别图片: 于明500.jpg
2025-07-31 12:51:41,993 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 于明500.jpg
2025-07-31 12:51:56,739 - INFO - 🔄 使用文件名中的姓名: 于明
2025-07-31 12:51:56,740 - INFO - ✅ 豆包AI分析成功: 👤于明 💳***2250 💰500.00 ⏰2025-07-29 17:13:26
2025-07-31 12:51:56,740 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:51:56,740 - INFO - 👤 付款方识别成功: 【于明】
2025-07-31 12:51:56,740 - INFO - 💳 账户号码识别成功: ***2250
2025-07-31 12:51:56,740 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:51:56,741 - INFO - ⏰ 还款时间识别成功: 2025-07-29 17:13:26 → 2025-07-29
2025-07-31 12:51:56,741 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:51:56,741 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:51:56,741 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:51:56,741 - INFO -    👤 还款人: 【于明】
2025-07-31 12:51:56,741 - INFO -    💳 还款账号: ***2250
2025-07-31 12:51:56,741 - INFO -    💰 还款金额: 500.00
2025-07-31 12:51:56,741 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:51:56,741 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:51:56,742 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:51:56,742 - INFO - 🎯 图片处理完成: 于明500.jpg
2025-07-31 12:51:56,742 - INFO -    📋 最终结果 - 付款方: 【于明】, 账户: ***2250, 来源: 【本人还款】
2025-07-31 12:51:56,743 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:51:56,743 - INFO - ✅ 通过姓名找到唯一匹配: 孙斌 → debtor_no: cc851c85b37e06876847564829991013
2025-07-31 12:51:56,743 - INFO - ✅ 通过还款对账明细找到debtor_no: 孙斌 → cc851c85b37e06876847564829991013 (姓名唯一匹配)
2025-07-31 12:51:56,744 - INFO - 📸 开始处理图片: 孙斌500.jpg
2025-07-31 12:51:56,744 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:51:56,744 - INFO - 🔍 开始OCR识别图片: 孙斌500.jpg
2025-07-31 12:51:56,744 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 孙斌500.jpg
2025-07-31 12:52:20,165 - INFO - ✅ 豆包AI分析成功: 👤孙斌 💳***2384 💰500.00 ⏰2025-07-29 16:38:00
2025-07-31 12:52:20,165 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:52:20,165 - INFO - 👤 付款方识别成功: 【孙斌】
2025-07-31 12:52:20,165 - INFO - 💳 账户号码识别成功: ***2384
2025-07-31 12:52:20,165 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:52:20,166 - INFO - ⏰ 还款时间识别成功: 2025-07-29 16:38:00 → 2025-07-29
2025-07-31 12:52:20,166 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:52:20,166 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:52:20,166 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:52:20,167 - INFO -    👤 还款人: 【孙斌】
2025-07-31 12:52:20,167 - INFO -    💳 还款账号: ***2384
2025-07-31 12:52:20,167 - INFO -    💰 还款金额: 500.00
2025-07-31 12:52:20,167 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:52:20,168 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:52:20,168 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:52:20,169 - INFO - 🎯 图片处理完成: 孙斌500.jpg
2025-07-31 12:52:20,169 - INFO -    📋 最终结果 - 付款方: 【孙斌】, 账户: ***2384, 来源: 【本人还款】
2025-07-31 12:52:20,171 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:52:20,171 - INFO - ✅ 通过姓名找到唯一匹配: 宛义 → debtor_no: 5df1f25a98ddad31f8efba3b5503f98a
2025-07-31 12:52:20,171 - INFO - ✅ 通过还款对账明细找到debtor_no: 宛义 → 5df1f25a98ddad31f8efba3b5503f98a (姓名唯一匹配)
2025-07-31 12:52:20,172 - INFO - 📸 开始处理图片: 宛义500.jpg
2025-07-31 12:52:20,172 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:52:20,172 - INFO - 🔍 开始OCR识别图片: 宛义500.jpg
2025-07-31 12:52:20,172 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 宛义500.jpg
2025-07-31 12:52:26,683 - INFO - ✅ 豆包AI分析成功: 👤宛义 💳***1937 💰500.00 ⏰2025-07-29 20:27:27
2025-07-31 12:52:26,684 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:52:26,684 - INFO - 👤 付款方识别成功: 【宛义】
2025-07-31 12:52:26,685 - INFO - 💳 账户号码识别成功: ***1937
2025-07-31 12:52:26,685 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:52:26,685 - INFO - ⏰ 还款时间识别成功: 2025-07-29 20:27:27 → 2025-07-29
2025-07-31 12:52:26,685 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:52:26,686 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:52:26,686 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:52:26,686 - INFO -    👤 还款人: 【宛义】
2025-07-31 12:52:26,687 - INFO -    💳 还款账号: ***1937
2025-07-31 12:52:26,687 - INFO -    💰 还款金额: 500.00
2025-07-31 12:52:26,687 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:52:26,687 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:52:26,688 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:52:26,688 - INFO - 🎯 图片处理完成: 宛义500.jpg
2025-07-31 12:52:26,688 - INFO -    📋 最终结果 - 付款方: 【宛义】, 账户: ***1937, 来源: 【本人还款】
2025-07-31 12:52:26,690 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:52:26,690 - INFO - ✅ 通过姓名找到唯一匹配: 康嫚丽 → debtor_no: 398b41251b11abfd579437e6464d556a
2025-07-31 12:52:26,690 - INFO - ✅ 通过还款对账明细找到debtor_no: 康嫚丽 → 398b41251b11abfd579437e6464d556a (姓名唯一匹配)
2025-07-31 12:52:26,690 - INFO - 📸 开始处理图片: 康嫚丽1000.jpg
2025-07-31 12:52:26,691 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:52:26,691 - INFO - 🔍 开始OCR识别图片: 康嫚丽1000.jpg
2025-07-31 12:52:26,691 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 康嫚丽1000.jpg
2025-07-31 12:52:36,720 - INFO - ✅ 豆包AI分析成功: 👤于爱荣 💳***4990 💰1000.00 ⏰2025-07-30 06:49:08
2025-07-31 12:52:36,720 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:52:36,721 - INFO - 👤 付款方识别成功: 【于爱荣】
2025-07-31 12:52:36,721 - INFO - 💳 账户号码识别成功: ***4990
2025-07-31 12:52:36,721 - INFO - 💰 还款金额识别成功: 1000.00
2025-07-31 12:52:36,721 - INFO - ⏰ 还款时间识别成功: 2025-07-30 06:49:08 → 2025-07-30
2025-07-31 12:52:36,721 - INFO - 🔄 还款来源: 他人代还 (付款人[于爱荣]与文件名[康嫚丽]不匹配)
2025-07-31 12:52:36,722 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:52:36,722 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.30 隆丰电催\康嫚丽1000.jpg
2025-07-31 12:52:36,722 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:52:36,722 - WARNING -    姓名匹配: OCR[于爱荣] vs 文件名[康嫚丽] = 0.00
2025-07-31 12:52:36,722 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-07-31 12:52:36,723 - WARNING -    金额匹配: OCR[1000.00] vs 文件名[1000] = 1.00
2025-07-31 12:52:36,723 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:52:36,723 - INFO -    👤 还款人: 【于爱荣】
2025-07-31 12:52:36,723 - INFO -    💳 还款账号: ***4990
2025-07-31 12:52:36,723 - INFO -    💰 还款金额: 1000.00
2025-07-31 12:52:36,723 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:52:36,723 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:52:36,724 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:52:36,724 - INFO - 🎯 图片处理完成: 康嫚丽1000.jpg
2025-07-31 12:52:36,724 - INFO -    📋 最终结果 - 付款方: 【于爱荣】, 账户: ***4990, 来源: 【他人代还】
2025-07-31 12:52:36,725 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:52:36,726 - INFO - ✅ 通过姓名找到唯一匹配: 徐祝 → debtor_no: 68c27962c64ef173459ee362261334a8
2025-07-31 12:52:36,726 - INFO - ✅ 通过还款对账明细找到debtor_no: 徐祝 → 68c27962c64ef173459ee362261334a8 (姓名唯一匹配)
2025-07-31 12:52:36,726 - INFO - 📸 开始处理图片: 徐祝790.jpg
2025-07-31 12:52:36,726 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:52:36,726 - INFO - 🔍 开始OCR识别图片: 徐祝790.jpg
2025-07-31 12:52:36,727 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 徐祝790.jpg
2025-07-31 12:52:50,558 - INFO - 🔄 使用文件名中的姓名: 徐祝
2025-07-31 12:52:50,558 - INFO - ✅ 豆包AI分析成功: 👤徐祝 💳***9066 💰790.00 ⏰2025-07-30 15:14:00
2025-07-31 12:52:50,559 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:52:50,559 - INFO - 👤 付款方识别成功: 【徐祝】
2025-07-31 12:52:50,559 - INFO - 💳 账户号码识别成功: ***9066
2025-07-31 12:52:50,559 - INFO - 💰 还款金额识别成功: 790.00
2025-07-31 12:52:50,560 - INFO - ⏰ 还款时间识别成功: 2025-07-30 15:14:00 → 2025-07-30
2025-07-31 12:52:50,560 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:52:50,560 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:52:50,561 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:52:50,561 - INFO -    👤 还款人: 【徐祝】
2025-07-31 12:52:50,561 - INFO -    💳 还款账号: ***9066
2025-07-31 12:52:50,561 - INFO -    💰 还款金额: 790.00
2025-07-31 12:52:50,562 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:52:50,562 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:52:50,562 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:52:50,562 - INFO - 🎯 图片处理完成: 徐祝790.jpg
2025-07-31 12:52:50,562 - INFO -    📋 最终结果 - 付款方: 【徐祝】, 账户: ***9066, 来源: 【本人还款】
2025-07-31 12:52:50,564 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:52:50,564 - INFO - ✅ 通过姓名找到唯一匹配: 李慧 → debtor_no: a78878fa94cbea5ffe3bd6d369f7fe3d
2025-07-31 12:52:50,564 - INFO - ✅ 通过还款对账明细找到debtor_no: 李慧 → a78878fa94cbea5ffe3bd6d369f7fe3d (姓名唯一匹配)
2025-07-31 12:52:50,565 - INFO - 📸 开始处理图片: 李慧500.jpg
2025-07-31 12:52:50,565 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:52:50,565 - INFO - 🔍 开始OCR识别图片: 李慧500.jpg
2025-07-31 12:52:50,566 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李慧500.jpg
2025-07-31 12:53:07,084 - INFO - 🔄 使用文件名中的姓名: 李慧
2025-07-31 12:53:07,085 - INFO - ✅ 豆包AI分析成功: 👤李慧 💳***8018 💰500.00 ⏰
2025-07-31 12:53:07,085 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:53:07,085 - INFO - 👤 付款方识别成功: 【李慧】
2025-07-31 12:53:07,086 - INFO - 💳 账户号码识别成功: ***8018
2025-07-31 12:53:07,086 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:53:07,086 - WARNING - ⏰ OCR未识别到还款时间，保持为空
2025-07-31 12:53:07,086 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:53:07,086 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:53:07,087 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:53:07,087 - INFO -    👤 还款人: 【李慧】
2025-07-31 12:53:07,087 - INFO -    💳 还款账号: ***8018
2025-07-31 12:53:07,087 - INFO -    💰 还款金额: 500.00
2025-07-31 12:53:07,087 - INFO -    ⏰ 还款时间: 
2025-07-31 12:53:07,088 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:53:07,088 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:53:07,088 - INFO - 🎯 图片处理完成: 李慧500.jpg
2025-07-31 12:53:07,088 - INFO -    📋 最终结果 - 付款方: 【李慧】, 账户: ***8018, 来源: 【本人还款】
2025-07-31 12:53:07,090 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:53:07,090 - INFO - ✅ 通过姓名找到唯一匹配: 李智 → debtor_no: 00a49c356f253a1039f48647317ea1f6
2025-07-31 12:53:07,090 - INFO - ✅ 通过还款对账明细找到debtor_no: 李智 → 00a49c356f253a1039f48647317ea1f6 (姓名唯一匹配)
2025-07-31 12:53:07,090 - INFO - 📸 开始处理图片: 李智1000.jpg
2025-07-31 12:53:07,090 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:53:07,090 - INFO - 🔍 开始OCR识别图片: 李智1000.jpg
2025-07-31 12:53:07,091 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李智1000.jpg
2025-07-31 12:53:22,953 - INFO - ✅ 豆包AI分析成功: 👤李智 0211 💳***7264 💰1000.00 ⏰2025-07-30 14:03:50
2025-07-31 12:53:22,953 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:53:22,953 - INFO - 👤 付款方识别成功: 【李智 0211】
2025-07-31 12:53:22,954 - INFO - 💳 账户号码识别成功: ***7264
2025-07-31 12:53:22,954 - INFO - 💰 还款金额识别成功: 1000.00
2025-07-31 12:53:22,954 - INFO - ⏰ 还款时间识别成功: 2025-07-30 14:03:50 → 2025-07-30
2025-07-31 12:53:22,954 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:53:22,954 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:53:22,954 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:53:22,954 - INFO -    👤 还款人: 【李智 0211】
2025-07-31 12:53:22,955 - INFO -    💳 还款账号: ***7264
2025-07-31 12:53:22,955 - INFO -    💰 还款金额: 1000.00
2025-07-31 12:53:22,955 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:53:22,955 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:53:22,955 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.82)
2025-07-31 12:53:22,955 - INFO - 🎯 图片处理完成: 李智1000.jpg
2025-07-31 12:53:22,955 - INFO -    📋 最终结果 - 付款方: 【李智 0211】, 账户: ***7264, 来源: 【本人还款】
2025-07-31 12:53:22,957 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:53:22,957 - INFO - ✅ 通过姓名找到唯一匹配: 杨婷 → debtor_no: b15ae9f40070669105226af9226031ad
2025-07-31 12:53:22,957 - INFO - ✅ 通过还款对账明细找到debtor_no: 杨婷 → b15ae9f40070669105226af9226031ad (姓名唯一匹配)
2025-07-31 12:53:22,957 - INFO - 📸 开始处理图片: 杨婷500.jpg
2025-07-31 12:53:22,957 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:53:22,958 - INFO - 🔍 开始OCR识别图片: 杨婷500.jpg
2025-07-31 12:53:22,958 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 杨婷500.jpg
2025-07-31 12:53:40,860 - INFO - 🔄 使用文件名中的姓名: 杨婷
2025-07-31 12:53:40,860 - INFO - ✅ 豆包AI分析成功: 👤杨婷 💳***1261 💰500.00 ⏰2025-07-29 22:26:21
2025-07-31 12:53:40,861 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:53:40,861 - INFO - 👤 付款方识别成功: 【杨婷】
2025-07-31 12:53:40,861 - INFO - 💳 账户号码识别成功: ***1261
2025-07-31 12:53:40,861 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:53:40,862 - INFO - ⏰ 还款时间识别成功: 2025-07-29 22:26:21 → 2025-07-29
2025-07-31 12:53:40,862 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:53:40,862 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:53:40,862 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:53:40,863 - INFO -    👤 还款人: 【杨婷】
2025-07-31 12:53:40,863 - INFO -    💳 还款账号: ***1261
2025-07-31 12:53:40,863 - INFO -    💰 还款金额: 500.00
2025-07-31 12:53:40,863 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:53:40,863 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:53:40,864 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:53:40,864 - INFO - 🎯 图片处理完成: 杨婷500.jpg
2025-07-31 12:53:40,864 - INFO -    📋 最终结果 - 付款方: 【杨婷】, 账户: ***1261, 来源: 【本人还款】
2025-07-31 12:53:40,866 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:53:40,866 - INFO - ✅ 通过姓名找到唯一匹配: 王红英 → debtor_no: 4e5d73e2b5fc658ba4891d750f70034d
2025-07-31 12:53:40,866 - INFO - ✅ 通过还款对账明细找到debtor_no: 王红英 → 4e5d73e2b5fc658ba4891d750f70034d (姓名唯一匹配)
2025-07-31 12:53:40,867 - INFO - 📸 开始处理图片: 王红英2000.jpg
2025-07-31 12:53:40,867 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:53:40,867 - INFO - 🔍 开始OCR识别图片: 王红英2000.jpg
2025-07-31 12:53:40,867 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王红英2000.jpg
2025-07-31 12:53:49,772 - INFO - ✅ 豆包AI分析成功: 👤穆海红 💳***8969 💰2000.00 ⏰2025-07-30 13:46:46
2025-07-31 12:53:49,772 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:53:49,772 - INFO - 👤 付款方识别成功: 【穆海红】
2025-07-31 12:53:49,773 - INFO - 💳 账户号码识别成功: ***8969
2025-07-31 12:53:49,773 - INFO - 💰 还款金额识别成功: 2000.00
2025-07-31 12:53:49,773 - INFO - ⏰ 还款时间识别成功: 2025-07-30 13:46:46 → 2025-07-30
2025-07-31 12:53:49,773 - INFO - 🔄 还款来源: 他人代还 (付款人[穆海红]与文件名[王红英]不匹配)
2025-07-31 12:53:49,774 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:53:49,774 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.30 隆丰电催\王红英2000.jpg
2025-07-31 12:53:49,774 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:53:49,774 - WARNING -    姓名匹配: OCR[穆海红] vs 文件名[王红英] = 0.00
2025-07-31 12:53:49,774 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.33)
2025-07-31 12:53:49,775 - WARNING -    金额匹配: OCR[2000.00] vs 文件名[2000] = 1.00
2025-07-31 12:53:49,775 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:53:49,775 - INFO -    👤 还款人: 【穆海红】
2025-07-31 12:53:49,775 - INFO -    💳 还款账号: ***8969
2025-07-31 12:53:49,775 - INFO -    💰 还款金额: 2000.00
2025-07-31 12:53:49,776 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:53:49,776 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:53:49,776 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:53:49,776 - INFO - 🎯 图片处理完成: 王红英2000.jpg
2025-07-31 12:53:49,776 - INFO -    📋 最终结果 - 付款方: 【穆海红】, 账户: ***8969, 来源: 【他人代还】
2025-07-31 12:53:49,778 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:53:49,778 - INFO - ✅ 通过姓名找到唯一匹配: 苑子龙 → debtor_no: 459e47d5a0d13e30fbf761f2d1e0c371
2025-07-31 12:53:49,778 - INFO - ✅ 通过还款对账明细找到debtor_no: 苑子龙 → 459e47d5a0d13e30fbf761f2d1e0c371 (姓名唯一匹配)
2025-07-31 12:53:49,778 - INFO - 📸 开始处理图片: 苑子龙500.jpg
2025-07-31 12:53:49,778 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:53:49,778 - INFO - 🔍 开始OCR识别图片: 苑子龙500.jpg
2025-07-31 12:53:49,779 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 苑子龙500.jpg
2025-07-31 12:54:11,671 - INFO - ✅ 豆包AI分析成功: 👤*** 💳***0000 💰500.00 ⏰2025-07-30 09:55:58
2025-07-31 12:54:11,671 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:54:11,672 - INFO - ⭐ 检测到带星号的付款方: 【***】，进行星号验证...
2025-07-31 12:54:11,672 - INFO - ✅ 通过姓名找到唯一匹配: 苑子龙 → debtor_no: 459e47d5a0d13e30fbf761f2d1e0c371
2025-07-31 12:54:11,672 - INFO - ✅ 星号还款人验证通过，将替换: *** → 苑子龙
2025-07-31 12:54:11,672 - INFO - ✅ 星号付款方替换成功: 【***】 → 【苑子龙】
2025-07-31 12:54:11,672 - INFO -    替换原因: 满足所有条件：含*号[***]，文件名债务人[苑子龙]，Excel有debtor_no[459e47d5a0d13e30fbf761f2d1e0c371]，时间一致[2025-07-30]
2025-07-31 12:54:11,673 - INFO - 💳 账户号码识别成功: ***0000
2025-07-31 12:54:11,673 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:54:11,673 - INFO - ⏰ 还款时间识别成功: 2025-07-30 09:55:58 → 2025-07-30
2025-07-31 12:54:11,673 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:54:11,673 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:54:11,674 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:54:11,674 - INFO -    👤 还款人: 【苑子龙】
2025-07-31 12:54:11,674 - INFO -    💳 还款账号: ***0000
2025-07-31 12:54:11,674 - INFO -    💰 还款金额: 500.00
2025-07-31 12:54:11,674 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:54:11,675 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:54:11,675 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.88)
2025-07-31 12:54:11,675 - INFO - 🎯 图片处理完成: 苑子龙500.jpg
2025-07-31 12:54:11,675 - INFO -    📋 最终结果 - 付款方: 【苑子龙】, 账户: ***0000, 来源: 【本人还款】
2025-07-31 12:54:11,677 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:54:11,677 - INFO - ✅ 通过姓名找到唯一匹配: 郑光 → debtor_no: 91196f77579c29e12d57ddc4b71b59d4
2025-07-31 12:54:11,677 - INFO - ✅ 通过还款对账明细找到debtor_no: 郑光 → 91196f77579c29e12d57ddc4b71b59d4 (姓名唯一匹配)
2025-07-31 12:54:11,677 - INFO - 📸 开始处理图片: 郑光500.jpg
2025-07-31 12:54:11,677 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:54:11,677 - INFO - 🔍 开始OCR识别图片: 郑光500.jpg
2025-07-31 12:54:11,678 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 郑光500.jpg
2025-07-31 12:54:23,746 - INFO - 🔄 使用文件名中的姓名: 郑光
2025-07-31 12:54:23,747 - INFO - ✅ 豆包AI分析成功: 👤郑光 💳***4258 💰500.00 ⏰2025-07-30 08:34:29
2025-07-31 12:54:23,747 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:54:23,747 - INFO - 👤 付款方识别成功: 【郑光】
2025-07-31 12:54:23,747 - INFO - 💳 账户号码识别成功: ***4258
2025-07-31 12:54:23,747 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:54:23,748 - INFO - ⏰ 还款时间识别成功: 2025-07-30 08:34:29 → 2025-07-30
2025-07-31 12:54:23,748 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:54:23,748 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:54:23,748 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:54:23,748 - INFO -    👤 还款人: 【郑光】
2025-07-31 12:54:23,749 - INFO -    💳 还款账号: ***4258
2025-07-31 12:54:23,749 - INFO -    💰 还款金额: 500.00
2025-07-31 12:54:23,749 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:54:23,749 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:54:23,749 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:54:23,750 - INFO - 🎯 图片处理完成: 郑光500.jpg
2025-07-31 12:54:23,750 - INFO -    📋 最终结果 - 付款方: 【郑光】, 账户: ***4258, 来源: 【本人还款】
2025-07-31 12:54:23,751 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:54:23,751 - INFO - ✅ 通过姓名找到唯一匹配: 韩旭 → debtor_no: ff67bb5c37497015db383f94b5132cc5
2025-07-31 12:54:23,751 - INFO - ✅ 通过还款对账明细找到debtor_no: 韩旭 → ff67bb5c37497015db383f94b5132cc5 (姓名唯一匹配)
2025-07-31 12:54:23,752 - INFO - 📸 开始处理图片: 韩旭500.jpg
2025-07-31 12:54:23,752 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:54:23,752 - INFO - 🔍 开始OCR识别图片: 韩旭500.jpg
2025-07-31 12:54:23,752 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 韩旭500.jpg
2025-07-31 12:54:30,842 - INFO - ✅ 豆包AI分析成功: 👤韩旭 💳***5286 💰500.00 ⏰2025-07-30 14:29:26
2025-07-31 12:54:30,842 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:54:30,842 - INFO - 👤 付款方识别成功: 【韩旭】
2025-07-31 12:54:30,842 - INFO - 💳 账户号码识别成功: ***5286
2025-07-31 12:54:30,843 - INFO - 💰 还款金额识别成功: 500.00
2025-07-31 12:54:30,843 - INFO - ⏰ 还款时间识别成功: 2025-07-30 14:29:26 → 2025-07-30
2025-07-31 12:54:30,843 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:54:30,843 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:54:30,843 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:54:30,844 - INFO -    👤 还款人: 【韩旭】
2025-07-31 12:54:30,844 - INFO -    💳 还款账号: ***5286
2025-07-31 12:54:30,844 - INFO -    💰 还款金额: 500.00
2025-07-31 12:54:30,844 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:54:30,845 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:54:30,845 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:54:30,845 - INFO - 🎯 图片处理完成: 韩旭500.jpg
2025-07-31 12:54:30,845 - INFO -    📋 最终结果 - 付款方: 【韩旭】, 账户: ***5286, 来源: 【本人还款】
2025-07-31 12:54:30,847 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:54:30,847 - INFO - ✅ 通过姓名找到唯一匹配: 韩波 → debtor_no: 96709d2b20440e53e29de465a2473027
2025-07-31 12:54:30,847 - INFO - ✅ 通过还款对账明细找到debtor_no: 韩波 → 96709d2b20440e53e29de465a2473027 (姓名唯一匹配)
2025-07-31 12:54:30,847 - INFO - 📸 开始处理图片: 韩波1310.jpg
2025-07-31 12:54:30,847 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:54:30,847 - INFO - 🔍 开始OCR识别图片: 韩波1310.jpg
2025-07-31 12:54:30,848 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 韩波1310.jpg
2025-07-31 12:54:43,453 - INFO - 🔄 使用文件名中的姓名: 韩波
2025-07-31 12:54:43,453 - INFO - ✅ 豆包AI分析成功: 👤韩波 💳***5163 💰1310.00 ⏰2025-07-29 19:45:53
2025-07-31 12:54:43,453 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:54:43,453 - INFO - 👤 付款方识别成功: 【韩波】
2025-07-31 12:54:43,454 - INFO - 💳 账户号码识别成功: ***5163
2025-07-31 12:54:43,454 - INFO - 💰 还款金额识别成功: 1310.00
2025-07-31 12:54:43,454 - INFO - ⏰ 还款时间识别成功: 2025-07-29 19:45:53 → 2025-07-29
2025-07-31 12:54:43,454 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:54:43,454 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:54:43,455 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:54:43,455 - INFO -    👤 还款人: 【韩波】
2025-07-31 12:54:43,455 - INFO -    💳 还款账号: ***5163
2025-07-31 12:54:43,455 - INFO -    💰 还款金额: 1310.00
2025-07-31 12:54:43,455 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:54:43,456 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:54:43,456 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:54:43,456 - INFO - 🎯 图片处理完成: 韩波1310.jpg
2025-07-31 12:54:43,456 - INFO -    📋 最终结果 - 付款方: 【韩波】, 账户: ***5163, 来源: 【本人还款】
2025-07-31 12:54:43,456 - INFO - 🔍 扫描还款对账明细Excel: 王福军561.31
2025-07-31 12:54:43,457 - INFO -    ⚠️ 未找到还款对账明细Excel文件
2025-07-31 12:54:43,457 - INFO - 处理子文件夹（按文件夹名解析）: 王福军561.31
2025-07-31 12:54:43,457 - INFO - 找到代存图片: D:\民生4期回款\7.30 隆丰电催\王福军561.31\代还.jpg
2025-07-31 12:54:43,458 - INFO - ✅ 通过姓名找到唯一匹配: 王福军 → debtor_no: c2a7240643b5ed1c9df209bedf94cd7b
2025-07-31 12:54:43,458 - INFO - ✅ 子目录通过还款对账明细找到debtor_no: 王福军 → c2a7240643b5ed1c9df209bedf94cd7b (姓名唯一匹配)
2025-07-31 12:54:43,458 - INFO - 🚀 子目录OCR识别流程: 王福军561.31.jpg
2025-07-31 12:54:43,458 - INFO - 🔍 开始OCR识别图片: 王福军561.31.jpg
2025-07-31 12:54:43,458 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王福军561.31.jpg
2025-07-31 12:54:53,939 - INFO - 🔄 使用文件名中的姓名: 王福军
2025-07-31 12:54:53,940 - ERROR - ❌ 豆包AI处理异常: 'float' object has no attribute 'strip'
2025-07-31 12:54:53,940 - ERROR - ❌ 子目录OCR识别失败，所有字段保持为空
2025-07-31 12:54:53,940 - INFO - 基于文件夹名称创建记录: 王福军561.31 -> 王福军 561.31元
2025-07-31 12:54:53,940 - INFO - 🔍 扫描还款对账明细Excel: 郭丞旭300
2025-07-31 12:54:53,941 - INFO -    ⚠️ 未找到还款对账明细Excel文件
2025-07-31 12:54:53,941 - INFO - 处理子文件夹（按文件夹名解析）: 郭丞旭300
2025-07-31 12:54:53,941 - INFO - 找到代存图片: D:\民生4期回款\7.30 隆丰电催\郭丞旭300\代还.jpg
2025-07-31 12:54:53,942 - INFO - ✅ 通过姓名找到唯一匹配: 郭丞旭 → debtor_no: d0e0a4ad5a89f35f3fa8afe6e0633cfd
2025-07-31 12:54:53,942 - INFO - ✅ 子目录通过还款对账明细找到debtor_no: 郭丞旭 → d0e0a4ad5a89f35f3fa8afe6e0633cfd (姓名唯一匹配)
2025-07-31 12:54:53,942 - INFO - 🚀 子目录OCR识别流程: 郭丞旭300.jpg
2025-07-31 12:54:53,943 - INFO - 🔍 开始OCR识别图片: 郭丞旭300.jpg
2025-07-31 12:54:53,943 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 郭丞旭300.jpg
2025-07-31 12:55:10,119 - INFO - 🔄 使用文件名中的姓名: 郭丞旭
2025-07-31 12:55:10,119 - ERROR - ❌ 豆包AI处理异常: 'float' object has no attribute 'strip'
2025-07-31 12:55:10,120 - ERROR - ❌ 子目录OCR识别失败，所有字段保持为空
2025-07-31 12:55:10,120 - INFO - 基于文件夹名称创建记录: 郭丞旭300 -> 郭丞旭 300元
2025-07-31 12:55:10,120 - INFO - 📊 开始验证文件夹 '7.30 隆丰电催' 的数据一致性...
2025-07-31 12:55:10,145 - ERROR - ❌ 文件夹 '7.30 隆丰电催' 数据一致性验证失败!
2025-07-31 12:55:10,145 - ERROR -    Excel中的人员未在图片中找到: 王红英, 康嫚丽, 李智
2025-07-31 12:55:10,146 - ERROR -    图片中的人员在Excel中未找到: 于爱荣, 李智 0211, 穆海红
2025-07-31 12:55:10,146 - INFO - 🔍 开始验证OCR识别结果与Excel对比...
2025-07-31 12:55:10,169 - INFO - 📋 读取还款对账明细文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:55:10,170 - WARNING - ❌ 还款人未在Excel中找到: 于爱荣
2025-07-31 12:55:10,170 - WARNING - ❌ 还款人未在Excel中找到: 穆海红
2025-07-31 12:55:10,171 - INFO - 📊 Excel验证完成 - 匹配: 12, 未匹配: 2
2025-07-31 12:55:10,171 - WARNING - ⚠️  Excel验证发现问题:
2025-07-31 12:55:10,171 - WARNING -    有 2 个还款人未在Excel中找到
2025-07-31 12:55:10,171 - INFO - 目录 7.30 隆丰电催 处理完成，共处理 16 个图片
2025-07-31 12:55:10,178 - INFO - 写入主要处理结果: 16 条记录
2025-07-31 12:55:10,183 - INFO - 写入还款对账明细: 16 条记录
2025-07-31 12:55:10,183 - INFO - 🔍 开始执行三重比对分析...
2025-07-31 12:55:10,183 - INFO - 📊 处理结果明细包含 16 个不同债务人
2025-07-31 12:55:10,183 - INFO - 📊 还款对账明细包含 16 个不同债务人
2025-07-31 12:55:10,184 - INFO - 🔍 执行分析1：债务人姓名匹配分析
2025-07-31 12:55:10,184 - INFO - 🔍 执行分析2：还款金额匹配分析
2025-07-31 12:55:10,184 - INFO - 🔍 执行分析3：结清验证分析
2025-07-31 12:55:10,185 - INFO - ✅ 三重比对分析完成
2025-07-31 12:55:10,185 - INFO -    📊 统计结果: {'还款对账明细债务人数': 16, '处理结果明细债务人数': 16, '共同债务人数': 16, '仅在还款对账明细中': 0, '仅在处理结果明细中': 0, '金额不一致债务人数': 2, '需要关注的结清案例': 0}
2025-07-31 12:55:10,189 - INFO - 写入金额匹配分析: 16 条记录
2025-07-31 12:55:10,193 - INFO - 写入结清验证分析: 1 条记录
2025-07-31 12:55:10,196 - INFO - 写入比对分析摘要: 7 条记录
2025-07-31 12:55:10,196 - INFO - 开始生成人名统计，共有 16 条处理结果
2025-07-31 12:55:10,197 - INFO - 处理完成，共处理 1 个目录，生成 16 个人员统计
2025-07-31 12:55:10,198 - INFO - 处理的目录列表: ['7.30 隆丰电催']
2025-07-31 12:55:10,719 - INFO - 最终生成 16 个人员统计记录
2025-07-31 12:55:10,722 - INFO - 写入人名统计信息: 16 条记录
2025-07-31 12:55:10,722 - INFO - 🎨 开始应用Excel格式设置...
2025-07-31 12:55:10,722 - INFO -    文件路径列索引: 2
2025-07-31 12:55:10,722 - INFO -    总行数: 17
2025-07-31 12:55:10,723 - INFO - 🔗 正在设置文件路径超链接...
2025-07-31 12:55:10,723 - INFO - ✅ 文件路径超链接设置完成，共设置 16 个超链接
2025-07-31 12:55:10,723 - INFO - 🎨 正在设置行颜色标记...
2025-07-31 12:55:10,724 - INFO - 🟡 应用黄色标识: OCR识别失败
2025-07-31 12:55:10,724 - INFO - 🟡 应用黄色标识: OCR识别失败
2025-07-31 12:55:10,725 - INFO - ✅ '处理结果明细'sheet格式设置完成
2025-07-31 12:55:10,725 - INFO - 🔴 正在设置结清验证状态列格式...
2025-07-31 12:55:10,725 - INFO - ✅ 结清验证状态列格式化完成：0 个单元格标红
2025-07-31 12:55:10,726 - INFO - ✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红
2025-07-31 12:55:10,726 - INFO - 📍 找到关键列 - 图片文件数量: 13, debtor_no: 9, 类型: 6
2025-07-31 12:55:10,727 - INFO -    🔴 标红第10行: 郭丞旭 (图片文件数量为0)
2025-07-31 12:55:10,728 - INFO -    🔴 标红第13行: 王福军 (图片文件数量为0)
2025-07-31 12:55:10,729 - INFO - ✅ 还款对账明细格式化完成
2025-07-31 12:55:10,729 - INFO -    🔴 图片文件缺失标红: 2 行
2025-07-31 12:55:10,729 - INFO -    🟠 结清验证问题标橙: 0 行
2025-07-31 12:55:10,730 - WARNING - ⚠️ 发现 2 条记录存在图片缺失或金额不一致，已在Excel中标红显示
2025-07-31 12:55:10,730 - WARNING -    💡 这些记录可能需要检查：图片缺失或金额差异
2025-07-31 12:55:10,732 - INFO - 写入目录统计信息: 1 条记录
2025-07-31 12:55:10,733 - INFO - 写入处理摘要信息
2025-07-31 12:55:10,761 - INFO - 增强版Excel文件保存成功: 7.30 隆丰电催_还款凭证解析_20250731_125510.xlsx
2025-07-31 12:55:10,761 - INFO - ✅ 成功生成Excel文件: 7.30 隆丰电催_还款凭证解析_20250731_125510.xlsx
2025-07-31 12:55:10,762 - INFO - ✅ 子文件夹 7.30 隆丰电催 处理完成，生成文件: 7.30 隆丰电催_还款凭证解析_20250731_125510.xlsx
2025-07-31 12:55:10,762 - INFO - ================================================================================
2025-07-31 12:55:10,763 - INFO - 🗂️  开始处理子文件夹: 7.30 隆丰诉保
2025-07-31 12:55:10,763 - INFO - ✅ 目录 7.30 隆丰诉保 中Excel文件验证通过: 还款对账明细（合作方用）.xlsx
2025-07-31 12:55:10,763 - INFO - 目录 7.30 隆丰诉保 Excel验证成功
2025-07-31 12:55:10,764 - INFO - 🔍 扫描还款对账明细Excel: 7.30 隆丰诉保
2025-07-31 12:55:10,765 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:55:10,791 - INFO -    📋 Excel文件包含 7 行数据
2025-07-31 12:55:10,791 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号码', '还款金额': '现金回收', '类型': '还款类型（部分还款/结清）', '身份证号': '身份证号码'}
2025-07-31 12:55:10,793 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:55:10,794 - INFO -       债务人姓名: '李伟东'
2025-07-31 12:55:10,794 - INFO -       合同号: '25722162'
2025-07-31 12:55:10,794 - INFO -       身份证号: '150404197911226011'
2025-07-31 12:55:10,795 - INFO - 🔍 使用身份证号查询: 150404197911226011
2025-07-31 12:55:10,796 - INFO -    数据库查询结果: found=True, debtor_no=f65a36b1672980c856864f6e7c060541
2025-07-31 12:55:10,796 - INFO - ✅ 通过身份证号找到债务人: f65a36b1672980c856864f6e7c060541
2025-07-31 12:55:10,797 - INFO -    ✅ 匹配成功: debtor_no=f65a36b1672980c856864f6e7c060541, 方式=身份证号
2025-07-31 12:55:10,797 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:55:10,798 - INFO -       债务人姓名: '刘士娟'
2025-07-31 12:55:10,798 - INFO -       合同号: '17433412'
2025-07-31 12:55:10,798 - INFO -       身份证号: '232321198004054882'
2025-07-31 12:55:10,798 - INFO - 🔍 使用身份证号查询: 232321198004054882
2025-07-31 12:55:10,799 - INFO -    数据库查询结果: found=True, debtor_no=1ed421680569569e942b2b940da5a2b0
2025-07-31 12:55:10,799 - INFO - ✅ 通过身份证号找到债务人: 1ed421680569569e942b2b940da5a2b0
2025-07-31 12:55:10,799 - INFO -    ✅ 匹配成功: debtor_no=1ed421680569569e942b2b940da5a2b0, 方式=身份证号
2025-07-31 12:55:10,800 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:55:10,800 - INFO -       债务人姓名: '奚婷婷'
2025-07-31 12:55:10,800 - INFO -       合同号: '8208813'
2025-07-31 12:55:10,801 - INFO -       身份证号: '220282199103080523'
2025-07-31 12:55:10,801 - INFO - 🔍 使用身份证号查询: 220282199103080523
2025-07-31 12:55:10,802 - INFO -    数据库查询结果: found=True, debtor_no=3724742ae84548139640a5803a68ee1b
2025-07-31 12:55:10,802 - INFO - ✅ 通过身份证号找到债务人: 3724742ae84548139640a5803a68ee1b
2025-07-31 12:55:10,802 - INFO -    ✅ 匹配成功: debtor_no=3724742ae84548139640a5803a68ee1b, 方式=身份证号
2025-07-31 12:55:10,803 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:55:10,803 - INFO -       债务人姓名: '李显成'
2025-07-31 12:55:10,803 - INFO -       合同号: '22086345'
2025-07-31 12:55:10,803 - INFO -       身份证号: '220112199412310412'
2025-07-31 12:55:10,803 - INFO - 🔍 使用身份证号查询: 220112199412310412
2025-07-31 12:55:10,804 - INFO -    数据库查询结果: found=True, debtor_no=225b30316a1a5d8467fc8fec45baaa0d
2025-07-31 12:55:10,804 - INFO - ✅ 通过身份证号找到债务人: 225b30316a1a5d8467fc8fec45baaa0d
2025-07-31 12:55:10,805 - INFO -    ✅ 匹配成功: debtor_no=225b30316a1a5d8467fc8fec45baaa0d, 方式=身份证号
2025-07-31 12:55:10,805 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:55:10,805 - INFO -       债务人姓名: '门春莉'
2025-07-31 12:55:10,806 - INFO -       合同号: '8184227'
2025-07-31 12:55:10,806 - INFO -       身份证号: '210103196505052422'
2025-07-31 12:55:10,806 - INFO - 🔍 使用身份证号查询: 210103196505052422
2025-07-31 12:55:10,807 - INFO -    数据库查询结果: found=True, debtor_no=2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-07-31 12:55:10,807 - INFO - ✅ 通过身份证号找到债务人: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-07-31 12:55:10,808 - INFO -    ✅ 匹配成功: debtor_no=2d1b7a0ff0b2b3d589792a1bdc1ca1a3, 方式=身份证号
2025-07-31 12:55:10,810 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:55:10,810 - INFO -       债务人姓名: '姜玉福'
2025-07-31 12:55:10,810 - INFO -       合同号: '28702578'
2025-07-31 12:55:10,810 - INFO -       身份证号: '222326197409090035'
2025-07-31 12:55:10,811 - INFO - 🔍 使用身份证号查询: 222326197409090035
2025-07-31 12:55:10,812 - INFO -    数据库查询结果: found=True, debtor_no=591b274c2760f70346e648283862ad28
2025-07-31 12:55:10,812 - INFO - ✅ 通过身份证号找到债务人: 591b274c2760f70346e648283862ad28
2025-07-31 12:55:10,813 - INFO -    ✅ 匹配成功: debtor_no=591b274c2760f70346e648283862ad28, 方式=身份证号
2025-07-31 12:55:10,813 - INFO -    🔍 准备匹配债务人:
2025-07-31 12:55:10,814 - INFO -       债务人姓名: '王雪'
2025-07-31 12:55:10,814 - INFO -       合同号: '30754890'
2025-07-31 12:55:10,814 - INFO -       身份证号: '220202199604226621'
2025-07-31 12:55:10,814 - INFO - 🔍 使用身份证号查询: 220202199604226621
2025-07-31 12:55:10,816 - INFO -    数据库查询结果: found=True, debtor_no=1fd248ba9dc78396268205bf525e90b1
2025-07-31 12:55:10,816 - INFO - ✅ 通过身份证号找到债务人: 1fd248ba9dc78396268205bf525e90b1
2025-07-31 12:55:10,816 - INFO -    ✅ 匹配成功: debtor_no=1fd248ba9dc78396268205bf525e90b1, 方式=身份证号
2025-07-31 12:55:10,817 - INFO -    ✅ 成功处理 7 条对账明细记录
2025-07-31 12:55:10,818 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:55:10,818 - INFO - 🗂️ 扫描还款对账明细: 7 条记录
2025-07-31 12:55:10,818 - INFO - 处理目录: D:\民生4期回款\7.30 隆丰诉保
2025-07-31 12:55:10,819 - INFO - 🔍 扫描还款对账明细Excel: 7.30 隆丰诉保
2025-07-31 12:55:10,819 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:55:10,820 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:55:10,822 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:55:10,822 - INFO - ✅ 通过姓名找到唯一匹配: 刘士娟 → debtor_no: 1ed421680569569e942b2b940da5a2b0
2025-07-31 12:55:10,822 - INFO - ✅ 通过还款对账明细找到debtor_no: 刘士娟 → 1ed421680569569e942b2b940da5a2b0 (姓名唯一匹配)
2025-07-31 12:55:10,822 - INFO - 📸 开始处理图片: 刘士娟4882-100.jpg
2025-07-31 12:55:10,823 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:55:10,823 - INFO - 🔍 开始OCR识别图片: 刘士娟4882-100.jpg
2025-07-31 12:55:10,823 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 刘士娟4882-100.jpg
2025-07-31 12:55:25,454 - INFO - ✅ 豆包AI分析成功: 👤*士娟 💳***9474 💰100.00 ⏰2025-07-29 14:42:46
2025-07-31 12:55:25,455 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:55:25,455 - INFO - ⭐ 检测到带星号的付款方: 【*士娟】，进行星号验证...
2025-07-31 12:55:25,455 - INFO - ✅ 通过姓名找到唯一匹配: 刘士娟 → debtor_no: 1ed421680569569e942b2b940da5a2b0
2025-07-31 12:55:25,455 - INFO - ⭐ 保持星号付款方: 【*士娟】
2025-07-31 12:55:25,455 - INFO -    不替换原因: 还款时间不一致：OCR[2025-07-29] vs Excel[2025-07-30]
2025-07-31 12:55:25,456 - INFO - 💳 账户号码识别成功: ***9474
2025-07-31 12:55:25,456 - INFO - 💰 还款金额识别成功: 100.00
2025-07-31 12:55:25,456 - INFO - ⏰ 还款时间识别成功: 2025-07-29 14:42:46 → 2025-07-29
2025-07-31 12:55:25,456 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:55:25,457 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:55:25,457 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:55:25,457 - INFO -    👤 还款人: 【*士娟】
2025-07-31 12:55:25,457 - INFO -    💳 还款账号: ***9474
2025-07-31 12:55:25,457 - INFO -    💰 还款金额: 100.00
2025-07-31 12:55:25,458 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:55:25,458 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:55:25,458 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.88)
2025-07-31 12:55:25,458 - INFO - 🎯 图片处理完成: 刘士娟4882-100.jpg
2025-07-31 12:55:25,459 - INFO -    📋 最终结果 - 付款方: 【*士娟】, 账户: ***9474, 来源: 【本人还款】
2025-07-31 12:55:25,460 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:55:25,461 - INFO - ✅ 通过姓名找到唯一匹配: 刘士娟 → debtor_no: 1ed421680569569e942b2b940da5a2b0
2025-07-31 12:55:25,461 - INFO - ✅ 通过还款对账明细找到debtor_no: 刘士娟 → 1ed421680569569e942b2b940da5a2b0 (姓名唯一匹配)
2025-07-31 12:55:25,461 - INFO - 📸 开始处理图片: 刘士娟4882-4901.jpg
2025-07-31 12:55:25,461 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:55:25,461 - INFO - 🔍 开始OCR识别图片: 刘士娟4882-4901.jpg
2025-07-31 12:55:25,462 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 刘士娟4882-4901.jpg
2025-07-31 12:55:35,014 - INFO - ✅ 豆包AI分析成功: 👤刘士娟 💳***9474 💰4901.00 ⏰2025-07-30 13:39:48
2025-07-31 12:55:35,014 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:55:35,014 - INFO - 👤 付款方识别成功: 【刘士娟】
2025-07-31 12:55:35,015 - INFO - 💳 账户号码识别成功: ***9474
2025-07-31 12:55:35,015 - INFO - 💰 还款金额识别成功: 4901.00
2025-07-31 12:55:35,015 - INFO - ⏰ 还款时间识别成功: 2025-07-30 13:39:48 → 2025-07-30
2025-07-31 12:55:35,015 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:55:35,016 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:55:35,016 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:55:35,016 - INFO -    👤 还款人: 【刘士娟】
2025-07-31 12:55:35,016 - INFO -    💳 还款账号: ***9474
2025-07-31 12:55:35,017 - INFO -    💰 还款金额: 4901.00
2025-07-31 12:55:35,017 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:55:35,017 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:55:35,017 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:55:35,017 - INFO - 🎯 图片处理完成: 刘士娟4882-4901.jpg
2025-07-31 12:55:35,018 - INFO -    📋 最终结果 - 付款方: 【刘士娟】, 账户: ***9474, 来源: 【本人还款】
2025-07-31 12:55:35,019 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:55:35,019 - INFO - ✅ 通过姓名找到唯一匹配: 刘士娟 → debtor_no: 1ed421680569569e942b2b940da5a2b0
2025-07-31 12:55:35,020 - INFO - ✅ 通过还款对账明细找到debtor_no: 刘士娟 → 1ed421680569569e942b2b940da5a2b0 (姓名唯一匹配)
2025-07-31 12:55:35,020 - INFO - 📸 开始处理图片: 刘士娟4882-4999.jpg
2025-07-31 12:55:35,020 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:55:35,020 - INFO - 🔍 开始OCR识别图片: 刘士娟4882-4999.jpg
2025-07-31 12:55:35,020 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 刘士娟4882-4999.jpg
2025-07-31 12:55:47,108 - INFO - ✅ 豆包AI分析成功: 👤刘士娟 💳***0000 💰4999.00 ⏰2025-07-29 14:59:28
2025-07-31 12:55:47,109 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:55:47,109 - INFO - 👤 付款方识别成功: 【刘士娟】
2025-07-31 12:55:47,109 - INFO - 💳 账户号码识别成功: ***0000
2025-07-31 12:55:47,109 - INFO - 💰 还款金额识别成功: 4999.00
2025-07-31 12:55:47,110 - INFO - ⏰ 还款时间识别成功: 2025-07-29 14:59:28 → 2025-07-29
2025-07-31 12:55:47,110 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:55:47,110 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:55:47,110 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:55:47,110 - INFO -    👤 还款人: 【刘士娟】
2025-07-31 12:55:47,111 - INFO -    💳 还款账号: ***0000
2025-07-31 12:55:47,111 - INFO -    💰 还款金额: 4999.00
2025-07-31 12:55:47,111 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:55:47,111 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:55:47,111 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:55:47,112 - INFO - 🎯 图片处理完成: 刘士娟4882-4999.jpg
2025-07-31 12:55:47,112 - INFO -    📋 最终结果 - 付款方: 【刘士娟】, 账户: ***0000, 来源: 【本人还款】
2025-07-31 12:55:47,113 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:55:47,113 - INFO - ✅ 通过姓名找到唯一匹配: 奚婷婷 → debtor_no: 3724742ae84548139640a5803a68ee1b
2025-07-31 12:55:47,113 - INFO - ✅ 通过还款对账明细找到debtor_no: 奚婷婷 → 3724742ae84548139640a5803a68ee1b (姓名唯一匹配)
2025-07-31 12:55:47,114 - INFO - 📸 开始处理图片: 奚婷婷0523-3333.3.jpg
2025-07-31 12:55:47,114 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:55:47,114 - INFO - 🔍 开始OCR识别图片: 奚婷婷0523-3333.3.jpg
2025-07-31 12:55:47,114 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 奚婷婷0523-3333.3.jpg
2025-07-31 12:56:05,155 - INFO - 🔄 使用文件名中的姓名: 奚婷婷
2025-07-31 12:56:05,155 - INFO - ✅ 豆包AI分析成功: 👤奚婷婷 💳***1230 💰3333.30 ⏰2025-07-30 13:57:15
2025-07-31 12:56:05,156 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:56:05,156 - INFO - 👤 付款方识别成功: 【奚婷婷】
2025-07-31 12:56:05,157 - INFO - 💳 账户号码识别成功: ***1230
2025-07-31 12:56:05,157 - INFO - 💰 还款金额识别成功: 3333.30
2025-07-31 12:56:05,158 - INFO - ⏰ 还款时间识别成功: 2025-07-30 13:57:15 → 2025-07-30
2025-07-31 12:56:05,158 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:56:05,159 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:56:05,159 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:56:05,159 - INFO -    👤 还款人: 【奚婷婷】
2025-07-31 12:56:05,160 - INFO -    💳 还款账号: ***1230
2025-07-31 12:56:05,160 - INFO -    💰 还款金额: 3333.30
2025-07-31 12:56:05,160 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:56:05,161 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:56:05,161 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:56:05,161 - INFO - 🎯 图片处理完成: 奚婷婷0523-3333.3.jpg
2025-07-31 12:56:05,162 - INFO -    📋 最终结果 - 付款方: 【奚婷婷】, 账户: ***1230, 来源: 【本人还款】
2025-07-31 12:56:05,164 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:56:05,164 - INFO - ✅ 通过姓名找到唯一匹配: 姜玉福 → debtor_no: 591b274c2760f70346e648283862ad28
2025-07-31 12:56:05,165 - INFO - ✅ 通过还款对账明细找到debtor_no: 姜玉福 → 591b274c2760f70346e648283862ad28 (姓名唯一匹配)
2025-07-31 12:56:05,165 - INFO - 📸 开始处理图片: 姜玉福0035-1100.jpg
2025-07-31 12:56:05,165 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:56:05,166 - INFO - 🔍 开始OCR识别图片: 姜玉福0035-1100.jpg
2025-07-31 12:56:05,166 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 姜玉福0035-1100.jpg
2025-07-31 12:56:20,715 - INFO - 🔄 使用文件名中的姓名: 姜玉福
2025-07-31 12:56:20,716 - INFO - ✅ 豆包AI分析成功: 👤姜玉福 💳***8051 💰1100.00 ⏰2025-07-30 15:27:18
2025-07-31 12:56:20,717 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:56:20,717 - INFO - 👤 付款方识别成功: 【姜玉福】
2025-07-31 12:56:20,718 - INFO - 💳 账户号码识别成功: ***8051
2025-07-31 12:56:20,718 - INFO - 💰 还款金额识别成功: 1100.00
2025-07-31 12:56:20,719 - INFO - ⏰ 还款时间识别成功: 2025-07-30 15:27:18 → 2025-07-30
2025-07-31 12:56:20,720 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:56:20,720 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:56:20,721 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:56:20,721 - INFO -    👤 还款人: 【姜玉福】
2025-07-31 12:56:20,722 - INFO -    💳 还款账号: ***8051
2025-07-31 12:56:20,723 - INFO -    💰 还款金额: 1100.00
2025-07-31 12:56:20,723 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:56:20,724 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:56:20,725 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:56:20,725 - INFO - 🎯 图片处理完成: 姜玉福0035-1100.jpg
2025-07-31 12:56:20,726 - INFO -    📋 最终结果 - 付款方: 【姜玉福】, 账户: ***8051, 来源: 【本人还款】
2025-07-31 12:56:20,728 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\x10\x00'
2025-07-31 12:56:20,728 - DEBUG - tag: Orientation (274) - type: long (4) - value: b'\x00\x00\x00\x00'
2025-07-31 12:56:20,728 - DEBUG - tag: BitsPerSample (258) - type: short (3) Tag Location: 46 - Data Location: 158 - value: b'\x00\x08\x00\x08\x00\x08'
2025-07-31 12:56:20,729 - DEBUG - tag: GPSInfoIFD (34853) - type: long (4) - value: b'\x00\x00\x03\x16'
2025-07-31 12:56:20,729 - DEBUG - tag: YResolution (283) - type: rational (5) Tag Location: 70 - Data Location: 164 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-07-31 12:56:20,729 - DEBUG - tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 172 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-07-31 12:56:20,729 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x0c\x00'
2025-07-31 12:56:20,729 - DEBUG - tag: Software (305) - type: string (2) Tag Location: 106 - Data Location: 180 - value: b'GFY-AL00 *********(C00E170R4P1)\x00'
2025-07-31 12:56:20,730 - DEBUG - tag: ImageDescription (270) - type: string (2) Tag Location: 118 - Data Location: 212 - value: b'smart\x00'
2025-07-31 12:56:20,730 - DEBUG - tag: YCbCrPositioning (531) - type: short (3) - value: b'\x00\x01'
2025-07-31 12:56:20,730 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00\xda'
2025-07-31 12:56:20,730 - DEBUG - tag: ResolutionUnit (296) - type: short (3) - value: b'\x00\x02'
2025-07-31 12:56:20,731 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:56:20,731 - INFO - ✅ 通过姓名找到唯一匹配: 李伟东 → debtor_no: f65a36b1672980c856864f6e7c060541
2025-07-31 12:56:20,731 - INFO - ✅ 通过还款对账明细找到debtor_no: 李伟东 → f65a36b1672980c856864f6e7c060541 (姓名唯一匹配)
2025-07-31 12:56:20,731 - INFO - 📸 开始处理图片: 李伟东6011--17000.jpg
2025-07-31 12:56:20,731 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:56:20,731 - INFO - 🔍 开始OCR识别图片: 李伟东6011--17000.jpg
2025-07-31 12:56:20,732 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李伟东6011--17000.jpg
2025-07-31 12:56:20,732 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\x10\x00'
2025-07-31 12:56:20,732 - DEBUG - tag: Orientation (274) - type: long (4) - value: b'\x00\x00\x00\x00'
2025-07-31 12:56:20,732 - DEBUG - tag: BitsPerSample (258) - type: short (3) Tag Location: 46 - Data Location: 158 - value: b'\x00\x08\x00\x08\x00\x08'
2025-07-31 12:56:20,733 - DEBUG - tag: GPSInfoIFD (34853) - type: long (4) - value: b'\x00\x00\x03\x16'
2025-07-31 12:56:20,733 - DEBUG - tag: YResolution (283) - type: rational (5) Tag Location: 70 - Data Location: 164 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-07-31 12:56:20,733 - DEBUG - tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 172 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-07-31 12:56:20,733 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x0c\x00'
2025-07-31 12:56:20,733 - DEBUG - tag: Software (305) - type: string (2) Tag Location: 106 - Data Location: 180 - value: b'GFY-AL00 *********(C00E170R4P1)\x00'
2025-07-31 12:56:20,733 - DEBUG - tag: ImageDescription (270) - type: string (2) Tag Location: 118 - Data Location: 212 - value: b'smart\x00'
2025-07-31 12:56:20,734 - DEBUG - tag: YCbCrPositioning (531) - type: short (3) - value: b'\x00\x01'
2025-07-31 12:56:20,734 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00\xda'
2025-07-31 12:56:20,734 - DEBUG - tag: ResolutionUnit (296) - type: short (3) - value: b'\x00\x02'
2025-07-31 12:56:33,631 - INFO - 🔄 使用文件名中的姓名: 李伟东
2025-07-31 12:56:33,631 - INFO - ✅ 豆包AI分析成功: 👤李伟东 💳***0000 💰17000 ⏰2023-07-30 00:00:00
2025-07-31 12:56:33,631 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:56:33,631 - INFO - 👤 付款方识别成功: 【李伟东】
2025-07-31 12:56:33,631 - INFO - 💳 账户号码识别成功: ***0000
2025-07-31 12:56:33,632 - INFO - 💰 还款金额识别成功: 17000
2025-07-31 12:56:33,632 - INFO - ⏰ 还款时间识别成功: 2023-07-30 00:00:00 → 2023-07-30
2025-07-31 12:56:33,632 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:56:33,632 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:56:33,633 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:56:33,633 - INFO -    👤 还款人: 【李伟东】
2025-07-31 12:56:33,633 - INFO -    💳 还款账号: ***0000
2025-07-31 12:56:33,634 - INFO -    💰 还款金额: 17000
2025-07-31 12:56:33,634 - INFO -    ⏰ 还款时间: 2023-07-30
2025-07-31 12:56:33,634 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:56:33,634 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:56:33,634 - INFO - 🎯 图片处理完成: 李伟东6011--17000.jpg
2025-07-31 12:56:33,634 - INFO -    📋 最终结果 - 付款方: 【李伟东】, 账户: ***0000, 来源: 【本人还款】
2025-07-31 12:56:33,636 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:56:33,636 - INFO - ✅ 通过姓名找到唯一匹配: 李显成 → debtor_no: 225b30316a1a5d8467fc8fec45baaa0d
2025-07-31 12:56:33,636 - INFO - ✅ 通过还款对账明细找到debtor_no: 李显成 → 225b30316a1a5d8467fc8fec45baaa0d (姓名唯一匹配)
2025-07-31 12:56:33,636 - INFO - 📸 开始处理图片: 李显成0412-3000.jpg
2025-07-31 12:56:33,636 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:56:33,636 - INFO - 🔍 开始OCR识别图片: 李显成0412-3000.jpg
2025-07-31 12:56:33,637 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李显成0412-3000.jpg
2025-07-31 12:56:47,496 - INFO - ✅ 豆包AI分析成功: 👤杨成龙 💳***3186 💰3000.00 ⏰2025-07-30 14:31:30
2025-07-31 12:56:47,496 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:56:47,496 - INFO - 👤 付款方识别成功: 【杨成龙】
2025-07-31 12:56:47,497 - INFO - 💳 账户号码识别成功: ***3186
2025-07-31 12:56:47,497 - INFO - 💰 还款金额识别成功: 3000.00
2025-07-31 12:56:47,497 - INFO - ⏰ 还款时间识别成功: 2025-07-30 14:31:30 → 2025-07-30
2025-07-31 12:56:47,497 - INFO - 🔄 还款来源: 他人代还 (付款人[杨成龙]与文件名[李显成]不匹配)
2025-07-31 12:56:47,498 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:56:47,498 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\7.30 隆丰诉保\李显成0412-3000.jpg
2025-07-31 12:56:47,498 - WARNING -    状态: 不一致, 置信度: 0.40
2025-07-31 12:56:47,498 - WARNING -    姓名匹配: OCR[杨成龙] vs 文件名[李显成] = 0.00
2025-07-31 12:56:47,498 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.33)
2025-07-31 12:56:47,499 - WARNING -    金额匹配: OCR[3000.00] vs 文件名[3000] = 1.00
2025-07-31 12:56:47,499 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:56:47,499 - INFO -    👤 还款人: 【杨成龙】
2025-07-31 12:56:47,499 - INFO -    💳 还款账号: ***3186
2025-07-31 12:56:47,499 - INFO -    💰 还款金额: 3000.00
2025-07-31 12:56:47,499 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:56:47,500 - INFO -    🔄 还款来源: 【他人代还】
2025-07-31 12:56:47,500 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-07-31 12:56:47,500 - INFO - 🎯 图片处理完成: 李显成0412-3000.jpg
2025-07-31 12:56:47,500 - INFO -    📋 最终结果 - 付款方: 【杨成龙】, 账户: ***3186, 来源: 【他人代还】
2025-07-31 12:56:47,502 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:56:47,502 - INFO - ✅ 通过姓名找到唯一匹配: 王雪 → debtor_no: 1fd248ba9dc78396268205bf525e90b1
2025-07-31 12:56:47,502 - INFO - ✅ 通过还款对账明细找到debtor_no: 王雪 → 1fd248ba9dc78396268205bf525e90b1 (姓名唯一匹配)
2025-07-31 12:56:47,502 - INFO - 📸 开始处理图片: 王雪6621-1000.jpg
2025-07-31 12:56:47,502 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:56:47,503 - INFO - 🔍 开始OCR识别图片: 王雪6621-1000.jpg
2025-07-31 12:56:47,503 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王雪6621-1000.jpg
2025-07-31 12:57:04,707 - INFO - ✅ 豆包AI分析成功: 👤王雪 💳***3515 💰1000.00 ⏰2025-07-30 16:45:17
2025-07-31 12:57:04,707 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:57:04,708 - INFO - 👤 付款方识别成功: 【王雪】
2025-07-31 12:57:04,708 - INFO - 💳 账户号码识别成功: ***3515
2025-07-31 12:57:04,708 - INFO - 💰 还款金额识别成功: 1000.00
2025-07-31 12:57:04,708 - INFO - ⏰ 还款时间识别成功: 2025-07-30 16:45:17 → 2025-07-30
2025-07-31 12:57:04,708 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:57:04,708 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:57:04,709 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:57:04,709 - INFO -    👤 还款人: 【王雪】
2025-07-31 12:57:04,709 - INFO -    💳 还款账号: ***3515
2025-07-31 12:57:04,709 - INFO -    💰 还款金额: 1000.00
2025-07-31 12:57:04,709 - INFO -    ⏰ 还款时间: 2025-07-30
2025-07-31 12:57:04,709 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:57:04,710 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-07-31 12:57:04,710 - INFO - 🎯 图片处理完成: 王雪6621-1000.jpg
2025-07-31 12:57:04,710 - INFO -    📋 最终结果 - 付款方: 【王雪】, 账户: ***3515, 来源: 【本人还款】
2025-07-31 12:57:04,712 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-07-31 12:57:04,712 - INFO - ✅ 通过姓名找到唯一匹配: 门春莉 → debtor_no: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-07-31 12:57:04,712 - INFO - ✅ 通过还款对账明细找到debtor_no: 门春莉 → 2d1b7a0ff0b2b3d589792a1bdc1ca1a3 (姓名唯一匹配)
2025-07-31 12:57:04,712 - INFO - 📸 开始处理图片: 门春莉2422-7000.jpg
2025-07-31 12:57:04,713 - INFO - 🚀 启动OCR识别流程...
2025-07-31 12:57:04,713 - INFO - 🔍 开始OCR识别图片: 门春莉2422-7000.jpg
2025-07-31 12:57:04,713 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 门春莉2422-7000.jpg
2025-07-31 12:57:18,008 - INFO - ✅ 豆包AI分析成功: 👤*春莉 💳***8178 💰7000.00 ⏰2025-07-29 19:24:49
2025-07-31 12:57:18,009 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-07-31 12:57:18,009 - INFO - ⭐ 检测到带星号的付款方: 【*春莉】，进行星号验证...
2025-07-31 12:57:18,009 - INFO - ✅ 通过姓名找到唯一匹配: 门春莉 → debtor_no: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-07-31 12:57:18,009 - INFO - ⭐ 保持星号付款方: 【*春莉】
2025-07-31 12:57:18,010 - INFO -    不替换原因: 还款时间不一致：OCR[2025-07-29] vs Excel[2025-07-30]
2025-07-31 12:57:18,010 - INFO - 💳 账户号码识别成功: ***8178
2025-07-31 12:57:18,010 - INFO - 💰 还款金额识别成功: 7000.00
2025-07-31 12:57:18,010 - INFO - ⏰ 还款时间识别成功: 2025-07-29 19:24:49 → 2025-07-29
2025-07-31 12:57:18,010 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-07-31 12:57:18,011 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-07-31 12:57:18,011 - INFO - 📊 OCR结果应用完成:
2025-07-31 12:57:18,011 - INFO -    👤 还款人: 【*春莉】
2025-07-31 12:57:18,011 - INFO -    💳 还款账号: ***8178
2025-07-31 12:57:18,012 - INFO -    💰 还款金额: 7000.00
2025-07-31 12:57:18,012 - INFO -    ⏰ 还款时间: 2025-07-29
2025-07-31 12:57:18,012 - INFO -    🔄 还款来源: 【本人还款】
2025-07-31 12:57:18,012 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.88)
2025-07-31 12:57:18,013 - INFO - 🎯 图片处理完成: 门春莉2422-7000.jpg
2025-07-31 12:57:18,013 - INFO -    📋 最终结果 - 付款方: 【*春莉】, 账户: ***8178, 来源: 【本人还款】
2025-07-31 12:57:18,013 - INFO - 📊 开始验证文件夹 '7.30 隆丰诉保' 的数据一致性...
2025-07-31 12:57:18,041 - ERROR - ❌ 文件夹 '7.30 隆丰诉保' 数据一致性验证失败!
2025-07-31 12:57:18,042 - ERROR -    Excel中的人员未在图片中找到: 李显成, 门春莉
2025-07-31 12:57:18,042 - ERROR -    图片中的人员在Excel中未找到: *春莉, *士娟, 杨成龙
2025-07-31 12:57:18,042 - INFO - 🔍 开始验证OCR识别结果与Excel对比...
2025-07-31 12:57:18,066 - INFO - 📋 读取还款对账明细文件: 还款对账明细（合作方用）.xlsx
2025-07-31 12:57:18,066 - WARNING - ❌ 还款人未在Excel中找到: 杨成龙
2025-07-31 12:57:18,066 - INFO - 📊 Excel验证完成 - 匹配: 8, 未匹配: 1
2025-07-31 12:57:18,067 - WARNING - ⚠️  Excel验证发现问题:
2025-07-31 12:57:18,067 - WARNING -    有 1 个还款人未在Excel中找到
2025-07-31 12:57:18,067 - INFO - 目录 7.30 隆丰诉保 处理完成，共处理 9 个图片
2025-07-31 12:57:18,073 - INFO - 写入主要处理结果: 9 条记录
2025-07-31 12:57:18,076 - INFO - 写入还款对账明细: 7 条记录
2025-07-31 12:57:18,076 - INFO - 🔍 开始执行三重比对分析...
2025-07-31 12:57:18,077 - INFO - 📊 处理结果明细包含 7 个不同债务人
2025-07-31 12:57:18,077 - INFO - 📊 还款对账明细包含 7 个不同债务人
2025-07-31 12:57:18,077 - INFO - 🔍 执行分析1：债务人姓名匹配分析
2025-07-31 12:57:18,077 - INFO - 🔍 执行分析2：还款金额匹配分析
2025-07-31 12:57:18,077 - INFO - 🔍 执行分析3：结清验证分析
2025-07-31 12:57:18,078 - INFO - ✅ 三重比对分析完成
2025-07-31 12:57:18,078 - INFO -    📊 统计结果: {'还款对账明细债务人数': 7, '处理结果明细债务人数': 7, '共同债务人数': 7, '仅在还款对账明细中': 0, '仅在处理结果明细中': 0, '金额不一致债务人数': 0, '需要关注的结清案例': 0}
2025-07-31 12:57:18,080 - INFO - 写入金额匹配分析: 7 条记录
2025-07-31 12:57:18,082 - INFO - 写入结清验证分析: 2 条记录
2025-07-31 12:57:18,083 - INFO - 写入比对分析摘要: 7 条记录
2025-07-31 12:57:18,084 - INFO - 开始生成人名统计，共有 9 条处理结果
2025-07-31 12:57:18,084 - INFO - 处理完成，共处理 1 个目录，生成 8 个人员统计
2025-07-31 12:57:18,084 - INFO - 处理的目录列表: ['7.30 隆丰诉保']
2025-07-31 12:57:18,111 - ERROR - 从Excel提取信息失败 7.30 隆丰诉保/*士娟: nothing to repeat at position 0
2025-07-31 12:57:18,283 - ERROR - 从Excel提取信息失败 7.30 隆丰诉保/*春莉: nothing to repeat at position 0
2025-07-31 12:57:18,283 - INFO - 最终生成 8 个人员统计记录
2025-07-31 12:57:18,285 - INFO - 写入人名统计信息: 8 条记录
2025-07-31 12:57:18,285 - INFO - 🎨 开始应用Excel格式设置...
2025-07-31 12:57:18,286 - INFO -    文件路径列索引: 2
2025-07-31 12:57:18,286 - INFO -    总行数: 10
2025-07-31 12:57:18,287 - INFO - 🔗 正在设置文件路径超链接...
2025-07-31 12:57:18,288 - INFO - ✅ 文件路径超链接设置完成，共设置 9 个超链接
2025-07-31 12:57:18,288 - INFO - 🎨 正在设置行颜色标记...
2025-07-31 12:57:18,289 - INFO - ✅ '处理结果明细'sheet格式设置完成
2025-07-31 12:57:18,289 - INFO - 🔴 正在设置结清验证状态列格式...
2025-07-31 12:57:18,289 - INFO - ✅ 结清验证状态列格式化完成：0 个单元格标红
2025-07-31 12:57:18,290 - INFO - ✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红
2025-07-31 12:57:18,290 - INFO - 📍 找到关键列 - 图片文件数量: 13, debtor_no: 9, 类型: 6
2025-07-31 12:57:18,290 - INFO - ✅ 还款对账明细格式化完成
2025-07-31 12:57:18,291 - INFO -    🔴 图片文件缺失标红: 0 行
2025-07-31 12:57:18,291 - INFO -    🟠 结清验证问题标橙: 0 行
2025-07-31 12:57:18,293 - INFO - 写入目录统计信息: 1 条记录
2025-07-31 12:57:18,294 - INFO - 写入处理摘要信息
2025-07-31 12:57:18,316 - INFO - 增强版Excel文件保存成功: 7.30 隆丰诉保_还款凭证解析_20250731_125718.xlsx
2025-07-31 12:57:18,317 - INFO - ✅ 成功生成Excel文件: 7.30 隆丰诉保_还款凭证解析_20250731_125718.xlsx
2025-07-31 12:57:18,317 - INFO - ✅ 子文件夹 7.30 隆丰诉保 处理完成，生成文件: 7.30 隆丰诉保_还款凭证解析_20250731_125718.xlsx
2025-07-31 12:57:18,317 - INFO - ================================================================================
2025-07-31 12:57:18,317 - INFO - 🎯 全部处理完成！
2025-07-31 12:57:18,318 - INFO - 📋 共处理 4 个子文件夹
2025-07-31 12:57:18,318 - INFO - 📄 成功生成 4 个Excel文件
2025-07-31 12:57:18,318 - INFO - 📄 生成的文件列表:
2025-07-31 12:57:18,319 - INFO -    - 7.29 隆丰电催_还款凭证解析_20250731_125019.xlsx
2025-07-31 12:57:18,319 - INFO -    - 7.29 隆丰诉保_还款凭证解析_20250731_125131.xlsx
2025-07-31 12:57:18,319 - INFO -    - 7.30 隆丰电催_还款凭证解析_20250731_125510.xlsx
2025-07-31 12:57:18,320 - INFO -    - 7.30 隆丰诉保_还款凭证解析_20250731_125718.xlsx
2025-07-31 12:57:18,321 - INFO - 开始生成人名统计，共有 9 条处理结果
2025-07-31 12:57:18,321 - INFO - 处理完成，共处理 1 个目录，生成 8 个人员统计
2025-07-31 12:57:18,321 - INFO - 处理的目录列表: ['7.30 隆丰诉保']
2025-07-31 12:57:18,346 - ERROR - 从Excel提取信息失败 7.30 隆丰诉保/*士娟: nothing to repeat at position 0
2025-07-31 12:57:18,518 - ERROR - 从Excel提取信息失败 7.30 隆丰诉保/*春莉: nothing to repeat at position 0
2025-07-31 12:57:18,518 - INFO - 最终生成 8 个人员统计记录
2025-08-04 09:18:55,764 - INFO - 🤖 自动选择OCR引擎...
2025-08-04 09:18:55,764 - INFO - ✅ 检测到豆包Seed-1.6 API，优先使用（AI视觉理解，最优中文识别）
2025-08-04 09:18:55,764 - INFO - 🚀 开始初始化豆包Seed-1.6 OCR引擎...
2025-08-04 09:18:56,847 - INFO - ✅ 豆包Seed-1.6引擎初始化成功
2025-08-04 09:18:56,847 - INFO -    🎯 AI视觉理解，最优中文识别
2025-08-04 09:18:56,847 - INFO -    🇨🇳 专门针对中文场景优化
2025-08-04 09:18:56,847 - INFO -    ⚡ 国内服务，响应速度快
2025-08-04 09:18:56,847 - INFO -    💰 极低成本，高性价比
2025-08-04 09:18:56,848 - INFO - 🎯 当前OCR引擎: 豆包Seed-1.6 AI视觉理解 (最优中文识别)
2025-08-04 09:18:56,884 - INFO - DuckDB数据库连接成功: debtors.duckdb
2025-08-04 09:18:56,902 - INFO - 使用现有DuckDB数据：23,043 条记录
2025-08-04 09:18:56,908 - INFO - 债务人数据库初始化成功：
2025-08-04 09:18:56,909 - INFO -   数据库类型: DuckDB
2025-08-04 09:18:56,909 - INFO -   总记录数: 23043
2025-08-04 09:18:56,909 - INFO -   有身份证号: 23043
2025-08-04 09:18:56,909 - INFO -   有合同号: 23043
2025-08-04 09:18:56,909 - INFO - 处理器初始化完成，OCR引擎: doubao
2025-08-04 09:18:56,914 - INFO - 开始处理文件夹: D:\民生4期回款
2025-08-04 09:18:56,915 - INFO - ================================================================================
2025-08-04 09:18:56,915 - INFO - 🗂️  开始处理子文件夹: 0801隆丰电催
2025-08-04 09:18:56,916 - INFO - ✅ 目录 0801隆丰电催 中Excel文件验证通过: 还款对账明细（合作方用）.xlsx
2025-08-04 09:18:56,916 - INFO - 目录 0801隆丰电催 Excel验证成功
2025-08-04 09:18:56,916 - INFO - 🔍 扫描还款对账明细Excel: 0801隆丰电催
2025-08-04 09:18:56,917 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:18:56,958 - INFO -    📋 Excel文件包含 14 行数据
2025-08-04 09:18:56,959 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号', '还款金额': '还款金额', '类型': '还款类型（部分还款/结清）'}
2025-08-04 09:18:56,960 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:56,960 - INFO -       债务人姓名: '马亮'
2025-08-04 09:18:56,960 - INFO -       合同号: '36839996'
2025-08-04 09:18:56,961 - INFO -       身份证号: ''
2025-08-04 09:18:56,961 - INFO - 🔍 使用合同号查询: 36839996
2025-08-04 09:18:56,965 - INFO -    数据库查询结果: found=True, debtor_no=1af4bac3e15c4febaaeb36dfc013b0f9
2025-08-04 09:18:56,965 - INFO - ✅ 通过合同号找到债务人: 1af4bac3e15c4febaaeb36dfc013b0f9
2025-08-04 09:18:56,966 - INFO -    ✅ 匹配成功: debtor_no=1af4bac3e15c4febaaeb36dfc013b0f9, 方式=合同号
2025-08-04 09:18:56,967 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:56,968 - INFO -       债务人姓名: '王新裕'
2025-08-04 09:18:56,968 - INFO -       合同号: '36359585'
2025-08-04 09:18:56,968 - INFO -       身份证号: ''
2025-08-04 09:18:56,968 - INFO - 🔍 使用合同号查询: 36359585
2025-08-04 09:18:56,972 - INFO -    数据库查询结果: found=True, debtor_no=e2498e856aafa265fab75acbe2888020
2025-08-04 09:18:56,972 - INFO - ✅ 通过合同号找到债务人: e2498e856aafa265fab75acbe2888020
2025-08-04 09:18:56,973 - INFO -    ✅ 匹配成功: debtor_no=e2498e856aafa265fab75acbe2888020, 方式=合同号
2025-08-04 09:18:56,974 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:56,974 - INFO -       债务人姓名: '赵殊弘'
2025-08-04 09:18:56,975 - INFO -       合同号: '35710781'
2025-08-04 09:18:56,975 - INFO -       身份证号: ''
2025-08-04 09:18:56,975 - INFO - 🔍 使用合同号查询: 35710781
2025-08-04 09:18:56,978 - INFO -    数据库查询结果: found=True, debtor_no=b97979872ca84a47f008df87cf902cf6
2025-08-04 09:18:56,978 - INFO - ✅ 通过合同号找到债务人: b97979872ca84a47f008df87cf902cf6
2025-08-04 09:18:56,978 - INFO -    ✅ 匹配成功: debtor_no=b97979872ca84a47f008df87cf902cf6, 方式=合同号
2025-08-04 09:18:56,979 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:56,980 - INFO -       债务人姓名: '王红伟'
2025-08-04 09:18:56,980 - INFO -       合同号: '30952656'
2025-08-04 09:18:56,980 - INFO -       身份证号: ''
2025-08-04 09:18:56,981 - INFO - 🔍 使用合同号查询: 30952656
2025-08-04 09:18:56,985 - INFO -    数据库查询结果: found=True, debtor_no=43626dd0f4180956f4179cdd5a3e97da
2025-08-04 09:18:56,986 - INFO - ✅ 通过合同号找到债务人: 43626dd0f4180956f4179cdd5a3e97da
2025-08-04 09:18:56,986 - INFO -    ✅ 匹配成功: debtor_no=43626dd0f4180956f4179cdd5a3e97da, 方式=合同号
2025-08-04 09:18:56,988 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:56,988 - INFO -       债务人姓名: '吴俊男'
2025-08-04 09:18:56,989 - INFO -       合同号: '22910158'
2025-08-04 09:18:56,989 - INFO -       身份证号: ''
2025-08-04 09:18:56,989 - INFO - 🔍 使用合同号查询: 22910158
2025-08-04 09:18:56,991 - INFO -    数据库查询结果: found=True, debtor_no=8fe2cb6c9e708f4210bba82cf1880060
2025-08-04 09:18:56,991 - INFO - ✅ 通过合同号找到债务人: 8fe2cb6c9e708f4210bba82cf1880060
2025-08-04 09:18:56,992 - INFO -    ✅ 匹配成功: debtor_no=8fe2cb6c9e708f4210bba82cf1880060, 方式=合同号
2025-08-04 09:18:56,992 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:56,993 - INFO -       债务人姓名: '付佳宇'
2025-08-04 09:18:56,993 - INFO -       合同号: '10887409'
2025-08-04 09:18:56,993 - INFO -       身份证号: ''
2025-08-04 09:18:56,993 - INFO - 🔍 使用合同号查询: 10887409
2025-08-04 09:18:56,997 - INFO -    数据库查询结果: found=True, debtor_no=226401882c097e264cde61f1b6ee056f
2025-08-04 09:18:56,997 - INFO - ✅ 通过合同号找到债务人: 226401882c097e264cde61f1b6ee056f
2025-08-04 09:18:56,998 - INFO -    ✅ 匹配成功: debtor_no=226401882c097e264cde61f1b6ee056f, 方式=合同号
2025-08-04 09:18:56,999 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:56,999 - INFO -       债务人姓名: '章诗嫄'
2025-08-04 09:18:56,999 - INFO -       合同号: '11838791'
2025-08-04 09:18:56,999 - INFO -       身份证号: ''
2025-08-04 09:18:56,999 - INFO - 🔍 使用合同号查询: 11838791
2025-08-04 09:18:57,002 - INFO -    数据库查询结果: found=True, debtor_no=ab828a197f30254f773a9ef8b6abd665
2025-08-04 09:18:57,002 - INFO - ✅ 通过合同号找到债务人: ab828a197f30254f773a9ef8b6abd665
2025-08-04 09:18:57,002 - INFO -    ✅ 匹配成功: debtor_no=ab828a197f30254f773a9ef8b6abd665, 方式=合同号
2025-08-04 09:18:57,004 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:57,004 - INFO -       债务人姓名: '程振华'
2025-08-04 09:18:57,004 - INFO -       合同号: '32655906'
2025-08-04 09:18:57,004 - INFO -       身份证号: ''
2025-08-04 09:18:57,005 - INFO - 🔍 使用合同号查询: 32655906
2025-08-04 09:18:57,007 - INFO -    数据库查询结果: found=True, debtor_no=ffb89bfd09410d89552ee86e4d4a1aa3
2025-08-04 09:18:57,008 - INFO - ✅ 通过合同号找到债务人: ffb89bfd09410d89552ee86e4d4a1aa3
2025-08-04 09:18:57,008 - INFO -    ✅ 匹配成功: debtor_no=ffb89bfd09410d89552ee86e4d4a1aa3, 方式=合同号
2025-08-04 09:18:57,009 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:57,009 - INFO -       债务人姓名: '李满英'
2025-08-04 09:18:57,009 - INFO -       合同号: '35652052'
2025-08-04 09:18:57,009 - INFO -       身份证号: ''
2025-08-04 09:18:57,010 - INFO - 🔍 使用合同号查询: 35652052
2025-08-04 09:18:57,013 - INFO -    数据库查询结果: found=True, debtor_no=46b4cedd817f35ae876af895ba676539
2025-08-04 09:18:57,014 - INFO - ✅ 通过合同号找到债务人: 46b4cedd817f35ae876af895ba676539
2025-08-04 09:18:57,014 - INFO -    ✅ 匹配成功: debtor_no=46b4cedd817f35ae876af895ba676539, 方式=合同号
2025-08-04 09:18:57,014 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:57,015 - INFO -       债务人姓名: '张华'
2025-08-04 09:18:57,015 - INFO -       合同号: '12279714'
2025-08-04 09:18:57,015 - INFO -       身份证号: ''
2025-08-04 09:18:57,015 - INFO - 🔍 使用合同号查询: 12279714
2025-08-04 09:18:57,019 - INFO -    数据库查询结果: found=True, debtor_no=f4dda55d5d38530bf52b00819b61307f
2025-08-04 09:18:57,020 - INFO - ✅ 通过合同号找到债务人: f4dda55d5d38530bf52b00819b61307f
2025-08-04 09:18:57,020 - INFO -    ✅ 匹配成功: debtor_no=f4dda55d5d38530bf52b00819b61307f, 方式=合同号
2025-08-04 09:18:57,022 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:57,022 - INFO -       债务人姓名: '张光绪'
2025-08-04 09:18:57,022 - INFO -       合同号: '17083180'
2025-08-04 09:18:57,022 - INFO -       身份证号: ''
2025-08-04 09:18:57,022 - INFO - 🔍 使用合同号查询: 17083180
2025-08-04 09:18:57,026 - INFO -    数据库查询结果: found=True, debtor_no=830cdba8575f496846b91e619a89c3cb
2025-08-04 09:18:57,027 - INFO - ✅ 通过合同号找到债务人: 830cdba8575f496846b91e619a89c3cb
2025-08-04 09:18:57,027 - INFO -    ✅ 匹配成功: debtor_no=830cdba8575f496846b91e619a89c3cb, 方式=合同号
2025-08-04 09:18:57,028 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:57,028 - INFO -       债务人姓名: '高力品'
2025-08-04 09:18:57,028 - INFO -       合同号: '39073726'
2025-08-04 09:18:57,028 - INFO -       身份证号: ''
2025-08-04 09:18:57,028 - INFO - 🔍 使用合同号查询: 39073726
2025-08-04 09:18:57,030 - INFO -    数据库查询结果: found=True, debtor_no=e86bd3388e9f55ef7c8d31a5dfc13de4
2025-08-04 09:18:57,031 - INFO - ✅ 通过合同号找到债务人: e86bd3388e9f55ef7c8d31a5dfc13de4
2025-08-04 09:18:57,031 - INFO -    ✅ 匹配成功: debtor_no=e86bd3388e9f55ef7c8d31a5dfc13de4, 方式=合同号
2025-08-04 09:18:57,031 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:57,031 - INFO -       债务人姓名: '何佳媛'
2025-08-04 09:18:57,032 - INFO -       合同号: '44002698'
2025-08-04 09:18:57,032 - INFO -       身份证号: ''
2025-08-04 09:18:57,032 - INFO - 🔍 使用合同号查询: 44002698
2025-08-04 09:18:57,033 - INFO -    数据库查询结果: found=True, debtor_no=7ad91ad62130d3762dfa3b080020303c
2025-08-04 09:18:57,034 - INFO - ✅ 通过合同号找到债务人: 7ad91ad62130d3762dfa3b080020303c
2025-08-04 09:18:57,034 - INFO -    ✅ 匹配成功: debtor_no=7ad91ad62130d3762dfa3b080020303c, 方式=合同号
2025-08-04 09:18:57,035 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:18:57,035 - INFO -       债务人姓名: '韩晶'
2025-08-04 09:18:57,035 - INFO -       合同号: '36502693'
2025-08-04 09:18:57,035 - INFO -       身份证号: ''
2025-08-04 09:18:57,035 - INFO - 🔍 使用合同号查询: 36502693
2025-08-04 09:18:57,040 - INFO -    数据库查询结果: found=True, debtor_no=b4a7a0932b8f909a5fa0475975cd70cb
2025-08-04 09:18:57,040 - INFO - ✅ 通过合同号找到债务人: b4a7a0932b8f909a5fa0475975cd70cb
2025-08-04 09:18:57,040 - INFO -    ✅ 匹配成功: debtor_no=b4a7a0932b8f909a5fa0475975cd70cb, 方式=合同号
2025-08-04 09:18:57,041 - INFO -    ✅ 成功处理 14 条对账明细记录
2025-08-04 09:18:57,042 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:18:57,042 - INFO - 🗂️ 扫描还款对账明细: 14 条记录
2025-08-04 09:18:57,043 - INFO - 处理目录: D:\民生4期回款\0801隆丰电催
2025-08-04 09:18:57,044 - INFO - 🔍 扫描还款对账明细Excel: 0801隆丰电催
2025-08-04 09:18:57,045 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:18:57,045 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:18:57,060 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:18:57,060 - INFO - ✅ 通过姓名找到唯一匹配: 付佳宇 → debtor_no: 226401882c097e264cde61f1b6ee056f
2025-08-04 09:18:57,060 - INFO - ✅ 通过还款对账明细找到debtor_no: 付佳宇 → 226401882c097e264cde61f1b6ee056f (姓名唯一匹配)
2025-08-04 09:18:57,060 - INFO - 📸 开始处理图片: 付佳宇1000.jpg
2025-08-04 09:18:57,060 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:18:57,061 - INFO - 🔍 开始OCR识别图片: 付佳宇1000.jpg
2025-08-04 09:18:57,061 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 付佳宇1000.jpg
2025-08-04 09:19:35,173 - INFO - ✅ 豆包AI分析成功: 👤402x 付佳宇 💳***1276 💰1000.00 ⏰2025-07-31 17:36:27
2025-08-04 09:19:35,173 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:19:35,173 - INFO - 👤 付款方识别成功: 【402x 付佳宇】
2025-08-04 09:19:35,174 - INFO - 💳 账户号码识别成功: ***1276
2025-08-04 09:19:35,174 - INFO - 💰 还款金额识别成功: 1000.00
2025-08-04 09:19:35,174 - INFO - ⏰ 还款时间识别成功: 2025-07-31 17:36:27 → 2025-07-31
2025-08-04 09:19:35,175 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:19:35,175 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:19:35,175 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:19:35,176 - INFO -    👤 还款人: 【402x 付佳宇】
2025-08-04 09:19:35,176 - INFO -    💳 还款账号: ***1276
2025-08-04 09:19:35,176 - INFO -    💰 还款金额: 1000.00
2025-08-04 09:19:35,176 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:19:35,177 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:19:35,177 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.82)
2025-08-04 09:19:35,177 - INFO - 🎯 图片处理完成: 付佳宇1000.jpg
2025-08-04 09:19:35,177 - INFO -    📋 最终结果 - 付款方: 【402x 付佳宇】, 账户: ***1276, 来源: 【本人还款】
2025-08-04 09:19:35,180 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x048'
2025-08-04 09:19:35,181 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\t`'
2025-08-04 09:19:35,181 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00>'
2025-08-04 09:19:35,181 - DEBUG - tag: Orientation (274) - type: short (3) - value: b'\x00\x00'
2025-08-04 09:19:35,181 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:19:35,181 - INFO - ✅ 通过姓名找到唯一匹配: 何佳媛 → debtor_no: 7ad91ad62130d3762dfa3b080020303c
2025-08-04 09:19:35,182 - INFO - ✅ 通过还款对账明细找到debtor_no: 何佳媛 → 7ad91ad62130d3762dfa3b080020303c (姓名唯一匹配)
2025-08-04 09:19:35,182 - INFO - 📸 开始处理图片: 何佳媛300.jpg
2025-08-04 09:19:35,182 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:19:35,182 - INFO - 🔍 开始OCR识别图片: 何佳媛300.jpg
2025-08-04 09:19:35,182 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 何佳媛300.jpg
2025-08-04 09:19:35,183 - DEBUG - tag: ImageWidth (256) - type: long (4) - value: b'\x00\x00\x048'
2025-08-04 09:19:35,183 - DEBUG - tag: ImageLength (257) - type: long (4) - value: b'\x00\x00\t`'
2025-08-04 09:19:35,183 - DEBUG - tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00>'
2025-08-04 09:19:35,183 - DEBUG - tag: Orientation (274) - type: short (3) - value: b'\x00\x00'
2025-08-04 09:19:56,726 - INFO - 🔄 使用文件名中的姓名: 何佳媛
2025-08-04 09:19:56,727 - INFO - ✅ 豆包AI分析成功: 👤何佳媛 💳***8326 💰300.00 ⏰2025-07-31 23:56:05
2025-08-04 09:19:56,727 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:19:56,727 - INFO - 👤 付款方识别成功: 【何佳媛】
2025-08-04 09:19:56,727 - INFO - 💳 账户号码识别成功: ***8326
2025-08-04 09:19:56,727 - INFO - 💰 还款金额识别成功: 300.00
2025-08-04 09:19:56,728 - INFO - ⏰ 还款时间识别成功: 2025-07-31 23:56:05 → 2025-07-31
2025-08-04 09:19:56,728 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:19:56,728 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:19:56,728 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:19:56,729 - INFO -    👤 还款人: 【何佳媛】
2025-08-04 09:19:56,729 - INFO -    💳 还款账号: ***8326
2025-08-04 09:19:56,729 - INFO -    💰 还款金额: 300.00
2025-08-04 09:19:56,729 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:19:56,729 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:19:56,730 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:19:56,730 - INFO - 🎯 图片处理完成: 何佳媛300.jpg
2025-08-04 09:19:56,730 - INFO -    📋 最终结果 - 付款方: 【何佳媛】, 账户: ***8326, 来源: 【本人还款】
2025-08-04 09:19:56,731 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:19:56,731 - INFO - ✅ 通过姓名找到唯一匹配: 吴俊男 → debtor_no: 8fe2cb6c9e708f4210bba82cf1880060
2025-08-04 09:19:56,731 - INFO - ✅ 通过还款对账明细找到debtor_no: 吴俊男 → 8fe2cb6c9e708f4210bba82cf1880060 (姓名唯一匹配)
2025-08-04 09:19:56,731 - INFO - 📸 开始处理图片: 吴俊男1661.5.jpg
2025-08-04 09:19:56,731 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:19:56,731 - INFO - 🔍 开始OCR识别图片: 吴俊男1661.5.jpg
2025-08-04 09:19:56,732 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 吴俊男1661.5.jpg
2025-08-04 09:20:13,943 - INFO - ✅ 豆包AI分析成功: 👤吴俊男 💳***7157 💰1661.50 ⏰2025-07-30 22:00:59
2025-08-04 09:20:13,943 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:20:13,943 - INFO - 👤 付款方识别成功: 【吴俊男】
2025-08-04 09:20:13,943 - INFO - 💳 账户号码识别成功: ***7157
2025-08-04 09:20:13,944 - INFO - 💰 还款金额识别成功: 1661.50
2025-08-04 09:20:13,944 - INFO - ⏰ 还款时间识别成功: 2025-07-30 22:00:59 → 2025-07-30
2025-08-04 09:20:13,944 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:20:13,944 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:20:13,944 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:20:13,945 - INFO -    👤 还款人: 【吴俊男】
2025-08-04 09:20:13,945 - INFO -    💳 还款账号: ***7157
2025-08-04 09:20:13,945 - INFO -    💰 还款金额: 1661.50
2025-08-04 09:20:13,945 - INFO -    ⏰ 还款时间: 2025-07-30
2025-08-04 09:20:13,945 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:20:13,946 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:20:13,946 - INFO - 🎯 图片处理完成: 吴俊男1661.5.jpg
2025-08-04 09:20:13,946 - INFO -    📋 最终结果 - 付款方: 【吴俊男】, 账户: ***7157, 来源: 【本人还款】
2025-08-04 09:20:13,947 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:20:13,947 - INFO - ✅ 通过姓名找到唯一匹配: 张光绪 → debtor_no: 830cdba8575f496846b91e619a89c3cb
2025-08-04 09:20:13,947 - INFO - ✅ 通过还款对账明细找到debtor_no: 张光绪 → 830cdba8575f496846b91e619a89c3cb (姓名唯一匹配)
2025-08-04 09:20:13,947 - INFO - 📸 开始处理图片: 张光绪588.67.jpg
2025-08-04 09:20:13,947 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:20:13,947 - INFO - 🔍 开始OCR识别图片: 张光绪588.67.jpg
2025-08-04 09:20:13,948 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张光绪588.67.jpg
2025-08-04 09:20:31,658 - INFO - 🔄 使用文件名中的姓名: 张光绪
2025-08-04 09:20:31,659 - INFO - ✅ 豆包AI分析成功: 👤张光绪 💳***6207 💰588.67 ⏰2025-07-31 14:43:54
2025-08-04 09:20:31,660 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:20:31,661 - INFO - 👤 付款方识别成功: 【张光绪】
2025-08-04 09:20:31,661 - INFO - 💳 账户号码识别成功: ***6207
2025-08-04 09:20:31,661 - INFO - 💰 还款金额识别成功: 588.67
2025-08-04 09:20:31,661 - INFO - ⏰ 还款时间识别成功: 2025-07-31 14:43:54 → 2025-07-31
2025-08-04 09:20:31,661 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:20:31,662 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:20:31,662 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:20:31,662 - INFO -    👤 还款人: 【张光绪】
2025-08-04 09:20:31,662 - INFO -    💳 还款账号: ***6207
2025-08-04 09:20:31,662 - INFO -    💰 还款金额: 588.67
2025-08-04 09:20:31,663 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:20:31,663 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:20:31,663 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:20:31,663 - INFO - 🎯 图片处理完成: 张光绪588.67.jpg
2025-08-04 09:20:31,671 - INFO -    📋 最终结果 - 付款方: 【张光绪】, 账户: ***6207, 来源: 【本人还款】
2025-08-04 09:20:31,672 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:20:31,672 - INFO - ✅ 通过姓名找到唯一匹配: 张华 → debtor_no: f4dda55d5d38530bf52b00819b61307f
2025-08-04 09:20:31,672 - INFO - ✅ 通过还款对账明细找到debtor_no: 张华 → f4dda55d5d38530bf52b00819b61307f (姓名唯一匹配)
2025-08-04 09:20:31,673 - INFO - 📸 开始处理图片: 张华1000.jpg
2025-08-04 09:20:31,673 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:20:31,673 - INFO - 🔍 开始OCR识别图片: 张华1000.jpg
2025-08-04 09:20:31,673 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张华1000.jpg
2025-08-04 09:21:10,804 - INFO - 🔄 使用文件名中的姓名: 张华
2025-08-04 09:21:10,804 - INFO - ✅ 豆包AI分析成功: 👤张华 💳***1730 💰1000.00 ⏰2025-07-31 15:20:39
2025-08-04 09:21:10,804 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:21:10,804 - INFO - 👤 付款方识别成功: 【张华】
2025-08-04 09:21:10,805 - INFO - 💳 账户号码识别成功: ***1730
2025-08-04 09:21:10,805 - INFO - 💰 还款金额识别成功: 1000.00
2025-08-04 09:21:10,805 - INFO - ⏰ 还款时间识别成功: 2025-07-31 15:20:39 → 2025-07-31
2025-08-04 09:21:10,805 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:21:10,805 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:21:10,805 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:21:10,806 - INFO -    👤 还款人: 【张华】
2025-08-04 09:21:10,806 - INFO -    💳 还款账号: ***1730
2025-08-04 09:21:10,806 - INFO -    💰 还款金额: 1000.00
2025-08-04 09:21:10,806 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:21:10,806 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:21:10,806 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:21:10,806 - INFO - 🎯 图片处理完成: 张华1000.jpg
2025-08-04 09:21:10,807 - INFO -    📋 最终结果 - 付款方: 【张华】, 账户: ***1730, 来源: 【本人还款】
2025-08-04 09:21:10,807 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:21:10,807 - INFO - ✅ 通过姓名找到唯一匹配: 李满英 → debtor_no: 46b4cedd817f35ae876af895ba676539
2025-08-04 09:21:10,807 - INFO - ✅ 通过还款对账明细找到debtor_no: 李满英 → 46b4cedd817f35ae876af895ba676539 (姓名唯一匹配)
2025-08-04 09:21:10,808 - INFO - 📸 开始处理图片: 李满英500.jpg
2025-08-04 09:21:10,808 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:21:10,808 - INFO - 🔍 开始OCR识别图片: 李满英500.jpg
2025-08-04 09:21:10,808 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李满英500.jpg
2025-08-04 09:21:39,095 - INFO - 🔄 使用文件名中的姓名: 李满英
2025-08-04 09:21:39,095 - INFO - ✅ 豆包AI分析成功: 👤李满英 💳***7608 💰500.00 ⏰2025-07-31 20:29:20
2025-08-04 09:21:39,095 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:21:39,096 - INFO - 👤 付款方识别成功: 【李满英】
2025-08-04 09:21:39,096 - INFO - 💳 账户号码识别成功: ***7608
2025-08-04 09:21:39,096 - INFO - 💰 还款金额识别成功: 500.00
2025-08-04 09:21:39,097 - INFO - ⏰ 还款时间识别成功: 2025-07-31 20:29:20 → 2025-07-31
2025-08-04 09:21:39,097 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:21:39,097 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:21:39,097 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:21:39,098 - INFO -    👤 还款人: 【李满英】
2025-08-04 09:21:39,098 - INFO -    💳 还款账号: ***7608
2025-08-04 09:21:39,098 - INFO -    💰 还款金额: 500.00
2025-08-04 09:21:39,098 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:21:39,098 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:21:39,099 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:21:39,099 - INFO - 🎯 图片处理完成: 李满英500.jpg
2025-08-04 09:21:39,099 - INFO -    📋 最终结果 - 付款方: 【李满英】, 账户: ***7608, 来源: 【本人还款】
2025-08-04 09:21:39,100 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:21:39,100 - INFO - ✅ 通过姓名找到唯一匹配: 王新裕 → debtor_no: e2498e856aafa265fab75acbe2888020
2025-08-04 09:21:39,100 - INFO - ✅ 通过还款对账明细找到debtor_no: 王新裕 → e2498e856aafa265fab75acbe2888020 (姓名唯一匹配)
2025-08-04 09:21:39,100 - INFO - 📸 开始处理图片: 王新裕500.jpg
2025-08-04 09:21:39,100 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:21:39,100 - INFO - 🔍 开始OCR识别图片: 王新裕500.jpg
2025-08-04 09:21:39,100 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王新裕500.jpg
2025-08-04 09:22:00,260 - INFO - 🔄 使用文件名中的姓名: 王新裕
2025-08-04 09:22:00,261 - INFO - ✅ 豆包AI分析成功: 👤王新裕 💳***0477 💰500.00 ⏰2025-07-31 16:54:58
2025-08-04 09:22:00,261 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:22:00,261 - INFO - 👤 付款方识别成功: 【王新裕】
2025-08-04 09:22:00,262 - INFO - 💳 账户号码识别成功: ***0477
2025-08-04 09:22:00,262 - INFO - 💰 还款金额识别成功: 500.00
2025-08-04 09:22:00,262 - INFO - ⏰ 还款时间识别成功: 2025-07-31 16:54:58 → 2025-07-31
2025-08-04 09:22:00,263 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:22:00,263 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:22:00,263 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:22:00,264 - INFO -    👤 还款人: 【王新裕】
2025-08-04 09:22:00,264 - INFO -    💳 还款账号: ***0477
2025-08-04 09:22:00,264 - INFO -    💰 还款金额: 500.00
2025-08-04 09:22:00,265 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:22:00,265 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:22:00,265 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:22:00,265 - INFO - 🎯 图片处理完成: 王新裕500.jpg
2025-08-04 09:22:00,266 - INFO -    📋 最终结果 - 付款方: 【王新裕】, 账户: ***0477, 来源: 【本人还款】
2025-08-04 09:22:00,266 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:22:00,266 - INFO - ✅ 通过姓名找到唯一匹配: 王红伟 → debtor_no: 43626dd0f4180956f4179cdd5a3e97da
2025-08-04 09:22:00,266 - INFO - ✅ 通过还款对账明细找到debtor_no: 王红伟 → 43626dd0f4180956f4179cdd5a3e97da (姓名唯一匹配)
2025-08-04 09:22:00,267 - INFO - 📸 开始处理图片: 王红伟744.jpg
2025-08-04 09:22:00,267 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:22:00,267 - INFO - 🔍 开始OCR识别图片: 王红伟744.jpg
2025-08-04 09:22:00,267 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王红伟744.jpg
2025-08-04 09:22:16,783 - INFO - ✅ 豆包AI分析成功: 👤肖桂霞 💳***2979 💰744.00 ⏰2025-07-31 18:04:22
2025-08-04 09:22:16,783 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:22:16,783 - INFO - 👤 付款方识别成功: 【肖桂霞】
2025-08-04 09:22:16,784 - INFO - 💳 账户号码识别成功: ***2979
2025-08-04 09:22:16,784 - INFO - 💰 还款金额识别成功: 744.00
2025-08-04 09:22:16,784 - INFO - ⏰ 还款时间识别成功: 2025-07-31 18:04:22 → 2025-07-31
2025-08-04 09:22:16,784 - INFO - 🔄 还款来源: 他人代还 (付款人[肖桂霞]与文件名[王红伟]不匹配)
2025-08-04 09:22:16,784 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:22:16,785 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\0801隆丰电催\王红伟744.jpg
2025-08-04 09:22:16,785 - WARNING -    状态: 不一致, 置信度: 0.40
2025-08-04 09:22:16,785 - WARNING -    姓名匹配: OCR[肖桂霞] vs 文件名[王红伟] = 0.00
2025-08-04 09:22:16,785 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-08-04 09:22:16,786 - WARNING -    金额匹配: OCR[744.00] vs 文件名[744] = 1.00
2025-08-04 09:22:16,786 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:22:16,786 - INFO -    👤 还款人: 【肖桂霞】
2025-08-04 09:22:16,786 - INFO -    💳 还款账号: ***2979
2025-08-04 09:22:16,786 - INFO -    💰 还款金额: 744.00
2025-08-04 09:22:16,787 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:22:16,787 - INFO -    🔄 还款来源: 【他人代还】
2025-08-04 09:22:16,787 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-08-04 09:22:16,787 - INFO - 🎯 图片处理完成: 王红伟744.jpg
2025-08-04 09:22:16,787 - INFO -    📋 最终结果 - 付款方: 【肖桂霞】, 账户: ***2979, 来源: 【他人代还】
2025-08-04 09:22:16,788 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:22:16,788 - INFO - ✅ 通过姓名找到唯一匹配: 程振华 → debtor_no: ffb89bfd09410d89552ee86e4d4a1aa3
2025-08-04 09:22:16,788 - INFO - ✅ 通过还款对账明细找到debtor_no: 程振华 → ffb89bfd09410d89552ee86e4d4a1aa3 (姓名唯一匹配)
2025-08-04 09:22:16,788 - INFO - 📸 开始处理图片: 程振华800.jpg
2025-08-04 09:22:16,789 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:22:16,789 - INFO - 🔍 开始OCR识别图片: 程振华800.jpg
2025-08-04 09:22:16,789 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 程振华800.jpg
2025-08-04 09:22:49,497 - INFO - 🔄 使用文件名中的姓名: 程振华
2025-08-04 09:22:49,497 - INFO - ✅ 豆包AI分析成功: 👤程振华 💳***7315 💰800.00 ⏰2025-08-01 07:03:52
2025-08-04 09:22:49,497 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:22:49,497 - INFO - 👤 付款方识别成功: 【程振华】
2025-08-04 09:22:49,498 - INFO - 💳 账户号码识别成功: ***7315
2025-08-04 09:22:49,498 - INFO - 💰 还款金额识别成功: 800.00
2025-08-04 09:22:49,498 - INFO - ⏰ 还款时间识别成功: 2025-08-01 07:03:52 → 2025-08-01
2025-08-04 09:22:49,498 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:22:49,498 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:22:49,499 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:22:49,499 - INFO -    👤 还款人: 【程振华】
2025-08-04 09:22:49,499 - INFO -    💳 还款账号: ***7315
2025-08-04 09:22:49,499 - INFO -    💰 还款金额: 800.00
2025-08-04 09:22:49,499 - INFO -    ⏰ 还款时间: 2025-08-01
2025-08-04 09:22:49,500 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:22:49,500 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:22:49,500 - INFO - 🎯 图片处理完成: 程振华800.jpg
2025-08-04 09:22:49,500 - INFO -    📋 最终结果 - 付款方: 【程振华】, 账户: ***7315, 来源: 【本人还款】
2025-08-04 09:22:49,501 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:22:49,501 - INFO - ✅ 通过姓名找到唯一匹配: 章诗嫄 → debtor_no: ab828a197f30254f773a9ef8b6abd665
2025-08-04 09:22:49,501 - INFO - ✅ 通过还款对账明细找到debtor_no: 章诗嫄 → ab828a197f30254f773a9ef8b6abd665 (姓名唯一匹配)
2025-08-04 09:22:49,502 - INFO - 📸 开始处理图片: 章诗嫄692.jpg
2025-08-04 09:22:49,502 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:22:49,503 - INFO - 🔍 开始OCR识别图片: 章诗嫄692.jpg
2025-08-04 09:22:49,503 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 章诗嫄692.jpg
2025-08-04 09:23:11,905 - INFO - ✅ 豆包AI分析成功: 👤单明晶 💳***0000 💰692.00 ⏰2025-07-31 22:48:00
2025-08-04 09:23:11,905 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:23:11,905 - INFO - 👤 付款方识别成功: 【单明晶】
2025-08-04 09:23:11,906 - INFO - 💳 账户号码识别成功: ***0000
2025-08-04 09:23:11,906 - INFO - 💰 还款金额识别成功: 692.00
2025-08-04 09:23:11,906 - INFO - ⏰ 还款时间识别成功: 2025-07-31 22:48:00 → 2025-07-31
2025-08-04 09:23:11,906 - INFO - 🔄 还款来源: 他人代还 (付款人[单明晶]与文件名[章诗嫄]不匹配)
2025-08-04 09:23:11,906 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:23:11,906 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\0801隆丰电催\章诗嫄692.jpg
2025-08-04 09:23:11,906 - WARNING -    状态: 不一致, 置信度: 0.40
2025-08-04 09:23:11,906 - WARNING -    姓名匹配: OCR[单明晶] vs 文件名[章诗嫄] = 0.00
2025-08-04 09:23:11,907 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-08-04 09:23:11,907 - WARNING -    金额匹配: OCR[692.00] vs 文件名[692] = 1.00
2025-08-04 09:23:11,907 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:23:11,907 - INFO -    👤 还款人: 【单明晶】
2025-08-04 09:23:11,907 - INFO -    💳 还款账号: ***0000
2025-08-04 09:23:11,907 - INFO -    💰 还款金额: 692.00
2025-08-04 09:23:11,907 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:23:11,907 - INFO -    🔄 还款来源: 【他人代还】
2025-08-04 09:23:11,907 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-08-04 09:23:11,908 - INFO - 🎯 图片处理完成: 章诗嫄692.jpg
2025-08-04 09:23:11,908 - INFO -    📋 最终结果 - 付款方: 【单明晶】, 账户: ***0000, 来源: 【他人代还】
2025-08-04 09:23:11,908 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:23:11,909 - INFO - ✅ 通过姓名找到唯一匹配: 赵殊弘 → debtor_no: b97979872ca84a47f008df87cf902cf6
2025-08-04 09:23:11,910 - INFO - ✅ 通过还款对账明细找到debtor_no: 赵殊弘 → b97979872ca84a47f008df87cf902cf6 (姓名唯一匹配)
2025-08-04 09:23:11,910 - INFO - 📸 开始处理图片: 赵殊弘1000.jpg
2025-08-04 09:23:11,910 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:23:11,911 - INFO - 🔍 开始OCR识别图片: 赵殊弘1000.jpg
2025-08-04 09:23:11,911 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 赵殊弘1000.jpg
2025-08-04 09:23:35,552 - INFO - ✅ 豆包AI分析成功: 👤赵殊弘 💳***1078 💰1000.00 ⏰2025-07-31 17:02:58
2025-08-04 09:23:35,553 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:23:35,553 - INFO - 👤 付款方识别成功: 【赵殊弘】
2025-08-04 09:23:35,553 - INFO - 💳 账户号码识别成功: ***1078
2025-08-04 09:23:35,553 - INFO - 💰 还款金额识别成功: 1000.00
2025-08-04 09:23:35,553 - INFO - ⏰ 还款时间识别成功: 2025-07-31 17:02:58 → 2025-07-31
2025-08-04 09:23:35,553 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:23:35,553 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:23:35,554 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:23:35,554 - INFO -    👤 还款人: 【赵殊弘】
2025-08-04 09:23:35,554 - INFO -    💳 还款账号: ***1078
2025-08-04 09:23:35,554 - INFO -    💰 还款金额: 1000.00
2025-08-04 09:23:35,554 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:23:35,554 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:23:35,555 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:23:35,555 - INFO - 🎯 图片处理完成: 赵殊弘1000.jpg
2025-08-04 09:23:35,555 - INFO -    📋 最终结果 - 付款方: 【赵殊弘】, 账户: ***1078, 来源: 【本人还款】
2025-08-04 09:23:35,556 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:23:35,556 - INFO - ✅ 通过姓名找到唯一匹配: 韩晶 → debtor_no: b4a7a0932b8f909a5fa0475975cd70cb
2025-08-04 09:23:35,556 - INFO - ✅ 通过还款对账明细找到debtor_no: 韩晶 → b4a7a0932b8f909a5fa0475975cd70cb (姓名唯一匹配)
2025-08-04 09:23:35,556 - INFO - 📸 开始处理图片: 韩晶.jpg
2025-08-04 09:23:35,556 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:23:35,556 - INFO - 🔍 开始OCR识别图片: 韩晶.jpg
2025-08-04 09:23:35,557 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 韩晶.jpg
2025-08-04 09:23:49,644 - INFO - ✅ 豆包AI分析成功: 👤*晶 💳***2378 💰500.00 ⏰2025-08-01 13:20:54
2025-08-04 09:23:49,645 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:23:49,646 - INFO - ⭐ 检测到带星号的付款方: 【*晶】，进行星号验证...
2025-08-04 09:23:49,647 - INFO - ✅ 通过姓名找到唯一匹配: 韩晶 → debtor_no: b4a7a0932b8f909a5fa0475975cd70cb
2025-08-04 09:23:49,648 - INFO - ✅ 星号还款人验证通过，将替换: *晶 → 韩晶
2025-08-04 09:23:49,648 - INFO - ✅ 星号付款方替换成功: 【*晶】 → 【韩晶】
2025-08-04 09:23:49,649 - INFO -    替换原因: 满足所有条件：含*号[*晶]，文件名债务人[韩晶]，Excel有debtor_no[b4a7a0932b8f909a5fa0475975cd70cb]，时间一致[2025-08-01]
2025-08-04 09:23:49,650 - INFO - 💳 账户号码识别成功: ***2378
2025-08-04 09:23:49,651 - INFO - 💰 还款金额识别成功: 500.00
2025-08-04 09:23:49,652 - INFO - ⏰ 还款时间识别成功: 2025-08-01 13:20:54 → 2025-08-01
2025-08-04 09:23:49,653 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:23:49,653 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:23:49,654 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:23:49,655 - INFO -    👤 还款人: 【韩晶】
2025-08-04 09:23:49,655 - INFO -    💳 还款账号: ***2378
2025-08-04 09:23:49,656 - INFO -    💰 还款金额: 500.00
2025-08-04 09:23:49,656 - INFO -    ⏰ 还款时间: 2025-08-01
2025-08-04 09:23:49,657 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:23:49,658 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.80)
2025-08-04 09:23:49,659 - INFO - 🎯 图片处理完成: 韩晶.jpg
2025-08-04 09:23:49,659 - INFO -    📋 最终结果 - 付款方: 【韩晶】, 账户: ***2378, 来源: 【本人还款】
2025-08-04 09:23:49,661 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:23:49,661 - INFO - ✅ 通过姓名找到唯一匹配: 高力品 → debtor_no: e86bd3388e9f55ef7c8d31a5dfc13de4
2025-08-04 09:23:49,662 - INFO - ✅ 通过还款对账明细找到debtor_no: 高力品 → e86bd3388e9f55ef7c8d31a5dfc13de4 (姓名唯一匹配)
2025-08-04 09:23:49,662 - INFO - 📸 开始处理图片: 高力品 500.jpg
2025-08-04 09:23:49,663 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:23:49,663 - INFO - 🔍 开始OCR识别图片: 高力品 500.jpg
2025-08-04 09:23:49,664 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 高力品 500.jpg
2025-08-04 09:24:09,433 - INFO - 🔄 使用文件名中的姓名: 高力品
2025-08-04 09:24:09,433 - INFO - ✅ 豆包AI分析成功: 👤高力品 💳***6672 💰500.00 ⏰2025-07-31 20:50:37
2025-08-04 09:24:09,433 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:24:09,434 - INFO - 👤 付款方识别成功: 【高力品】
2025-08-04 09:24:09,434 - INFO - 💳 账户号码识别成功: ***6672
2025-08-04 09:24:09,434 - INFO - 💰 还款金额识别成功: 500.00
2025-08-04 09:24:09,434 - INFO - ⏰ 还款时间识别成功: 2025-07-31 20:50:37 → 2025-07-31
2025-08-04 09:24:09,434 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:24:09,435 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:24:09,435 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:24:09,435 - INFO -    👤 还款人: 【高力品】
2025-08-04 09:24:09,435 - INFO -    💳 还款账号: ***6672
2025-08-04 09:24:09,435 - INFO -    💰 还款金额: 500.00
2025-08-04 09:24:09,436 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:24:09,436 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:24:09,436 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:24:09,436 - INFO - 🎯 图片处理完成: 高力品 500.jpg
2025-08-04 09:24:09,437 - INFO -    📋 最终结果 - 付款方: 【高力品】, 账户: ***6672, 来源: 【本人还款】
2025-08-04 09:24:09,437 - INFO - 🔍 扫描还款对账明细Excel: 马亮500
2025-08-04 09:24:09,438 - INFO -    ⚠️ 未找到还款对账明细Excel文件
2025-08-04 09:24:09,438 - INFO - 处理子文件夹（按文件夹名解析）: 马亮500
2025-08-04 09:24:09,438 - INFO - 找到代存图片: D:\民生4期回款\0801隆丰电催\马亮500\马亮代还.jpg
2025-08-04 09:24:09,439 - INFO - ✅ 通过姓名找到唯一匹配: 马亮 → debtor_no: 1af4bac3e15c4febaaeb36dfc013b0f9
2025-08-04 09:24:09,439 - INFO - ✅ 子目录通过还款对账明细找到debtor_no: 马亮 → 1af4bac3e15c4febaaeb36dfc013b0f9 (姓名唯一匹配)
2025-08-04 09:24:09,440 - INFO - 🚀 子目录OCR识别流程: 马亮500.jpg
2025-08-04 09:24:09,440 - INFO - 🔍 开始OCR识别图片: 马亮500.jpg
2025-08-04 09:24:09,441 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 马亮500.jpg
2025-08-04 09:24:31,734 - INFO - 🔄 使用文件名中的姓名: 马亮
2025-08-04 09:24:31,735 - INFO - ✅ 豆包AI分析成功: 👤马亮 💳***7225 💰500.00 ⏰2025-07-31 16:44:58
2025-08-04 09:24:31,735 - INFO - ✅ 子目录OCR识别成功，应用识别结果...
2025-08-04 09:24:31,735 - INFO - 👤 子目录付款方识别成功: 【马亮】
2025-08-04 09:24:31,735 - INFO - 💳 子目录账户识别成功: ***7225
2025-08-04 09:24:31,735 - INFO - 💰 子目录还款金额识别成功: 500.00
2025-08-04 09:24:31,735 - INFO - ⏰ 子目录还款时间识别成功: 2025-07-31 16:44:58 → 2025-07-31
2025-08-04 09:24:31,735 - INFO - 🔄 子目录还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:24:31,736 - INFO - 📊 子目录OCR结果应用完成:
2025-08-04 09:24:31,736 - INFO -    👤 还款人: 【马亮】
2025-08-04 09:24:31,736 - INFO -    💳 还款账号: ***7225
2025-08-04 09:24:31,736 - INFO -    💰 还款金额: 500.00
2025-08-04 09:24:31,736 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:24:31,736 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:24:31,737 - INFO - 基于文件夹名称创建记录: 马亮500 -> 马亮 500元
2025-08-04 09:24:31,737 - INFO - 📊 开始验证文件夹 '0801隆丰电催' 的数据一致性...
2025-08-04 09:24:31,758 - ERROR - ❌ 文件夹 '0801隆丰电催' 数据一致性验证失败!
2025-08-04 09:24:31,758 - ERROR -    Excel中的人员未在图片中找到: 王红伟, 章诗嫄, 付佳宇
2025-08-04 09:24:31,758 - ERROR -    图片中的人员在Excel中未找到: 402x 付佳宇, 肖桂霞, 单明晶
2025-08-04 09:24:31,758 - INFO - 🔍 开始验证OCR识别结果与Excel对比...
2025-08-04 09:24:31,778 - INFO - 📋 读取还款对账明细文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:24:31,778 - WARNING - ❌ 还款人未在Excel中找到: 肖桂霞
2025-08-04 09:24:31,779 - WARNING - ❌ 还款人未在Excel中找到: 单明晶
2025-08-04 09:24:31,779 - INFO - 📊 Excel验证完成 - 匹配: 12, 未匹配: 2
2025-08-04 09:24:31,779 - WARNING - ⚠️  Excel验证发现问题:
2025-08-04 09:24:31,779 - WARNING -    有 2 个还款人未在Excel中找到
2025-08-04 09:24:31,780 - INFO - 目录 0801隆丰电催 处理完成，共处理 14 个图片
2025-08-04 09:24:31,783 - DEBUG - 列 '还款账号' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:31,784 - DEBUG - 列 'debtor_no' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:31,791 - DEBUG - 列 '还款账号' (第6列) 已设置Excel文本格式
2025-08-04 09:24:31,792 - DEBUG - 列 'debtor_no' (第13列) 已设置Excel文本格式
2025-08-04 09:24:31,792 - INFO - 写入主要处理结果: 14 条记录
2025-08-04 09:24:31,793 - DEBUG - 列 '合同号' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:31,794 - DEBUG - 列 '身份证号' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:31,795 - DEBUG - 列 'debtor_no' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:31,799 - DEBUG - 列 '合同号' (第4列) 已设置Excel文本格式
2025-08-04 09:24:31,799 - DEBUG - 列 '身份证号' (第7列) 已设置Excel文本格式
2025-08-04 09:24:31,799 - DEBUG - 列 'debtor_no' (第9列) 已设置Excel文本格式
2025-08-04 09:24:31,799 - INFO - 写入还款对账明细: 14 条记录
2025-08-04 09:24:31,800 - INFO - 🔍 开始执行三重比对分析...
2025-08-04 09:24:31,800 - INFO - 📊 处理结果明细包含 14 个不同债务人
2025-08-04 09:24:31,800 - INFO - 📊 还款对账明细包含 14 个不同债务人
2025-08-04 09:24:31,800 - INFO - 🔍 执行分析1：债务人姓名匹配分析
2025-08-04 09:24:31,800 - INFO - 🔍 执行分析2：还款金额匹配分析
2025-08-04 09:24:31,801 - INFO - 🔍 执行分析3：结清验证分析
2025-08-04 09:24:31,801 - INFO - ✅ 三重比对分析完成
2025-08-04 09:24:31,801 - INFO -    📊 统计结果: {'还款对账明细债务人数': 14, '处理结果明细债务人数': 14, '共同债务人数': 14, '仅在还款对账明细中': 0, '仅在处理结果明细中': 0, '金额不一致债务人数': 0, '需要关注的结清案例': 0}
2025-08-04 09:24:31,804 - INFO - 写入金额匹配分析: 14 条记录
2025-08-04 09:24:31,805 - INFO - 写入比对分析摘要: 7 条记录
2025-08-04 09:24:31,805 - INFO - 开始生成人名统计，共有 14 条处理结果
2025-08-04 09:24:31,806 - INFO - 处理完成，共处理 1 个目录，生成 14 个人员统计
2025-08-04 09:24:31,806 - INFO - 处理的目录列表: ['0801隆丰电催']
2025-08-04 09:24:32,084 - INFO - 最终生成 14 个人员统计记录
2025-08-04 09:24:32,086 - DEBUG - 列 '合同号' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:32,087 - DEBUG - 列 '身份证号' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:32,088 - DEBUG - 列 'debtor_no' 已准备为文本格式（不添加单引号）
2025-08-04 09:24:32,090 - DEBUG - 列 '合同号' (第4列) 已设置Excel文本格式
2025-08-04 09:24:32,090 - DEBUG - 列 '身份证号' (第5列) 已设置Excel文本格式
2025-08-04 09:24:32,091 - DEBUG - 列 'debtor_no' (第7列) 已设置Excel文本格式
2025-08-04 09:24:32,091 - INFO - 写入人名统计信息: 14 条记录
2025-08-04 09:24:32,093 - INFO - 🎨 开始应用Excel格式设置...
2025-08-04 09:24:32,093 - INFO -    文件路径列索引: 2
2025-08-04 09:24:32,093 - INFO -    总行数: 15
2025-08-04 09:24:32,093 - INFO - 🔗 正在设置文件路径超链接...
2025-08-04 09:24:32,094 - INFO - ✅ 文件路径超链接设置完成，共设置 14 个超链接
2025-08-04 09:24:32,094 - INFO - 🎨 正在设置行颜色标记...
2025-08-04 09:24:32,094 - INFO - ✅ '处理结果明细'sheet格式设置完成
2025-08-04 09:24:32,094 - INFO - 🔴 正在设置结清验证状态列格式...
2025-08-04 09:24:32,095 - INFO - ✅ 结清验证状态列格式化完成：0 个单元格标红
2025-08-04 09:24:32,095 - INFO - ✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红
2025-08-04 09:24:32,095 - INFO - 📍 找到关键列 - 图片文件数量: 13, debtor_no: 9, 类型: 6
2025-08-04 09:24:32,096 - INFO -    🔴 标红第2行: 马亮 (图片文件数量为0)
2025-08-04 09:24:32,096 - INFO - ✅ 还款对账明细格式化完成
2025-08-04 09:24:32,096 - INFO -    🔴 图片文件缺失标红: 1 行
2025-08-04 09:24:32,096 - INFO -    🟠 结清验证问题标橙: 0 行
2025-08-04 09:24:32,096 - WARNING - ⚠️ 发现 1 条记录存在图片缺失或金额不一致，已在Excel中标红显示
2025-08-04 09:24:32,097 - WARNING -    💡 这些记录可能需要检查：图片缺失或金额差异
2025-08-04 09:24:32,098 - INFO - 写入目录统计信息: 1 条记录
2025-08-04 09:24:32,099 - INFO - 写入处理摘要信息
2025-08-04 09:24:32,127 - INFO - 增强版Excel文件保存成功: 0801隆丰电催_还款凭证解析_20250804_092431.xlsx
2025-08-04 09:24:32,128 - INFO - ✅ 成功生成Excel文件: 0801隆丰电催_还款凭证解析_20250804_092431.xlsx
2025-08-04 09:24:32,128 - INFO - ✅ 子文件夹 0801隆丰电催 处理完成，生成文件: 0801隆丰电催_还款凭证解析_20250804_092431.xlsx
2025-08-04 09:24:32,129 - INFO - ================================================================================
2025-08-04 09:24:32,129 - INFO - 🗂️  开始处理子文件夹: 0801隆丰诉保
2025-08-04 09:24:32,130 - INFO - ✅ 目录 0801隆丰诉保 中Excel文件验证通过: 还款对账明细（合作方用）.xlsx
2025-08-04 09:24:32,130 - INFO - 目录 0801隆丰诉保 Excel验证成功
2025-08-04 09:24:32,130 - INFO - 🔍 扫描还款对账明细Excel: 0801隆丰诉保
2025-08-04 09:24:32,131 - INFO -    📊 读取Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:24:32,158 - INFO -    📋 Excel文件包含 11 行数据
2025-08-04 09:24:32,158 - INFO -    🔑 识别的列名映射: {'还款时间': '还款时间', '债务人姓名': '债务人姓名', '合同号': '合同号码', '还款金额': '现金回收', '类型': '还款类型（部分还款/结清）', '身份证号': '身份证号码'}
2025-08-04 09:24:32,159 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,159 - INFO -       债务人姓名: '王丽娜'
2025-08-04 09:24:32,159 - INFO -       合同号: '22631369'
2025-08-04 09:24:32,159 - INFO -       身份证号: '230127198402091625'
2025-08-04 09:24:32,160 - INFO - 🔍 使用身份证号查询: 230127198402091625
2025-08-04 09:24:32,165 - INFO -    数据库查询结果: found=True, debtor_no=f346bbc1d42eba899e2f9b49728c7e5d
2025-08-04 09:24:32,165 - INFO - ✅ 通过身份证号找到债务人: f346bbc1d42eba899e2f9b49728c7e5d
2025-08-04 09:24:32,165 - INFO -    ✅ 匹配成功: debtor_no=f346bbc1d42eba899e2f9b49728c7e5d, 方式=身份证号
2025-08-04 09:24:32,166 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,166 - INFO -       债务人姓名: '门春莉'
2025-08-04 09:24:32,167 - INFO -       合同号: '8184227'
2025-08-04 09:24:32,167 - INFO -       身份证号: '210103196505052422'
2025-08-04 09:24:32,167 - INFO - 🔍 使用身份证号查询: 210103196505052422
2025-08-04 09:24:32,168 - INFO -    数据库查询结果: found=True, debtor_no=2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-08-04 09:24:32,168 - INFO - ✅ 通过身份证号找到债务人: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-08-04 09:24:32,168 - INFO -    ✅ 匹配成功: debtor_no=2d1b7a0ff0b2b3d589792a1bdc1ca1a3, 方式=身份证号
2025-08-04 09:24:32,169 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,169 - INFO -       债务人姓名: '李海洋'
2025-08-04 09:24:32,169 - INFO -       合同号: '20225820'
2025-08-04 09:24:32,169 - INFO -       身份证号: '232321198806048222'
2025-08-04 09:24:32,169 - INFO - 🔍 使用身份证号查询: 232321198806048222
2025-08-04 09:24:32,170 - INFO -    数据库查询结果: found=True, debtor_no=272784b0f5d65845e2a7ea4f1a581eb7
2025-08-04 09:24:32,170 - INFO - ✅ 通过身份证号找到债务人: 272784b0f5d65845e2a7ea4f1a581eb7
2025-08-04 09:24:32,170 - INFO -    ✅ 匹配成功: debtor_no=272784b0f5d65845e2a7ea4f1a581eb7, 方式=身份证号
2025-08-04 09:24:32,171 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,172 - INFO -       债务人姓名: '张艳玲'
2025-08-04 09:24:32,172 - INFO -       合同号: '20583539'
2025-08-04 09:24:32,172 - INFO -       身份证号: '220722199211260629'
2025-08-04 09:24:32,172 - INFO - 🔍 使用身份证号查询: 220722199211260629
2025-08-04 09:24:32,176 - INFO -    数据库查询结果: found=True, debtor_no=48f2be5a0d7f830abd682193369fa1b0
2025-08-04 09:24:32,176 - INFO - ✅ 通过身份证号找到债务人: 48f2be5a0d7f830abd682193369fa1b0
2025-08-04 09:24:32,177 - INFO -    ✅ 匹配成功: debtor_no=48f2be5a0d7f830abd682193369fa1b0, 方式=身份证号
2025-08-04 09:24:32,178 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,178 - INFO -       债务人姓名: '薛林'
2025-08-04 09:24:32,178 - INFO -       合同号: '17990592'
2025-08-04 09:24:32,178 - INFO -       身份证号: '210882198610023036'
2025-08-04 09:24:32,179 - INFO - 🔍 使用身份证号查询: 210882198610023036
2025-08-04 09:24:32,180 - INFO -    数据库查询结果: found=True, debtor_no=fc53e72eae1ea222a0bd38dfe0cf68aa
2025-08-04 09:24:32,181 - INFO - ✅ 通过身份证号找到债务人: fc53e72eae1ea222a0bd38dfe0cf68aa
2025-08-04 09:24:32,181 - INFO -    ✅ 匹配成功: debtor_no=fc53e72eae1ea222a0bd38dfe0cf68aa, 方式=身份证号
2025-08-04 09:24:32,181 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,182 - INFO -       债务人姓名: '张洪霞'
2025-08-04 09:24:32,182 - INFO -       合同号: '25944856'
2025-08-04 09:24:32,182 - INFO -       身份证号: '210104197305255824'
2025-08-04 09:24:32,182 - INFO - 🔍 使用身份证号查询: 210104197305255824
2025-08-04 09:24:32,183 - INFO -    数据库查询结果: found=True, debtor_no=9ab60f221df5a27be09c215db4c41d93
2025-08-04 09:24:32,183 - INFO - ✅ 通过身份证号找到债务人: 9ab60f221df5a27be09c215db4c41d93
2025-08-04 09:24:32,183 - INFO -    ✅ 匹配成功: debtor_no=9ab60f221df5a27be09c215db4c41d93, 方式=身份证号
2025-08-04 09:24:32,184 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,184 - INFO -       债务人姓名: '姜海涛'
2025-08-04 09:24:32,184 - INFO -       合同号: '6185387'
2025-08-04 09:24:32,184 - INFO -       身份证号: '152323197810013741'
2025-08-04 09:24:32,184 - INFO - 🔍 使用身份证号查询: 152323197810013741
2025-08-04 09:24:32,185 - INFO -    数据库查询结果: found=True, debtor_no=573dc866474470be8cd7760afe92b25c
2025-08-04 09:24:32,185 - INFO - ✅ 通过身份证号找到债务人: 573dc866474470be8cd7760afe92b25c
2025-08-04 09:24:32,186 - INFO -    ✅ 匹配成功: debtor_no=573dc866474470be8cd7760afe92b25c, 方式=身份证号
2025-08-04 09:24:32,186 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,186 - INFO -       债务人姓名: '穆晓旋'
2025-08-04 09:24:32,186 - INFO -       合同号: '33481646'
2025-08-04 09:24:32,187 - INFO -       身份证号: '230521199804053523'
2025-08-04 09:24:32,187 - INFO - 🔍 使用身份证号查询: 230521199804053523
2025-08-04 09:24:32,188 - INFO -    数据库查询结果: found=True, debtor_no=dd1db7f088ef217f71904fd9c921216b
2025-08-04 09:24:32,188 - INFO - ✅ 通过身份证号找到债务人: dd1db7f088ef217f71904fd9c921216b
2025-08-04 09:24:32,188 - INFO -    ✅ 匹配成功: debtor_no=dd1db7f088ef217f71904fd9c921216b, 方式=身份证号
2025-08-04 09:24:32,189 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,189 - INFO -       债务人姓名: '高成帅'
2025-08-04 09:24:32,189 - INFO -       合同号: '14549571'
2025-08-04 09:24:32,189 - INFO -       身份证号: '211022197602102013'
2025-08-04 09:24:32,190 - INFO - 🔍 使用身份证号查询: 211022197602102013
2025-08-04 09:24:32,191 - INFO -    数据库查询结果: found=True, debtor_no=c850017b09dcab502b3b31d06eef82c2
2025-08-04 09:24:32,191 - INFO - ✅ 通过身份证号找到债务人: c850017b09dcab502b3b31d06eef82c2
2025-08-04 09:24:32,191 - INFO -    ✅ 匹配成功: debtor_no=c850017b09dcab502b3b31d06eef82c2, 方式=身份证号
2025-08-04 09:24:32,192 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,192 - INFO -       债务人姓名: '王骁'
2025-08-04 09:24:32,192 - INFO -       合同号: '15336414'
2025-08-04 09:24:32,193 - INFO -       身份证号: '23012119900930001X'
2025-08-04 09:24:32,193 - INFO - 🔍 使用身份证号查询: 23012119900930001X
2025-08-04 09:24:32,194 - INFO -    数据库查询结果: found=True, debtor_no=476cbe5054463ceae355a2e38ac1dc45
2025-08-04 09:24:32,194 - INFO - ✅ 通过身份证号找到债务人: 476cbe5054463ceae355a2e38ac1dc45
2025-08-04 09:24:32,195 - INFO -    ✅ 匹配成功: debtor_no=476cbe5054463ceae355a2e38ac1dc45, 方式=身份证号
2025-08-04 09:24:32,195 - INFO -    🔍 准备匹配债务人:
2025-08-04 09:24:32,196 - INFO -       债务人姓名: '王晓艺'
2025-08-04 09:24:32,196 - INFO -       合同号: '13842535'
2025-08-04 09:24:32,196 - INFO -       身份证号: '220105199402031627'
2025-08-04 09:24:32,196 - INFO - 🔍 使用身份证号查询: 220105199402031627
2025-08-04 09:24:32,197 - INFO -    数据库查询结果: found=True, debtor_no=9b0d9f75301a2b48b7031871e3787123
2025-08-04 09:24:32,197 - INFO - ✅ 通过身份证号找到债务人: 9b0d9f75301a2b48b7031871e3787123
2025-08-04 09:24:32,197 - INFO -    ✅ 匹配成功: debtor_no=9b0d9f75301a2b48b7031871e3787123, 方式=身份证号
2025-08-04 09:24:32,198 - INFO -    ✅ 成功处理 11 条对账明细记录
2025-08-04 09:24:32,198 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:24:32,198 - INFO - 🗂️ 扫描还款对账明细: 11 条记录
2025-08-04 09:24:32,199 - INFO - 处理目录: D:\民生4期回款\0801隆丰诉保
2025-08-04 09:24:32,199 - INFO - 🔍 扫描还款对账明细Excel: 0801隆丰诉保
2025-08-04 09:24:32,200 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:24:32,200 - INFO -    ⏭️ 跳过已处理的Excel文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:24:32,201 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:24:32,201 - INFO - ✅ 通过姓名找到唯一匹配: 姜海涛 → debtor_no: 573dc866474470be8cd7760afe92b25c
2025-08-04 09:24:32,201 - INFO - ✅ 通过还款对账明细找到debtor_no: 姜海涛 → 573dc866474470be8cd7760afe92b25c (姓名唯一匹配)
2025-08-04 09:24:32,201 - INFO - 📸 开始处理图片: 姜海涛3741-1167.jpg
2025-08-04 09:24:32,202 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:24:32,202 - INFO - 🔍 开始OCR识别图片: 姜海涛3741-1167.jpg
2025-08-04 09:24:32,202 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 姜海涛3741-1167.jpg
2025-08-04 09:24:59,958 - INFO - ✅ 豆包AI分析成功: 👤姜海涛 💳***7551 💰1167.00 ⏰2025-07-31 14:30:10
2025-08-04 09:24:59,959 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:24:59,959 - INFO - 👤 付款方识别成功: 【姜海涛】
2025-08-04 09:24:59,959 - INFO - 💳 账户号码识别成功: ***7551
2025-08-04 09:24:59,959 - INFO - 💰 还款金额识别成功: 1167.00
2025-08-04 09:24:59,959 - INFO - ⏰ 还款时间识别成功: 2025-07-31 14:30:10 → 2025-07-31
2025-08-04 09:24:59,960 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:24:59,960 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:24:59,960 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:24:59,960 - INFO -    👤 还款人: 【姜海涛】
2025-08-04 09:24:59,961 - INFO -    💳 还款账号: ***7551
2025-08-04 09:24:59,961 - INFO -    💰 还款金额: 1167.00
2025-08-04 09:24:59,961 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:24:59,961 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:24:59,961 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:24:59,962 - INFO - 🎯 图片处理完成: 姜海涛3741-1167.jpg
2025-08-04 09:24:59,962 - INFO -    📋 最终结果 - 付款方: 【姜海涛】, 账户: ***7551, 来源: 【本人还款】
2025-08-04 09:24:59,962 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:24:59,963 - INFO - ✅ 通过姓名找到唯一匹配: 张洪霞 → debtor_no: 9ab60f221df5a27be09c215db4c41d93
2025-08-04 09:24:59,963 - INFO - ✅ 通过还款对账明细找到debtor_no: 张洪霞 → 9ab60f221df5a27be09c215db4c41d93 (姓名唯一匹配)
2025-08-04 09:24:59,963 - INFO - 📸 开始处理图片: 张洪霞5824-500.jpg
2025-08-04 09:24:59,963 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:24:59,963 - INFO - 🔍 开始OCR识别图片: 张洪霞5824-500.jpg
2025-08-04 09:24:59,963 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张洪霞5824-500.jpg
2025-08-04 09:25:14,449 - INFO - ✅ 豆包AI分析成功: 👤张洪霞 💳***0417 💰500.00 ⏰2025-08-01 10:41:00
2025-08-04 09:25:14,449 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:25:14,449 - INFO - 👤 付款方识别成功: 【张洪霞】
2025-08-04 09:25:14,449 - INFO - 💳 账户号码识别成功: ***0417
2025-08-04 09:25:14,449 - INFO - 💰 还款金额识别成功: 500.00
2025-08-04 09:25:14,450 - INFO - ⏰ 还款时间识别成功: 2025-08-01 10:41:00 → 2025-08-01
2025-08-04 09:25:14,450 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:25:14,450 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:25:14,450 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:25:14,451 - INFO -    👤 还款人: 【张洪霞】
2025-08-04 09:25:14,451 - INFO -    💳 还款账号: ***0417
2025-08-04 09:25:14,451 - INFO -    💰 还款金额: 500.00
2025-08-04 09:25:14,451 - INFO -    ⏰ 还款时间: 2025-08-01
2025-08-04 09:25:14,451 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:25:14,452 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:25:14,452 - INFO - 🎯 图片处理完成: 张洪霞5824-500.jpg
2025-08-04 09:25:14,452 - INFO -    📋 最终结果 - 付款方: 【张洪霞】, 账户: ***0417, 来源: 【本人还款】
2025-08-04 09:25:14,452 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:25:14,453 - INFO - ✅ 通过姓名找到唯一匹配: 张艳玲 → debtor_no: 48f2be5a0d7f830abd682193369fa1b0
2025-08-04 09:25:14,453 - INFO - ✅ 通过还款对账明细找到debtor_no: 张艳玲 → 48f2be5a0d7f830abd682193369fa1b0 (姓名唯一匹配)
2025-08-04 09:25:14,453 - INFO - 📸 开始处理图片: 张艳玲0629-584.jpg
2025-08-04 09:25:14,453 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:25:14,454 - INFO - 🔍 开始OCR识别图片: 张艳玲0629-584.jpg
2025-08-04 09:25:14,454 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 张艳玲0629-584.jpg
2025-08-04 09:25:48,539 - INFO - 🔄 使用文件名中的姓名: 张艳玲
2025-08-04 09:25:48,539 - INFO - ✅ 豆包AI分析成功: 👤张艳玲 💳***9481 💰584.00 ⏰
2025-08-04 09:25:48,540 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:25:48,540 - INFO - 👤 付款方识别成功: 【张艳玲】
2025-08-04 09:25:48,540 - INFO - 💳 账户号码识别成功: ***9481
2025-08-04 09:25:48,540 - INFO - 💰 还款金额识别成功: 584.00
2025-08-04 09:25:48,541 - WARNING - ⏰ OCR未识别到还款时间，保持为空
2025-08-04 09:25:48,541 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:25:48,541 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:25:48,541 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:25:48,541 - INFO -    👤 还款人: 【张艳玲】
2025-08-04 09:25:48,542 - INFO -    💳 还款账号: ***9481
2025-08-04 09:25:48,542 - INFO -    💰 还款金额: 584.00
2025-08-04 09:25:48,542 - INFO -    ⏰ 还款时间: 
2025-08-04 09:25:48,542 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:25:48,543 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:25:48,543 - INFO - 🎯 图片处理完成: 张艳玲0629-584.jpg
2025-08-04 09:25:48,543 - INFO -    📋 最终结果 - 付款方: 【张艳玲】, 账户: ***9481, 来源: 【本人还款】
2025-08-04 09:25:48,544 - DEBUG - STREAM b'IHDR' 16 13
2025-08-04 09:25:48,544 - DEBUG - STREAM b'sRGB' 41 1
2025-08-04 09:25:48,544 - DEBUG - STREAM b'eXIf' 54 142
2025-08-04 09:25:48,544 - DEBUG - STREAM b'iTXt' 208 495
2025-08-04 09:25:48,545 - DEBUG - STREAM b'IDAT' 715 16384
2025-08-04 09:25:48,545 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:25:48,546 - INFO - ✅ 通过姓名找到唯一匹配: 李海洋 → debtor_no: 272784b0f5d65845e2a7ea4f1a581eb7
2025-08-04 09:25:48,546 - INFO - ✅ 通过还款对账明细找到debtor_no: 李海洋 → 272784b0f5d65845e2a7ea4f1a581eb7 (姓名唯一匹配)
2025-08-04 09:25:48,546 - INFO - 📸 开始处理图片: 李海洋8222-950.png
2025-08-04 09:25:48,546 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:25:48,546 - INFO - 🔍 开始OCR识别图片: 李海洋8222-950.png
2025-08-04 09:25:48,547 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 李海洋8222-950.png
2025-08-04 09:25:48,547 - DEBUG - STREAM b'IHDR' 16 13
2025-08-04 09:25:48,547 - DEBUG - STREAM b'sRGB' 41 1
2025-08-04 09:25:48,547 - DEBUG - STREAM b'eXIf' 54 142
2025-08-04 09:25:48,548 - DEBUG - STREAM b'iTXt' 208 495
2025-08-04 09:25:48,548 - DEBUG - STREAM b'IDAT' 715 16384
2025-08-04 09:26:12,340 - INFO - 🔄 使用文件名中的姓名: 李海洋
2025-08-04 09:26:12,340 - INFO - ✅ 豆包AI分析成功: 👤李海洋 💳***1124 💰950.00 ⏰2025-07-31 21:59:53
2025-08-04 09:26:12,340 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:26:12,341 - INFO - 👤 付款方识别成功: 【李海洋】
2025-08-04 09:26:12,341 - INFO - 💳 账户号码识别成功: ***1124
2025-08-04 09:26:12,341 - INFO - 💰 还款金额识别成功: 950.00
2025-08-04 09:26:12,341 - INFO - ⏰ 还款时间识别成功: 2025-07-31 21:59:53 → 2025-07-31
2025-08-04 09:26:12,342 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:26:12,342 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:26:12,342 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:26:12,342 - INFO -    👤 还款人: 【李海洋】
2025-08-04 09:26:12,343 - INFO -    💳 还款账号: ***1124
2025-08-04 09:26:12,343 - INFO -    💰 还款金额: 950.00
2025-08-04 09:26:12,343 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:26:12,343 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:26:12,343 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:26:12,344 - INFO - 🎯 图片处理完成: 李海洋8222-950.png
2025-08-04 09:26:12,344 - INFO -    📋 最终结果 - 付款方: 【李海洋】, 账户: ***1124, 来源: 【本人还款】
2025-08-04 09:26:12,344 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:26:12,344 - INFO - ✅ 通过姓名找到唯一匹配: 王丽娜 → debtor_no: f346bbc1d42eba899e2f9b49728c7e5d
2025-08-04 09:26:12,345 - INFO - ✅ 通过还款对账明细找到debtor_no: 王丽娜 → f346bbc1d42eba899e2f9b49728c7e5d (姓名唯一匹配)
2025-08-04 09:26:12,345 - INFO - 📸 开始处理图片: 王丽娜1625-3866.jpg
2025-08-04 09:26:12,345 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:26:12,345 - INFO - 🔍 开始OCR识别图片: 王丽娜1625-3866.jpg
2025-08-04 09:26:12,345 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王丽娜1625-3866.jpg
2025-08-04 09:26:28,569 - INFO - 🔄 使用文件名中的姓名: 王丽娜
2025-08-04 09:26:28,569 - INFO - ✅ 豆包AI分析成功: 👤王丽娜 💳***3274 💰3866.00 ⏰2025-07-31 11:08:49
2025-08-04 09:26:28,569 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:26:28,569 - INFO - 👤 付款方识别成功: 【王丽娜】
2025-08-04 09:26:28,570 - INFO - 💳 账户号码识别成功: ***3274
2025-08-04 09:26:28,570 - INFO - 💰 还款金额识别成功: 3866.00
2025-08-04 09:26:28,570 - INFO - ⏰ 还款时间识别成功: 2025-07-31 11:08:49 → 2025-07-31
2025-08-04 09:26:28,570 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:26:28,570 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:26:28,571 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:26:28,571 - INFO -    👤 还款人: 【王丽娜】
2025-08-04 09:26:28,571 - INFO -    💳 还款账号: ***3274
2025-08-04 09:26:28,571 - INFO -    💰 还款金额: 3866.00
2025-08-04 09:26:28,571 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:26:28,572 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:26:28,572 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:26:28,572 - INFO - 🎯 图片处理完成: 王丽娜1625-3866.jpg
2025-08-04 09:26:28,572 - INFO -    📋 最终结果 - 付款方: 【王丽娜】, 账户: ***3274, 来源: 【本人还款】
2025-08-04 09:26:28,573 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:26:28,573 - INFO - ✅ 通过姓名找到唯一匹配: 王晓艺 → debtor_no: 9b0d9f75301a2b48b7031871e3787123
2025-08-04 09:26:28,573 - INFO - ✅ 通过还款对账明细找到debtor_no: 王晓艺 → 9b0d9f75301a2b48b7031871e3787123 (姓名唯一匹配)
2025-08-04 09:26:28,573 - INFO - 📸 开始处理图片: 王晓艺1627-659.jpg
2025-08-04 09:26:28,573 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:26:28,574 - INFO - 🔍 开始OCR识别图片: 王晓艺1627-659.jpg
2025-08-04 09:26:28,574 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王晓艺1627-659.jpg
2025-08-04 09:27:14,075 - INFO - 🔄 使用文件名中的姓名: 王晓艺
2025-08-04 09:27:14,075 - ERROR - ❌ 豆包AI处理异常: 'int' object has no attribute 'strip'
2025-08-04 09:27:14,075 - ERROR - ❌ OCR识别失败，所有字段保持为空
2025-08-04 09:27:14,076 - INFO - 🎯 图片处理完成: 王晓艺1627-659.jpg
2025-08-04 09:27:14,076 - INFO -    📋 最终结果 - 付款方: 【】, 账户: ***, 来源: 【】
2025-08-04 09:27:14,076 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:27:14,077 - INFO - ✅ 通过姓名找到唯一匹配: 王骁 → debtor_no: 476cbe5054463ceae355a2e38ac1dc45
2025-08-04 09:27:14,077 - INFO - ✅ 通过还款对账明细找到debtor_no: 王骁 → 476cbe5054463ceae355a2e38ac1dc45 (姓名唯一匹配)
2025-08-04 09:27:14,077 - INFO - 📸 开始处理图片: 王骁001X-684.jpg
2025-08-04 09:27:14,077 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:27:14,077 - INFO - 🔍 开始OCR识别图片: 王骁001X-684.jpg
2025-08-04 09:27:14,078 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 王骁001X-684.jpg
2025-08-04 09:27:28,485 - INFO - ✅ 豆包AI分析成功: 👤王** 💳***7543 💰684.00 ⏰2025-08-01 15:03:03
2025-08-04 09:27:28,486 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:27:28,486 - INFO - ⭐ 检测到带星号的付款方: 【王**】，进行星号验证...
2025-08-04 09:27:28,486 - INFO - ✅ 通过姓名找到唯一匹配: 王骁 → debtor_no: 476cbe5054463ceae355a2e38ac1dc45
2025-08-04 09:27:28,486 - INFO - ✅ 星号还款人验证通过，将替换: 王** → 王骁
2025-08-04 09:27:28,486 - INFO - ✅ 星号付款方替换成功: 【王**】 → 【王骁】
2025-08-04 09:27:28,487 - INFO -    替换原因: 满足所有条件：含*号[王**]，文件名债务人[王骁]，Excel有debtor_no[476cbe5054463ceae355a2e38ac1dc45]，时间一致[2025-08-01]
2025-08-04 09:27:28,487 - INFO - 💳 账户号码识别成功: ***7543
2025-08-04 09:27:28,487 - INFO - 💰 还款金额识别成功: 684.00
2025-08-04 09:27:28,487 - INFO - ⏰ 还款时间识别成功: 2025-08-01 15:03:03 → 2025-08-01
2025-08-04 09:27:28,488 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:27:28,488 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:27:28,488 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:27:28,488 - INFO -    👤 还款人: 【王骁】
2025-08-04 09:27:28,488 - INFO -    💳 还款账号: ***7543
2025-08-04 09:27:28,489 - INFO -    💰 还款金额: 684.00
2025-08-04 09:27:28,489 - INFO -    ⏰ 还款时间: 2025-08-01
2025-08-04 09:27:28,489 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:27:28,489 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.82)
2025-08-04 09:27:28,489 - INFO - 🎯 图片处理完成: 王骁001X-684.jpg
2025-08-04 09:27:28,490 - INFO -    📋 最终结果 - 付款方: 【王骁】, 账户: ***7543, 来源: 【本人还款】
2025-08-04 09:27:28,490 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:27:28,490 - INFO - ✅ 通过姓名找到唯一匹配: 穆晓旋 → debtor_no: dd1db7f088ef217f71904fd9c921216b
2025-08-04 09:27:28,490 - INFO - ✅ 通过还款对账明细找到debtor_no: 穆晓旋 → dd1db7f088ef217f71904fd9c921216b (姓名唯一匹配)
2025-08-04 09:27:28,491 - INFO - 📸 开始处理图片: 穆晓旋3523-1500.jpg
2025-08-04 09:27:28,491 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:27:28,491 - INFO - 🔍 开始OCR识别图片: 穆晓旋3523-1500.jpg
2025-08-04 09:27:28,491 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 穆晓旋3523-1500.jpg
2025-08-04 09:27:48,319 - INFO - ✅ 豆包AI分析成功: 👤穆晓旋 💳***6141 💰1500.00 ⏰2025-08-01 12:25:34
2025-08-04 09:27:48,320 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:27:48,320 - INFO - 👤 付款方识别成功: 【穆晓旋】
2025-08-04 09:27:48,320 - INFO - 💳 账户号码识别成功: ***6141
2025-08-04 09:27:48,320 - INFO - 💰 还款金额识别成功: 1500.00
2025-08-04 09:27:48,321 - INFO - ⏰ 还款时间识别成功: 2025-08-01 12:25:34 → 2025-08-01
2025-08-04 09:27:48,321 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:27:48,321 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:27:48,321 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:27:48,321 - INFO -    👤 还款人: 【穆晓旋】
2025-08-04 09:27:48,322 - INFO -    💳 还款账号: ***6141
2025-08-04 09:27:48,322 - INFO -    💰 还款金额: 1500.00
2025-08-04 09:27:48,322 - INFO -    ⏰ 还款时间: 2025-08-01
2025-08-04 09:27:48,322 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:27:48,322 - INFO -    ✅ 匹配验证: 一致 (置信度: 1.00)
2025-08-04 09:27:48,323 - INFO - 🎯 图片处理完成: 穆晓旋3523-1500.jpg
2025-08-04 09:27:48,323 - INFO -    📋 最终结果 - 付款方: 【穆晓旋】, 账户: ***6141, 来源: 【本人还款】
2025-08-04 09:27:48,323 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:27:48,323 - INFO - ✅ 通过姓名找到唯一匹配: 薛林 → debtor_no: fc53e72eae1ea222a0bd38dfe0cf68aa
2025-08-04 09:27:48,324 - INFO - ✅ 通过还款对账明细找到debtor_no: 薛林 → fc53e72eae1ea222a0bd38dfe0cf68aa (姓名唯一匹配)
2025-08-04 09:27:48,324 - INFO - 📸 开始处理图片: 薛林3036-3000.jpg
2025-08-04 09:27:48,324 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:27:48,324 - INFO - 🔍 开始OCR识别图片: 薛林3036-3000.jpg
2025-08-04 09:27:48,324 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 薛林3036-3000.jpg
2025-08-04 09:28:04,659 - INFO - ✅ 豆包AI分析成功: 👤张磊 💳***7384 💰3000.00 ⏰2025-07-31 15:29:28
2025-08-04 09:28:04,660 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:28:04,660 - INFO - 👤 付款方识别成功: 【张磊】
2025-08-04 09:28:04,660 - INFO - 💳 账户号码识别成功: ***7384
2025-08-04 09:28:04,660 - INFO - 💰 还款金额识别成功: 3000.00
2025-08-04 09:28:04,661 - INFO - ⏰ 还款时间识别成功: 2025-07-31 15:29:28 → 2025-07-31
2025-08-04 09:28:04,661 - INFO - 🔄 还款来源: 他人代还 (付款人[张磊]与文件名[薛林]不匹配)
2025-08-04 09:28:04,661 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:28:04,661 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\0801隆丰诉保\薛林3036-3000.jpg
2025-08-04 09:28:04,661 - WARNING -    状态: 不一致, 置信度: 0.40
2025-08-04 09:28:04,662 - WARNING -    姓名匹配: OCR[张磊] vs 文件名[薛林] = 0.00
2025-08-04 09:28:04,662 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.00)
2025-08-04 09:28:04,662 - WARNING -    金额匹配: OCR[3000.00] vs 文件名[3000] = 1.00
2025-08-04 09:28:04,662 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:28:04,663 - INFO -    👤 还款人: 【张磊】
2025-08-04 09:28:04,663 - INFO -    💳 还款账号: ***7384
2025-08-04 09:28:04,663 - INFO -    💰 还款金额: 3000.00
2025-08-04 09:28:04,663 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:28:04,664 - INFO -    🔄 还款来源: 【他人代还】
2025-08-04 09:28:04,664 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-08-04 09:28:04,664 - INFO - 🎯 图片处理完成: 薛林3036-3000.jpg
2025-08-04 09:28:04,664 - INFO -    📋 最终结果 - 付款方: 【张磊】, 账户: ***7384, 来源: 【他人代还】
2025-08-04 09:28:04,665 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:28:04,665 - INFO - ✅ 通过姓名找到唯一匹配: 门春莉 → debtor_no: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-08-04 09:28:04,665 - INFO - ✅ 通过还款对账明细找到debtor_no: 门春莉 → 2d1b7a0ff0b2b3d589792a1bdc1ca1a3 (姓名唯一匹配)
2025-08-04 09:28:04,665 - INFO - 📸 开始处理图片: 门春莉2422-10000.jpg
2025-08-04 09:28:04,665 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:28:04,666 - INFO - 🔍 开始OCR识别图片: 门春莉2422-10000.jpg
2025-08-04 09:28:04,666 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 门春莉2422-10000.jpg
2025-08-04 09:28:23,487 - INFO - ✅ 豆包AI分析成功: 👤*春莉 💳***8178 💰10000.00 ⏰2025-07-31 19:44:30
2025-08-04 09:28:23,487 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:28:23,487 - INFO - ⭐ 检测到带星号的付款方: 【*春莉】，进行星号验证...
2025-08-04 09:28:23,487 - INFO - ✅ 通过姓名找到唯一匹配: 门春莉 → debtor_no: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3
2025-08-04 09:28:23,488 - INFO - ✅ 星号还款人验证通过，将替换: *春莉 → 门春莉
2025-08-04 09:28:23,488 - INFO - ✅ 星号付款方替换成功: 【*春莉】 → 【门春莉】
2025-08-04 09:28:23,488 - INFO -    替换原因: 满足所有条件：含*号[*春莉]，文件名债务人[门春莉]，Excel有debtor_no[2d1b7a0ff0b2b3d589792a1bdc1ca1a3]，时间一致[2025-07-31]
2025-08-04 09:28:23,488 - INFO - 💳 账户号码识别成功: ***8178
2025-08-04 09:28:23,488 - INFO - 💰 还款金额识别成功: 10000.00
2025-08-04 09:28:23,488 - INFO - ⏰ 还款时间识别成功: 2025-07-31 19:44:30 → 2025-07-31
2025-08-04 09:28:23,489 - INFO - 🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)
2025-08-04 09:28:23,489 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:28:23,489 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:28:23,489 - INFO -    👤 还款人: 【门春莉】
2025-08-04 09:28:23,490 - INFO -    💳 还款账号: ***8178
2025-08-04 09:28:23,490 - INFO -    💰 还款金额: 10000.00
2025-08-04 09:28:23,490 - INFO -    ⏰ 还款时间: 2025-07-31
2025-08-04 09:28:23,490 - INFO -    🔄 还款来源: 【本人还款】
2025-08-04 09:28:23,490 - INFO -    ✅ 匹配验证: 一致 (置信度: 0.88)
2025-08-04 09:28:23,490 - INFO - 🎯 图片处理完成: 门春莉2422-10000.jpg
2025-08-04 09:28:23,490 - INFO -    📋 最终结果 - 付款方: 【门春莉】, 账户: ***8178, 来源: 【本人还款】
2025-08-04 09:28:23,491 - INFO - 📌 强制使用OCR识别模式，不使用文件名解析结果
2025-08-04 09:28:23,491 - INFO - ✅ 通过姓名找到唯一匹配: 高成帅 → debtor_no: c850017b09dcab502b3b31d06eef82c2
2025-08-04 09:28:23,491 - INFO - ✅ 通过还款对账明细找到debtor_no: 高成帅 → c850017b09dcab502b3b31d06eef82c2 (姓名唯一匹配)
2025-08-04 09:28:23,492 - INFO - 📸 开始处理图片: 高成帅2013-2567.jpg
2025-08-04 09:28:23,492 - INFO - 🚀 启动OCR识别流程...
2025-08-04 09:28:23,493 - INFO - 🔍 开始OCR识别图片: 高成帅2013-2567.jpg
2025-08-04 09:28:23,493 - INFO - 🤖 使用豆包Seed-1.6 AI处理图片: 高成帅2013-2567.jpg
2025-08-04 09:28:43,690 - INFO - ✅ 豆包AI分析成功: 👤高荣概 💳***2306 💰2567.00 ⏰2025-08-01 14:10:38
2025-08-04 09:28:43,690 - INFO - ✅ OCR识别成功，开始应用识别结果...
2025-08-04 09:28:43,691 - INFO - 👤 付款方识别成功: 【高荣概】
2025-08-04 09:28:43,691 - INFO - 💳 账户号码识别成功: ***2306
2025-08-04 09:28:43,691 - INFO - 💰 还款金额识别成功: 2567.00
2025-08-04 09:28:43,691 - INFO - ⏰ 还款时间识别成功: 2025-08-01 14:10:38 → 2025-08-01
2025-08-04 09:28:43,692 - INFO - 🔄 还款来源: 他人代还 (付款人[高荣概]与文件名[高成帅]不匹配)
2025-08-04 09:28:43,692 - INFO - 🔍 开始OCR与文件名匹配验证...
2025-08-04 09:28:43,692 - WARNING - ❌ OCR与文件名不匹配: D:\民生4期回款\0801隆丰诉保\高成帅2013-2567.jpg
2025-08-04 09:28:43,692 - WARNING -    状态: 不一致, 置信度: 0.40
2025-08-04 09:28:43,693 - WARNING -    姓名匹配: OCR[高荣概] vs 文件名[高成帅] = 0.00
2025-08-04 09:28:43,693 - WARNING -    ⚠️ 姓名不匹配: 不匹配(相似度仅0.33)
2025-08-04 09:28:43,693 - WARNING -    金额匹配: OCR[2567.00] vs 文件名[2567] = 1.00
2025-08-04 09:28:43,693 - INFO - 📊 OCR结果应用完成:
2025-08-04 09:28:43,694 - INFO -    👤 还款人: 【高荣概】
2025-08-04 09:28:43,694 - INFO -    💳 还款账号: ***2306
2025-08-04 09:28:43,694 - INFO -    💰 还款金额: 2567.00
2025-08-04 09:28:43,694 - INFO -    ⏰ 还款时间: 2025-08-01
2025-08-04 09:28:43,694 - INFO -    🔄 还款来源: 【他人代还】
2025-08-04 09:28:43,695 - INFO -    ✅ 匹配验证: 不一致 (置信度: 0.40)
2025-08-04 09:28:43,695 - INFO - 🎯 图片处理完成: 高成帅2013-2567.jpg
2025-08-04 09:28:43,695 - INFO -    📋 最终结果 - 付款方: 【高荣概】, 账户: ***2306, 来源: 【他人代还】
2025-08-04 09:28:43,696 - INFO - 📊 开始验证文件夹 '0801隆丰诉保' 的数据一致性...
2025-08-04 09:28:43,735 - ERROR - ❌ 文件夹 '0801隆丰诉保' 数据一致性验证失败!
2025-08-04 09:28:43,735 - ERROR -    Excel中的人员未在图片中找到: 高成帅, 薛林
2025-08-04 09:28:43,735 - ERROR -    图片中的人员在Excel中未找到: 张磊, 高荣概
2025-08-04 09:28:43,736 - INFO - 🔍 开始验证OCR识别结果与Excel对比...
2025-08-04 09:28:43,764 - INFO - 📋 读取还款对账明细文件: 还款对账明细（合作方用）.xlsx
2025-08-04 09:28:43,765 - WARNING - ❌ 还款人未在Excel中找到: 张磊
2025-08-04 09:28:43,765 - WARNING - ❌ 还款人未在Excel中找到: 高荣概
2025-08-04 09:28:43,765 - INFO - 📊 Excel验证完成 - 匹配: 8, 未匹配: 2
2025-08-04 09:28:43,766 - WARNING - ⚠️  Excel验证发现问题:
2025-08-04 09:28:43,766 - WARNING -    有 2 个还款人未在Excel中找到
2025-08-04 09:28:43,766 - INFO - 目录 0801隆丰诉保 处理完成，共处理 11 个图片
2025-08-04 09:28:43,768 - INFO - 🟠 发现结清验证问题: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3 - 还款10000.0，剩余17000.0
2025-08-04 09:28:43,768 - INFO - ✅ 设置结清验证状态: 2d1b7a0ff0b2b3d589792a1bdc1ca1a3 - 结清但还款金额小于剩余本金，需要关注
2025-08-04 09:28:43,770 - DEBUG - 列 '还款账号' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:43,771 - DEBUG - 列 'debtor_no' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:43,774 - DEBUG - 列 '还款账号' (第6列) 已设置Excel文本格式
2025-08-04 09:28:43,774 - DEBUG - 列 'debtor_no' (第13列) 已设置Excel文本格式
2025-08-04 09:28:43,774 - INFO - 写入主要处理结果: 11 条记录
2025-08-04 09:28:43,776 - DEBUG - 列 '合同号' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:43,777 - DEBUG - 列 '身份证号' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:43,778 - DEBUG - 列 'debtor_no' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:43,782 - DEBUG - 列 '合同号' (第4列) 已设置Excel文本格式
2025-08-04 09:28:43,783 - DEBUG - 列 '身份证号' (第7列) 已设置Excel文本格式
2025-08-04 09:28:43,783 - DEBUG - 列 'debtor_no' (第9列) 已设置Excel文本格式
2025-08-04 09:28:43,783 - INFO - 写入还款对账明细: 11 条记录
2025-08-04 09:28:43,784 - INFO - 🔍 开始执行三重比对分析...
2025-08-04 09:28:43,784 - INFO - 📊 处理结果明细包含 11 个不同债务人
2025-08-04 09:28:43,784 - INFO - 📊 还款对账明细包含 11 个不同债务人
2025-08-04 09:28:43,784 - INFO - 🔍 执行分析1：债务人姓名匹配分析
2025-08-04 09:28:43,784 - INFO - 🔍 执行分析2：还款金额匹配分析
2025-08-04 09:28:43,785 - INFO - 🔍 执行分析3：结清验证分析
2025-08-04 09:28:43,785 - INFO - ✅ 三重比对分析完成
2025-08-04 09:28:43,785 - INFO -    📊 统计结果: {'还款对账明细债务人数': 11, '处理结果明细债务人数': 11, '共同债务人数': 11, '仅在还款对账明细中': 0, '仅在处理结果明细中': 0, '金额不一致债务人数': 1, '需要关注的结清案例': 1}
2025-08-04 09:28:43,788 - INFO - 写入金额匹配分析: 11 条记录
2025-08-04 09:28:43,790 - INFO - 写入结清验证分析: 1 条记录
2025-08-04 09:28:43,791 - INFO - 写入比对分析摘要: 7 条记录
2025-08-04 09:28:43,791 - INFO - 开始生成人名统计，共有 11 条处理结果
2025-08-04 09:28:43,791 - INFO - 处理完成，共处理 1 个目录，生成 11 个人员统计
2025-08-04 09:28:43,791 - INFO - 处理的目录列表: ['0801隆丰诉保']
2025-08-04 09:28:44,103 - INFO - 最终生成 11 个人员统计记录
2025-08-04 09:28:44,108 - DEBUG - 列 '合同号' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:44,109 - DEBUG - 列 '身份证号' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:44,112 - DEBUG - 列 'debtor_no' 已准备为文本格式（不添加单引号）
2025-08-04 09:28:44,115 - DEBUG - 列 '合同号' (第4列) 已设置Excel文本格式
2025-08-04 09:28:44,115 - DEBUG - 列 '身份证号' (第5列) 已设置Excel文本格式
2025-08-04 09:28:44,116 - DEBUG - 列 'debtor_no' (第7列) 已设置Excel文本格式
2025-08-04 09:28:44,116 - INFO - 写入人名统计信息: 11 条记录
2025-08-04 09:28:44,117 - INFO - 🎨 开始应用Excel格式设置...
2025-08-04 09:28:44,117 - INFO -    文件路径列索引: 2
2025-08-04 09:28:44,117 - INFO -    总行数: 12
2025-08-04 09:28:44,117 - INFO - 🔗 正在设置文件路径超链接...
2025-08-04 09:28:44,118 - INFO - ✅ 文件路径超链接设置完成，共设置 11 个超链接
2025-08-04 09:28:44,118 - INFO - 🎨 正在设置行颜色标记...
2025-08-04 09:28:44,119 - INFO - 🟡 应用黄色标识: OCR识别失败
2025-08-04 09:28:44,119 - INFO - ✅ '处理结果明细'sheet格式设置完成
2025-08-04 09:28:44,120 - INFO - 🔴 正在设置结清验证状态列格式...
2025-08-04 09:28:44,120 - INFO - ✅ 结清验证状态列格式化完成：1 个单元格标红
2025-08-04 09:28:44,120 - INFO - ✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红
2025-08-04 09:28:44,121 - INFO - 📍 找到关键列 - 图片文件数量: 13, debtor_no: 9, 类型: 6
2025-08-04 09:28:44,121 - INFO - ✅ 还款对账明细格式化完成
2025-08-04 09:28:44,121 - INFO -    🔴 图片文件缺失标红: 0 行
2025-08-04 09:28:44,122 - INFO -    🟠 结清验证问题标橙: 0 行
2025-08-04 09:28:44,125 - INFO - 写入目录统计信息: 1 条记录
2025-08-04 09:28:44,127 - INFO - 写入处理摘要信息
2025-08-04 09:28:44,168 - INFO - 增强版Excel文件保存成功: 0801隆丰诉保_还款凭证解析_20250804_092843.xlsx
2025-08-04 09:28:44,168 - INFO - ✅ 成功生成Excel文件: 0801隆丰诉保_还款凭证解析_20250804_092843.xlsx
2025-08-04 09:28:44,168 - INFO - ✅ 子文件夹 0801隆丰诉保 处理完成，生成文件: 0801隆丰诉保_还款凭证解析_20250804_092843.xlsx
2025-08-04 09:28:44,168 - INFO - ================================================================================
2025-08-04 09:28:44,169 - INFO - 🎯 全部处理完成！
2025-08-04 09:28:44,169 - INFO - 📋 共处理 2 个子文件夹
2025-08-04 09:28:44,169 - INFO - 📄 成功生成 2 个Excel文件
2025-08-04 09:28:44,169 - INFO - 📄 生成的文件列表:
2025-08-04 09:28:44,169 - INFO -    - 0801隆丰电催_还款凭证解析_20250804_092431.xlsx
2025-08-04 09:28:44,169 - INFO -    - 0801隆丰诉保_还款凭证解析_20250804_092843.xlsx
2025-08-04 09:28:44,170 - INFO - 开始生成人名统计，共有 11 条处理结果
2025-08-04 09:28:44,171 - INFO - 处理完成，共处理 1 个目录，生成 11 个人员统计
2025-08-04 09:28:44,171 - INFO - 处理的目录列表: ['0801隆丰诉保']
2025-08-04 09:28:45,241 - INFO - 最终生成 11 个人员统计记录

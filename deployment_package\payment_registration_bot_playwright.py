#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
还款登记自动化脚本 - 原生Playwright版本
功能：读取Excel文件，自动登录系统并完成还款登记操作
作者：AI Assistant
日期：2025-01-20
"""

import json
import pandas as pd
import time
import logging
import os
import glob
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import asyncio
from playwright.async_api import async_playwright, Page, Browser, BrowserContext

# 创建必要的文件夹
os.makedirs("log", exist_ok=True)
os.makedirs("png", exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('log', 'payment_registration_playwright.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PaymentRegistrationBotPlaywright:
    def __init__(self, config_path: str = "config.json"):
        """
        初始化自动化机器人
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self.load_config(config_path)
        self.users_data = {}  # 存储用户数据
        self.current_user = None
        self.browser = None
        self.context = None
        self.page = None
        
        # 添加状态跟踪
        self.sheet1_df = None  # 存储Sheet1数据框引用
        self.sheet2_df = None  # 存储Sheet2数据框引用
        self.processed_debtors = {}  # 记录已处理的债务人状态 {debtor_name: success}
        
        # 初始化处理汇总统计
        self.processing_summary = {
            'success_count': 0,
            'failed_count': 0,
            'failed_debtors': [],
            'success_debtors': []
        }
        
    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {str(e)}")
            # 返回默认配置
            return {
                "users": {},
                "settings": {
                    "base_url": "https://ams.faamc.com",
                    "project_code": "dssz20240716001",
                    "default_password": "FA123567",
                    "timeout": 30000,
                    "retry_count": 3,
                    "delay_between_operations": 2000
                },
                "excel_config": {
                    "sheet1_name": "处理结果明细",
                    "sheet2_name": "还款对账明细",
                    "key_columns": {
                        "debtor_no": "debtor_no",
                        "debtor_name": "债务人姓名",
                        "collector_user": "collector_user_name",
                        "payment_time": "还款时间",
                        "receipt_count": "凭证数量",
                        "payer": "还款人",
                        "payment_amount": "图片OCR识别金额",
                        "payment_account": "还款账号",
                        "payment_source": "还款来源",
                        "is_deposit": "是否代存",
                        "deposit_image_path": "代存图片路径",
                        "file_path": "完整文件路径",
                        "settlement_status": "结清验证状态"
                    }
                }
            }

    def get_excel_files_from_folder(self, folder_path: str) -> List[str]:
        """
        获取文件夹中包含"还款凭证解析"字样的Excel文件
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            Excel文件路径列表
        """
        try:
            if not os.path.exists(folder_path):
                logger.error(f"文件夹不存在: {folder_path}")
                return []
            
            # 查找包含"还款凭证解析"字样的Excel文件
            pattern = os.path.join(folder_path, "*还款凭证解析*.xlsx")
            excel_files = glob.glob(pattern)
            
            # 过滤掉临时文件（以~$开头的文件）
            excel_files = [f for f in excel_files if not os.path.basename(f).startswith('~$')]
            
            logger.info(f"在文件夹 {folder_path} 中找到 {len(excel_files)} 个还款凭证解析文件")
            for file in excel_files:
                logger.info(f"  - {os.path.basename(file)}")
            
            return excel_files
            
        except Exception as e:
            logger.error(f"获取Excel文件失败: {str(e)}")
            return []

    def create_attention_file(self, attention_records: List[Dict], source_file: str) -> str:
        """
        创建需要关注的还款登记信息文件
        
        Args:
            attention_records: 需要关注的记录列表
            source_file: 源文件名（用于生成新文件名）
            
        Returns:
            创建的文件路径
        """
        try:
            if not attention_records:
                return ""
            
            # 生成文件名（使用完整时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"需做还款登记信息_{timestamp}.xlsx"
            
            # 转换为DataFrame
            df = pd.DataFrame(attention_records)
            
            # 保存到Excel文件
            df.to_excel(filename, index=False, sheet_name="需要关注的记录")
            
            logger.info(f"创建需要关注的文件: {filename}, 包含 {len(attention_records)} 条记录")
            return filename
            
        except Exception as e:
            logger.error(f"创建关注文件失败: {str(e)}")
            return ""

    def process_settlement_status(self, sheet1_df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        处理结清验证状态，分离需要关注的记录
        
        Args:
            sheet1_df: 处理结果明细DataFrame
            
        Returns:
            (过滤后的DataFrame, 需要关注的记录列表)
        """
        try:
            col_config = self.config['excel_config']['key_columns']
            
            # 安全获取结清验证状态列名
            settlement_status_col = col_config.get('settlement_status', '结清验证状态')
            
            attention_records = []
            
            # 检查是否存在结清验证状态列
            if settlement_status_col not in sheet1_df.columns:
                logger.warning(f"未找到'{settlement_status_col}'列，跳过结清验证状态处理")
                logger.info(f"当前可用列名: {list(sheet1_df.columns)}")
                return sheet1_df, attention_records
            
            # 获取结清验证状态列数据并进行类型检查
            status_series = sheet1_df[settlement_status_col]
            logger.info(f"结清验证状态列数据类型: {status_series.dtype}")
            logger.info(f"结清验证状态列前3个值: {list(status_series.head(3))}")
            
            # 安全地处理字符串操作 - 先转换为字符串再进行匹配
            try:
                # 将列转换为字符串类型，处理NaN值
                status_series_str = status_series.fillna('').astype(str)
                
                # 查找需要关注的记录
                attention_mask = status_series_str.str.contains(
                    "结清但还款金额小于剩余本金，需要关注", na=False
                )
                
                logger.info(f"找到包含关注关键词的记录数: {attention_mask.sum()}")
                
            except Exception as str_error:
                logger.warning(f"字符串处理失败: {str_error}，尝试直接匹配")
                # 备选方案：直接进行字符串比较
                attention_mask = status_series.apply(
                    lambda x: "结清但还款金额小于剩余本金，需要关注" in str(x) if pd.notna(x) else False
                )
            
            attention_rows = sheet1_df[attention_mask]
            if len(attention_rows) > 0:
                logger.info(f"发现 {len(attention_rows)} 条需要关注的记录")
                
                # 转换为字典列表
                attention_records = attention_rows.to_dict('records')
                
                # 记录详细信息
                for idx, row in attention_rows.iterrows():
                    debtor_name = row.get(col_config.get('debtor_name', '债务人姓名'), '未知')
                    debtor_no = row.get(col_config.get('debtor_no', 'debtor_no'), '未知')
                    status_value = row.get(settlement_status_col, '未知')
                    logger.info(f"关注记录: {debtor_name} ({debtor_no}) - {status_value}")
            
            # 过滤掉需要关注的记录
            filtered_df = sheet1_df[~attention_mask].copy()
            
            logger.info(f"过滤后剩余 {len(filtered_df)} 条记录用于处理")
            
            return filtered_df, attention_records
            
        except Exception as e:
            logger.error(f"处理结清验证状态失败: {str(e)}")
            logger.error(f"错误详情: {type(e).__name__}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return sheet1_df, []

    async def init_browser(self, headless: bool = False):
        """初始化浏览器"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=headless,
                slow_mo=300,  # 缩短每个操作间隔到300ms，提高速度
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            self.page = await self.context.new_page()
            
            # 设置默认超时时间
            self.page.set_default_timeout(30000)
            
            logger.info("浏览器初始化成功")
            
        except Exception as e:
            logger.error(f"浏览器初始化失败: {str(e)}")
            raise

    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")

    def read_excel_data(self, folder_path: str) -> Tuple[pd.DataFrame, pd.DataFrame, List[str]]:
        """
        读取文件夹中所有包含"还款凭证解析"的Excel文件的两个sheet
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            (合并后的sheet1_df, 合并后的sheet2_df, 创建的关注文件列表)
        """
        try:
            # 获取所有符合条件的Excel文件
            excel_files = self.get_excel_files_from_folder(folder_path)
            
            if not excel_files:
                raise ValueError(f"在文件夹 {folder_path} 中未找到包含'还款凭证解析'的Excel文件")
            
            sheet1_name = self.config['excel_config']['sheet1_name']
            sheet2_name = self.config['excel_config']['sheet2_name']
            
            all_sheet1_data = []
            all_sheet2_data = []
            all_attention_records = []
            attention_files = []
            
            for excel_file in excel_files:
                try:
                    logger.info(f"处理文件: {os.path.basename(excel_file)}")
                    
                    # 首先检查Excel文件中实际有哪些sheet
                    xl_file = pd.ExcelFile(excel_file)
                    available_sheets = xl_file.sheet_names
                    logger.info(f"  - 可用Sheet: {available_sheets}")
                    
                    # 自动检测正确的sheet名称
                    actual_sheet1_name = self.detect_sheet_name(available_sheets, sheet1_name, 
                                                               ['处理结果明细', '明细', '结果'])
                    actual_sheet2_name = self.detect_sheet_name(available_sheets, sheet2_name, 
                                                               ['还款对账明细', '按人名统计', '统计', '对账明细'])
                    
                    logger.info(f"  - 使用Sheet1: {actual_sheet1_name}")
                    logger.info(f"  - 使用Sheet2: {actual_sheet2_name}")
                    
                    # 读取两个sheet
                    sheet1_df = pd.read_excel(excel_file, sheet_name=actual_sheet1_name)
                    sheet2_df = pd.read_excel(excel_file, sheet_name=actual_sheet2_name)
                    
                    logger.info(f"  - {actual_sheet1_name}: {len(sheet1_df)}行")
                    logger.info(f"  - {actual_sheet2_name}: {len(sheet2_df)}行")
                    
                    # 处理结清验证状态，分离需要关注的记录
                    filtered_sheet1_df, attention_records = self.process_settlement_status(sheet1_df)
                    
                    # 收集需要关注的记录，但不立即创建文件
                    if attention_records:
                        # 为每条记录添加源文件信息
                        for record in attention_records:
                            record['源文件'] = os.path.basename(excel_file)
                        all_attention_records.extend(attention_records)
                        logger.info(f"  - 收集到 {len(attention_records)} 条需要关注的记录")
                    
                    # 添加文件来源标识
                    filtered_sheet1_df['源文件'] = os.path.basename(excel_file)
                    sheet2_df['源文件'] = os.path.basename(excel_file)
                    
                    all_sheet1_data.append(filtered_sheet1_df)
                    all_sheet2_data.append(sheet2_df)
                    
                except Exception as e:
                    logger.error(f"处理文件 {excel_file} 失败: {str(e)}")
                    continue
            
            if not all_sheet1_data or not all_sheet2_data:
                raise ValueError("没有成功读取到任何有效的Excel数据")
            
            # 合并所有数据
            combined_sheet1_df = pd.concat(all_sheet1_data, ignore_index=True)
            combined_sheet2_df = pd.concat(all_sheet2_data, ignore_index=True)
            
            # 在处理完所有文件后，统一创建关注文件
            if all_attention_records:
                attention_file = self.create_combined_attention_file(all_attention_records)
                if attention_file:
                    attention_files.append(attention_file)
                    logger.info(f"统一创建关注文件: {attention_file}, 包含来自 {len(excel_files)} 个文件的 {len(all_attention_records)} 条记录")
            
            logger.info(f"合并完成 - {sheet1_name}: {len(combined_sheet1_df)}行, {sheet2_name}: {len(combined_sheet2_df)}行")
            logger.info(f"总计需要关注的记录: {len(all_attention_records)}条")
            
            return combined_sheet1_df, combined_sheet2_df, attention_files
            
        except Exception as e:
            logger.error(f"读取Excel数据失败: {str(e)}")
            raise

    def process_records_without_debtor_no(self, sheet1_df: pd.DataFrame):
        """处理Sheet1中没有debtor_no的行"""
        try:
            col_config = self.config['excel_config']['key_columns']
            debtor_no_col = col_config.get('debtor_no', 'debtor_no')
            
            logger.info(f"检查债务人编号列: {debtor_no_col}")
            logger.info(f"Sheet1列名: {list(sheet1_df.columns)}")
            
            # 查找没有debtor_no的行
            if debtor_no_col in sheet1_df.columns:
                # 详细检查debtor_no列的数据
                debtor_no_values = sheet1_df[debtor_no_col]
                logger.info(f"债务人编号列数据类型: {debtor_no_values.dtype}")
                logger.info(f"债务人编号列前5个值: {list(debtor_no_values.head())}")
                logger.info(f"债务人编号列是否有空值: {debtor_no_values.isna().sum()}")
                logger.info(f"债务人编号列空字符串数量: {(debtor_no_values == '').sum()}")
                logger.info(f"债务人编号列唯一值数量: {debtor_no_values.nunique()}")
                
                mask = debtor_no_values.isna() | \
                       (debtor_no_values == '') | \
                       (debtor_no_values == 'nan') | \
                       (debtor_no_values.astype(str) == 'nan')
                
                rows_without_debtor = sheet1_df.loc[mask]
                rows_with_debtor = sheet1_df.loc[~mask]
                
                logger.info(f"有债务人编号的记录: {len(rows_with_debtor)} 行")
                logger.info(f"没有债务人编号的记录: {len(rows_without_debtor)} 行")
                
                if len(rows_without_debtor) > 0:
                    logger.info(f"发现 {len(rows_without_debtor)} 行没有债务人编号的记录")
                    # 标记这些行的状态
                    sheet1_df.loc[mask, '处理状态'] = '有图片文件无债务人登记信息'
                    
                    # 记录详细信息
                    for idx, row in rows_without_debtor.iterrows():
                        payer = row.get(col_config.get('payer', '还款人'), '未知')
                        file_path = row.get(col_config.get('file_path', '完整文件路径'), '无文件')
                        debtor_no_value = row.get(debtor_no_col, '无')
                        logger.info(f"跳过记录 - 还款人: {payer}, debtor_no: {debtor_no_value}, 文件: {file_path}")
                
                if len(rows_with_debtor) > 0:
                    logger.info(f"有效债务人编号样例:")
                    for i, (idx, row) in enumerate(rows_with_debtor.iterrows()):
                        if i >= 3:  # 只显示前3个样例
                            break
                        payer = row.get(col_config.get('payer', '还款人'), '未知')
                        debtor_no_value = row.get(debtor_no_col, '无')
                        logger.info(f"  - 还款人: {payer}, debtor_no: {debtor_no_value}")
            else:
                logger.error(f"Sheet1中未找到债务人编号列: {debtor_no_col}")
            
        except Exception as e:
            logger.error(f"处理没有债务人编号的记录时出错: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")

    def update_processed_debtors_status(self):
        """更新已处理债务人的状态"""
        try:
            col_config = self.config['excel_config']['key_columns']
            
            # 重置统计信息（为当前处理清零）
            self.processing_summary = {
                'success_count': 0,
                'failed_count': 0,
                'failed_debtors': [],
                'success_debtors': []
            }
            
            # 更新Sheet2中处理的债务人状态
            for debtor_name, status in self.processed_debtors.items():
                # 在Sheet2中找到对应的债务人
                mask = self.sheet2_df[col_config['debtor_name']] == debtor_name
                if mask.any():
                    if status is True:  # 成功处理
                        self.sheet2_df.loc[mask, '处理状态'] = '还款登记成功'
                        # 成功的只记录DEBUG日志，不在终端显示
                        logger.debug(f"更新债务人 {debtor_name} 状态为：还款登记成功")
                        
                        # 同时更新Sheet1中对应的还款记录
                        sheet1_mask = self.sheet1_df[col_config['payer']] == debtor_name
                        if sheet1_mask.any():
                            self.sheet1_df.loc[sheet1_mask, '处理状态'] = '还款登记成功'
                            logger.debug(f"更新还款人 {debtor_name} 的还款记录状态为：还款登记成功")
                        
                        # 记录成功信息
                        self.processing_summary['success_count'] += 1
                        self.processing_summary['success_debtors'].append(debtor_name)
                        
                    else:  # 其他状态（失败、已有登记信息等）
                        status_text = str(status) if status else "处理失败"
                        self.sheet2_df.loc[mask, '处理状态'] = status_text
                        # 失败的继续显示在终端
                        logger.warning(f"更新债务人 {debtor_name} 状态为：{status_text}")
                        
                        # 同时更新Sheet1中对应的还款记录
                        sheet1_mask = self.sheet1_df[col_config['payer']] == debtor_name
                        if sheet1_mask.any():
                            self.sheet1_df.loc[sheet1_mask, '处理状态'] = status_text
                            logger.warning(f"更新还款人 {debtor_name} 的还款记录状态为：{status_text}")
                        
                        # 记录失败信息
                        self.processing_summary['failed_count'] += 1
                        self.processing_summary['failed_debtors'].append({
                            'name': debtor_name,
                            'status': status_text
                        })
                        
        except Exception as e:
            logger.error(f"更新已处理债务人状态时出错: {str(e)}")

    def update_excel_status(self, excel_path: str, sheet1_df: pd.DataFrame, sheet2_df: pd.DataFrame):
        """更新Excel文件的状态信息"""
        try:
            import openpyxl
            from openpyxl import load_workbook
            
            # 读取现有的Excel文件
            wb = load_workbook(excel_path)
            
            # 更新Sheet1
            if 'Sheet1' in wb.sheetnames:
                ws1 = wb['Sheet1']
                
                # 查找状态列的位置或添加新列
                status_col_index = len(sheet1_df.columns)
                
                # 添加状态列标题（如果不存在）
                if ws1.cell(row=1, column=status_col_index).value != '处理状态':
                    ws1.cell(row=1, column=status_col_index, value='处理状态')
                
                # 更新状态数据
                for idx, status in enumerate(sheet1_df['处理状态'], start=2):
                    if status:  # 只更新有状态的行
                        ws1.cell(row=idx, column=status_col_index, value=status)
                        logger.debug(f"Sheet1第{idx}行状态更新为: {status}")
            
            # 更新Sheet2
            if 'Sheet2' in wb.sheetnames:
                ws2 = wb['Sheet2']
                
                # 查找状态列的位置或添加新列
                status_col_index = len(sheet2_df.columns)
                
                # 添加状态列标题（如果不存在）
                if ws2.cell(row=1, column=status_col_index).value != '处理状态':
                    ws2.cell(row=1, column=status_col_index, value='处理状态')
                
                # 更新状态数据
                for idx, status in enumerate(sheet2_df['处理状态'], start=2):
                    if status:  # 只更新有状态的行
                        ws2.cell(row=idx, column=status_col_index, value=status)
                        logger.debug(f"Sheet2第{idx}行状态更新为: {status}")
            
            # 保存文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = excel_path.replace('.xlsx', f'_状态更新_{timestamp}.xlsx')
            wb.save(output_path)
            logger.info(f"Excel状态已更新并保存到: {output_path}")
            
            # 生成状态统计报告
            self.generate_status_report(sheet1_df, sheet2_df)
            
        except Exception as e:
            logger.error(f"更新Excel状态失败: {str(e)}")

    def generate_status_report(self, sheet1_df: pd.DataFrame, sheet2_df: pd.DataFrame):
        """生成处理状态报告"""
        try:
            # 统计Sheet1状态
            sheet1_status_count = sheet1_df['处理状态'].value_counts()
            logger.info("=== Sheet1 处理状态统计 ===")
            for status, count in sheet1_status_count.items():
                if status:  # 非空状态
                    logger.info(f"{status}: {count} 条记录")
            
            # 统计Sheet2状态
            sheet2_status_count = sheet2_df['处理状态'].value_counts()
            logger.info("=== Sheet2 处理状态统计 ===")
            for status, count in sheet2_status_count.items():
                if status:  # 非空状态
                    logger.info(f"{status}: {count} 条记录")
                    
            # 详细统计processed_debtors中的状态
            status_breakdown = {}
            for debtor_name, status in self.processed_debtors.items():
                if status is True:
                    status_key = "成功处理"
                elif isinstance(status, str):
                    status_key = status
                else:
                    status_key = "未知状态"
                
                status_breakdown[status_key] = status_breakdown.get(status_key, 0) + 1
            
            logger.info(f"=== 详细处理状态统计 ===")
            for status, count in status_breakdown.items():
                logger.info(f"{status}: {count} 个债务人")
                    
            # 总体统计
            total_processed = len([name for name, status in self.processed_debtors.items() if status is True])
            total_debtors = len(self.processed_debtors)
            already_registered = len([name for name, status in self.processed_debtors.items() if isinstance(status, str) and "已有登记信息" in status])
            
            logger.info(f"=== 总体处理统计 ===")
            logger.info(f"成功处理债务人: {total_processed}")
            logger.info(f"已有登记信息债务人: {already_registered}")
            logger.info(f"总债务人数: {total_debtors}")
            logger.info(f"成功率: {(total_processed/total_debtors*100):.1f}%" if total_debtors > 0 else "成功率: 0%")
            
        except Exception as e:
            logger.error(f"生成状态报告失败: {str(e)}")

    def process_user_data(self, sheet1_df: pd.DataFrame, sheet2_df: pd.DataFrame) -> Dict:
        """
        处理用户数据并按collecter_user_name排序
        
        Args:
            sheet1_df: 处理结果明细
            sheet2_df: 还款对账明细
            
        Returns:
            按用户分组的数据字典
        """
        try:
            col_config = self.config['excel_config']['key_columns']
            
            # 调试信息：打印列名和数据类型
            logger.info(f"Sheet1 列名: {list(sheet1_df.columns)}")
            logger.info(f"Sheet2 列名: {list(sheet2_df.columns)}")
            logger.info(f"Sheet1 形状: {sheet1_df.shape}")
            logger.info(f"Sheet2 形状: {sheet2_df.shape}")
            
            # 为Sheet1和Sheet2添加状态列（如果不存在）
            if '处理状态' not in sheet1_df.columns:
                sheet1_df['处理状态'] = ''
            if '处理状态' not in sheet2_df.columns:
                sheet2_df['处理状态'] = ''
            
            # 处理Sheet1中没有debtor_no的行
            self.process_records_without_debtor_no(sheet1_df)
            
            # 对debtor_no进行去重处理（从Sheet1中）
            debtor_no_col = col_config.get('debtor_no', 'debtor_no')
            logger.info(f"开始去重处理，使用列名: {debtor_no_col}")
            
            # 详细检查过滤条件
            if debtor_no_col in sheet1_df.columns:
                debtor_no_series = sheet1_df[debtor_no_col]
                logger.info(f"去重前总行数: {len(sheet1_df)}")
                logger.info(f"debtor_no列中非空值数量: {debtor_no_series.notna().sum()}")
                logger.info(f"debtor_no列中非空字符串数量: {(debtor_no_series != '').sum()}")
                logger.info(f"debtor_no列中非'nan'字符串数量: {(debtor_no_series != 'nan').sum()}")
                
                # 逐步应用过滤条件
                mask1 = debtor_no_series.notna()
                mask2 = (debtor_no_series != '')
                mask3 = (debtor_no_series != 'nan')
                mask4 = (debtor_no_series.astype(str) != 'nan')  # 添加字符串转换检查
                
                logger.info(f"notna()过滤后: {mask1.sum()} 行")
                logger.info(f"非空字符串过滤后: {(mask1 & mask2).sum()} 行")
                logger.info(f"非'nan'过滤后: {(mask1 & mask2 & mask3).sum()} 行")
                logger.info(f"非字符串'nan'过滤后: {(mask1 & mask2 & mask3 & mask4).sum()} 行")
                
                final_mask = mask1 & mask2 & mask3 & mask4
                sheet1_filtered = sheet1_df[final_mask]
            else:
                logger.error(f"Sheet1中未找到列: {debtor_no_col}")
                sheet1_filtered = pd.DataFrame()
            
            # 获取唯一的debtor_no列表
            if len(sheet1_filtered) > 0:
                unique_debtor_nos = sheet1_filtered[debtor_no_col].unique()
                logger.info(f"去重后的唯一债务人编号数量: {len(unique_debtor_nos)}")
                if len(unique_debtor_nos) > 0:
                    logger.info(f"前3个债务人编号样例: {list(unique_debtor_nos[:3])}")
            else:
                unique_debtor_nos = []
                logger.warning("过滤后没有任何有效的债务人记录")
            
            # 记录跳过的行
            skipped_rows = len(sheet1_df) - len(sheet1_filtered)
            if skipped_rows > 0:
                logger.info(f"跳过了 {skipped_rows} 行没有债务人编号的记录")
                # 为跳过的行标记状态
                mask = sheet1_df[debtor_no_col].isna() | \
                       (sheet1_df[debtor_no_col] == '') | \
                       (sheet1_df[debtor_no_col] == 'nan') | \
                       (sheet1_df[debtor_no_col].astype(str) == 'nan')
                sheet1_df.loc[mask, '处理状态'] = '有图片文件无债务人登记信息'
            
            users_data = {}
            
            # 为每个唯一的debtor_no处理数据
            for debtor_no in unique_debtor_nos:
                try:
                    # 在还款对账明细中查找对应的collector_user_name
                    sheet2_match = sheet2_df[sheet2_df[col_config['debtor_no']] == debtor_no]
                    
                    if len(sheet2_match) == 0:
                        logger.warning(f"债务人编号 {debtor_no} 在还款对账明细中未找到对应记录")
                        
                        # 添加详细的调试信息
                        if debtor_no in unique_debtor_nos[:3]:  # 只为前3个显示详细信息，避免日志过长
                            logger.info(f"调试信息 - Sheet2中的所有债务人编号:")
                            sheet2_debtor_nos = sheet2_df[col_config['debtor_no']].unique()
                            logger.info(f"  Sheet2债务人编号数量: {len(sheet2_debtor_nos)}")
                            logger.info(f"  Sheet2前5个债务人编号: {list(sheet2_debtor_nos[:5])}")
                            logger.info(f"  Sheet1当前查找的编号: {debtor_no}")
                            logger.info(f"  是否包含当前编号: {debtor_no in sheet2_debtor_nos}")
                            
                            # 检查是否有相似的编号
                            similar_debtor_nos = [
                                no for no in sheet2_debtor_nos 
                                if isinstance(no, str) and isinstance(debtor_no, str) and 
                                (debtor_no in no or no in debtor_no)
                            ]
                            if similar_debtor_nos:
                                logger.info(f"  发现相似编号: {similar_debtor_nos}")
                        
                        continue
                    
                    # 获取第一个匹配记录的催收员信息
                    collector_user = sheet2_match.iloc[0][col_config['collector_user']]
                    debtor_name = sheet2_match.iloc[0][col_config['debtor_name']]
                    payment_time = sheet2_match.iloc[0][col_config['payment_time']]
                    
                    logger.info(f"处理债务人: {debtor_name} ({debtor_no}), 催收员: {collector_user}")
                    
                    if collector_user not in users_data:
                        users_data[collector_user] = []
                    
                    # 获取该债务人的基本信息（从Sheet2）
                    debtor_info = {
                        'debtor_no': debtor_no,
                        'debtor_name': debtor_name,
                        'payment_time': payment_time,
                        'receipt_count': sheet2_match.iloc[0].get(col_config['receipt_count'], 1)
                    }
                    
                    # 从Sheet1中获取该debtor_no的所有还款记录
                    debtor_records = sheet1_filtered[sheet1_filtered[col_config['debtor_no']] == debtor_no]
                    logger.info(f"债务人 {debtor_name} ({debtor_no}) 在处理结果明细中找到 {len(debtor_records)} 条记录")
                    
                    payment_records = []
                    for _, record in debtor_records.iterrows():
                        # 从Sheet1获取具体还款记录详情
                        payment_record = {
                            'payer': record[col_config['payer']],
                            'payment_amount': record[col_config['payment_amount']],
                            'payment_account': record[col_config['payment_account']],
                            'payment_source': record[col_config['payment_source']],
                            'is_deposit': record[col_config['is_deposit']],
                            'deposit_image_path': record.get(col_config['deposit_image_path'], ''),
                            'file_path': record[col_config['file_path']]
                        }
                        payment_records.append(payment_record)
                        logger.info(f"添加还款记录: {record[col_config['payer']]} - {record[col_config['payment_amount']]} - {record[col_config['file_path']]}")
                    
                    debtor_info['payment_records'] = payment_records
                    logger.info(f"债务人 {debtor_name} ({debtor_no}) 最终包含 {len(payment_records)} 条还款记录")
                    users_data[collector_user].append(debtor_info)
                    
                except Exception as e:
                    logger.error(f"处理债务人编号 {debtor_no} 时出错: {str(e)}")
                    continue
            
            # 按催收员用户名排序
            sorted_users_data = {}
            for user in sorted(users_data.keys()):
                sorted_users_data[user] = users_data[user]
            
            logger.info(f"用户数据处理完成，共{len(sorted_users_data)}个用户")
            for user, debtors in sorted_users_data.items():
                logger.info(f"  - {user}: {len(debtors)}个债务人")
            
            return sorted_users_data
            
        except Exception as e:
            logger.error(f"用户数据处理失败: {str(e)}")
            raise

    async def login_user(self, username: str) -> bool:
        """
        登录指定用户
        
        Args:
            username: 用户名
            
        Returns:
            登录是否成功
        """
        try:
            password = self.config['users'].get(username, self.config['settings']['default_password'])
            base_url = self.config['settings']['base_url']
            
            logger.info(f"开始登录用户: {username}")
            
            # 导航到登录页面
            await self.page.goto(base_url)
            logger.info(f"导航到登录页面: {base_url}")

            # 等待登录表单加载
            await self.page.wait_for_selector("#username", timeout=10000)
            logger.info("登录页面加载完成")

            # 输入用户名
            await self.page.fill("#username", username)
            logger.info(f"输入用户名: {username}")

            # 输入密码
            await self.page.fill("#password", password)
            logger.info("输入密码")

            # 点击登录按钮
            await self.page.click(".loginBtn")
            logger.info("点击登录按钮")

            # 等待登录成功（检查页面变化）
            try:
                # 等待页面跳转，检查多个可能的成功指标
                success_indicators = [
                    "text=熵管",  # 主页的熵管按钮
                    "text=熵收",  # 主页的熵收按钮
                    "text=进入平台",  # 进入平台按钮
                    "text=退出系统",  # 退出系统文本
                    "text=资产管理平台",  # 平台描述
                    "text=智能处置平台"  # 平台描述
                ]
                
                success = False
                for indicator in success_indicators:
                    try:
                        await self.page.wait_for_selector(indicator, timeout=3000)
                        success = True
                        logger.info(f"登录成功指标: {indicator}")
                        break
                    except:
                        continue
                
                if success:
                    self.current_user = username
                    logger.info(f"用户 {username} 登录成功")
                    
                    # 登录成功后截图
                    await self.take_screenshot(f"login_success_{username}_{int(time.time())}.png")
                    return True
                else:
                    # 检查是否有错误提示
                    current_url = self.page.url
                    logger.error(f"登录失败，当前URL: {current_url}")
                    await self.take_screenshot(f"login_failed_{username}_{int(time.time())}.png")
                    return False
                    
            except Exception as e:
                logger.error(f"登录检查异常: {str(e)}")
                await self.take_screenshot(f"login_error_{username}_{int(time.time())}.png")
                return False
            
        except Exception as e:
            logger.error(f"用户 {username} 登录失败: {str(e)}")
            return False

    async def enter_collection_platform(self) -> bool:
        """
        进入催收平台
        
        Returns:
            是否成功进入
        """
        try:
            logger.info("尝试进入催收平台")
            
            # 检查是否已经在催收平台内或者主页
            current_url = self.page.url
            if "/robin/collect" in current_url or "/home" in current_url:
                logger.info("已在催收平台内或主页")
                return True
            
            # 查找并点击熵收模块的进入平台按钮
            platform_selectors = [
                "text=进入平台",
                "button:has-text('进入平台')",
                "a:has-text('进入平台')"
            ]
            
            # 首先确保我们在主页
            if not ("ams.faamc.com" in current_url and current_url.count('/') <= 3):
                await self.page.goto(self.config['settings']['base_url'])
                await asyncio.sleep(2)
            
            platform_entered = False
            for selector in platform_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    # 如果有多个"进入平台"按钮，选择第二个（熵收模块的）
                    if len(elements) >= 2:
                        await elements[1].click()  # 第二个按钮应该是熵收的
                        logger.info("点击熵收平台的进入平台按钮")
                        platform_entered = True
                        break
                    elif len(elements) == 1:
                        await elements[0].click()
                        logger.info("点击进入平台按钮")
                        platform_entered = True
                        break
                except Exception as e:
                    logger.warning(f"尝试选择器 {selector} 失败: {str(e)}")
                    continue
            
            if not platform_entered:
                logger.error("未找到进入平台按钮")
                return False
            
            # 缩短等待时间，只等待3秒后就可以继续
            await asyncio.sleep(3)
            
            # 不再等待networkidle，直接检查URL变化
            try:
                # 等待URL变化，最多等待5秒
                await self.page.wait_for_url("**/home", timeout=5000)
                logger.info("成功进入催收平台主页")
            except:
                # 如果URL没有变化到/home，检查当前URL
                current_url = self.page.url
                logger.info(f"当前URL: {current_url}")
                # 只要不是登录页面就认为成功
                if "login" not in current_url and current_url != self.config['settings']['base_url']:
                    logger.info("已进入平台（基于URL判断）")
                else:
                    logger.warning("进入平台可能失败，但继续尝试")
            
            final_url = self.page.url
            logger.info(f"进入平台后URL: {final_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"进入催收平台失败: {str(e)}")
            return False

    async def navigate_to_debtor_page(self, debtor_no: str, debtor_name: str) -> bool:
        """
        导航到债务人详情页面
        
        Args:
            debtor_no: 债务人编号
            debtor_name: 债务人姓名
            
        Returns:
            导航是否成功
        """
        try:
            # 检查当前URL，如果已经在催收平台相关页面就直接导航
            current_url = self.page.url
            if "/robin/collect" not in current_url and "/home" not in current_url:
                # 先确保已进入催收平台
                if not await self.enter_collection_platform():
                    logger.error("无法进入催收平台")
                    return False
            else:
                logger.info("已在催收平台，直接导航到债务人页面")
            
            base_url = self.config['settings']['base_url']
            project_code = self.config['settings']['project_code']
            timestamp = int(time.time() * 1000)
            
            url = f"{base_url}/robin/collect/case/detail?debtorNo={debtor_no}&projectCode={project_code}&debtorName={debtor_name}&ts={timestamp}"
            
            logger.info(f"导航到债务人页面: {debtor_name} ({debtor_no})")
            
            # 尝试最多2次（初始加载 + 1次刷新）
            max_attempts = 2
            
            for attempt in range(max_attempts):
                try:
                    if attempt == 0:
                        logger.info(f"第{attempt + 1}次尝试：直接导航到债务人页面")
                        await self.page.goto(url)
                    else:
                        logger.info(f"第{attempt + 1}次尝试：刷新页面重新加载")
                        await self.page.reload()
                    
                    # 等待15秒来确保页面完全加载
                    logger.info("等待15秒确保债务人页面完全加载...")
                    
                    # 等待页面内容加载（最多15秒）
                    page_loaded = False
                    start_time = time.time()
                    
                    # 定义债务人页面的关键内容指标
                    debtor_content_indicators = [
                        debtor_name,                      # 债务人姓名
                        "身份证号",                       # 身份证号标签
                        "到期时间",                       # 到期时间标签
                        "基本信息",                       # 基本信息标签
                        "案件信息",                       # 案件信息标签
                        "还款登记"                        # 还款登记按钮
                    ]
                    
                    page_text = ""  # 初始化页面文本
                    found_indicators = []  # 初始化找到的指标列表
                    
                    while time.time() - start_time < 15:  # 增加到15秒超时
                        current_url = self.page.url
                        
                        # 检查页面是否有实际内容（更重要的指标）
                        page_text = await self.page.inner_text('body') if await self.page.query_selector('body') else ""
                        
                        # 检查页面是否包含债务人相关内容
                        content_found = 0
                        found_indicators = []
                        for indicator in debtor_content_indicators:
                            if indicator in page_text:
                                content_found += 1
                                found_indicators.append(indicator)
                        
                        logger.info(f"页面内容检查: 找到 {content_found}/{len(debtor_content_indicators)} 个关键内容指标")
                        logger.info(f"找到的指标: {found_indicators}")
                        
                        # 如果找到足够的关键内容，认为页面加载完成
                        if content_found >= 3:  # 至少找到3个关键内容
                            logger.info("页面内容丰富，认为加载完成")
                            page_loaded = True
                            break
                        
                        # 检查是否还在加载状态（作为次要判断）
                        loading_indicators = [
                            ".ant-spin-spinning",           # Ant Design加载指示器
                            ".loading",                     # 通用加载类
                            "[class*='loading']",           # 包含loading的类
                            ".ant-spin-dot"                 # Ant Design旋转点
                        ]
                        
                        is_loading = False
                        active_loading_count = 0
                        for loading_selector in loading_indicators:
                            try:
                                loading_element = await self.page.query_selector(loading_selector)
                                if loading_element and await loading_element.is_visible():
                                    active_loading_count += 1
                                    is_loading = True
                            except:
                                continue
                        
                        # 如果有加载指示器但内容已经足够，只记录但不阻止继续
                        if is_loading and content_found < 3:
                            logger.info(f"检测到 {active_loading_count} 个加载指示器，但内容不足，继续等待...")
                            await asyncio.sleep(1)
                            continue
                        elif is_loading and content_found >= 3:
                            logger.info(f"检测到 {active_loading_count} 个加载指示器，但页面内容已充足，忽略加载状态")
                            page_loaded = True
                            break
                        
                        # 打印调试信息
                        logger.info(f"当前页面状态 - URL: {current_url}")
                        logger.info(f"页面内容长度: {len(page_text)} 字符")
                        
                        # 如果页面内容太少，可能还在加载
                        if len(page_text) < 100:
                            logger.info("页面内容太少，可能还在加载")
                            await asyncio.sleep(1)
                            continue
                        
                        # 如果内容不足但也没有加载指示器，可能是出错了
                        if content_found < 2 and not is_loading:
                            logger.warning(f"页面内容不足且无加载指示器，可能出现问题")
                            await asyncio.sleep(1)
                            continue
                        
                        # 每1秒检查一次（降低频率）
                        await asyncio.sleep(1)
                    
                    if page_loaded:
                        # 页面加载成功，截图记录
                        await self.take_screenshot(f"debtor_page_loaded_{debtor_name}_{int(time.time())}.png")
                        logger.info(f"债务人页面成功加载完成 (第{attempt + 1}次尝试)")
                        logger.info(f"页面包含关键内容: {found_indicators}")
                        return True
                    else:
                        # 页面未完全加载
                        current_url = self.page.url
                        logger.warning(f"第{attempt + 1}次尝试：15秒后页面仍未完全加载")
                        logger.warning(f"当前URL: {current_url}")
                        
                        # 获取当前页面的详细信息用于调试
                        try:
                            page_title = await self.page.title()
                            page_text = await self.page.inner_text('body') if await self.page.query_selector('body') else "无法获取页面内容"
                            logger.warning(f"页面标题: {page_title}")
                            logger.warning(f"页面内容前200字符: {page_text[:200]}...")
                            
                            # 检查是否有错误信息
                            error_selectors = [
                                "text=404",
                                "text=错误",
                                "text=Error",
                                "text=访问被拒绝",
                                "text=无权限",
                                "[class*='error']",
                                "[class*='404']"
                            ]
                            
                            error_found = False
                            for error_selector in error_selectors:
                                try:
                                    error_element = await self.page.query_selector(error_selector)
                                    if error_element and await error_element.is_visible():
                                        error_text = await error_element.inner_text()
                                        logger.error(f"发现页面错误信息: {error_text}")
                                        error_found = True
                                        break
                                except:
                                    continue
                                    
                            if error_found:
                                logger.error("页面包含错误信息，停止重试")
                                break
                            
                        except Exception as debug_error:
                            logger.warning(f"获取页面调试信息失败: {str(debug_error)}")
                        
                        # 截图用于调试
                        await self.take_screenshot(f"debtor_page_loading_failed_attempt{attempt + 1}_{debtor_name}_{int(time.time())}.png")
                        
                        # 检查页面内容，看是否有错误信息
                        try:
                            page_content = await self.page.content()
                            if "404" in page_content or "error" in page_content.lower():
                                logger.error(f"债务人页面返回错误内容")
                                break  # 如果是错误页面，不再重试
                        except:
                            pass
                        
                        if attempt < max_attempts - 1:
                            logger.info("准备刷新页面重试...")
                            await asyncio.sleep(3)  # 增加刷新前等待时间到3秒
                        
                except Exception as nav_error:
                    logger.error(f"第{attempt + 1}次导航尝试失败: {str(nav_error)}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(2)
                    continue
            
            # 所有尝试都失败
            logger.error(f"经过{max_attempts}次尝试，债务人页面仍无法正确加载，跳过债务人: {debtor_name}")
            await self.take_screenshot(f"debtor_page_failed_final_{debtor_name}_{int(time.time())}.png")
            
            # 保存页面HTML内容用于调试
            try:
                timestamp = int(time.time())
                html_content = await self.page.content()
                html_filename = f"debug_html/debtor_page_failed_{debtor_name}_{timestamp}.html"
                
                # 确保目录存在
                import os
                os.makedirs("debug_html", exist_ok=True)
                
                with open(html_filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                logger.info(f"已保存页面HTML内容到: {html_filename}")
                
                # 同时记录一些关键信息
                debug_info = f"""
=== 债务人页面调试信息 ===
债务人: {debtor_name}
债务人编号: {debtor_no}
时间: {timestamp}
URL: {self.page.url}
页面标题: {await self.page.title()}
页面内容长度: {len(html_content)} 字符

页面文本内容前500字符:
{(await self.page.inner_text('body'))[:500] if await self.page.query_selector('body') else '无法获取页面文本'}
===========================
"""
                debug_filename = f"debug_html/debtor_page_debug_{debtor_name}_{timestamp}.txt"
                with open(debug_filename, 'w', encoding='utf-8') as f:
                    f.write(debug_info)
                logger.info(f"已保存调试信息到: {debug_filename}")
                
            except Exception as save_error:
                logger.warning(f"保存调试信息失败: {str(save_error)}")
            
            return False
            
        except Exception as e:
            logger.error(f"导航到债务人页面失败: {str(e)}")
            return False

    async def click_payment_registration(self) -> str:
        """
        点击还款登记按钮
        
        Returns:
            "success": 点击成功，可以继续
            "failed": 点击失败
            "already_registered": 客户已有登记信息，需要跳过
        """
        try:
            logger.info("寻找还款登记按钮")
            
            # 首先打印当前页面的详细信息用于调试
            try:
                current_url = self.page.url
                page_title = await self.page.title()
                logger.info(f"当前页面URL: {current_url}")
                logger.info(f"当前页面标题: {page_title}")
                
                # 获取页面中所有可能是按钮的元素
                all_buttons = await self.page.query_selector_all("button, a, [role='button'], .btn")
                logger.info(f"页面中找到 {len(all_buttons)} 个按钮/链接元素")
                
                button_texts = []
                for i, btn in enumerate(all_buttons[:10]):  # 只检查前10个
                    try:
                        text = await btn.inner_text()
                        if text.strip():
                            button_texts.append(text.strip())
                    except:
                        continue
                
                logger.info(f"前10个按钮的文本: {button_texts}")
                
                # 检查页面中是否包含"还款登记"文本
                page_text = await self.page.inner_text('body') if await self.page.query_selector('body') else ""
                if "还款登记" in page_text:
                    logger.info("页面中包含'还款登记'文本")
                else:
                    logger.warning("页面中不包含'还款登记'文本")
                    
            except Exception as debug_error:
                logger.warning(f"获取页面调试信息失败: {str(debug_error)}")
            
            # 尝试多种可能的选择器
            selectors = [
                "button:has-text('还款登记')",
                "a:has-text('还款登记')",
                ".btn:has-text('还款登记')",
                "[title*='还款登记']",
                "[data-title*='还款登记']",
                "text=还款登记",                    # 纯文本选择器
                "*:has-text('还款登记')",           # 任何包含文本的元素
                "button[title='还款登记']",         # 带title属性的按钮
                "a[title='还款登记']",             # 带title属性的链接
                ".ant-btn:has-text('还款登记')",    # Ant Design按钮
                "[class*='btn']:has-text('还款登记')", # 包含btn类的元素
                "span:has-text('还款登记')"         # span元素
            ]
            
            button_found = False
            found_element = None
            used_selector = ""
            
            for selector in selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.info(f"找到还款登记按钮: {selector}")
                        found_element = element
                        used_selector = selector
                        button_found = True
                        break
                    elif element:
                        logger.info(f"找到元素但不可见: {selector}")
                except Exception as e:
                    logger.debug(f"选择器 {selector} 查找失败: {str(e)}")
                    continue
            
            if button_found and found_element:
                try:
                    # 尝试点击元素
                    await found_element.click()
                    logger.info(f"成功点击还款登记按钮: {used_selector}")
                except Exception as click_error:
                    logger.error(f"点击还款登记按钮失败: {str(click_error)}")
                    return "failed"
            else:
                logger.error("未找到还款登记按钮")
                
                # 输出更多调试信息
                try:
                    page_content = await self.page.content()
                    logger.debug(f"页面HTML长度: {len(page_content)} 字符")
                    # 查找页面中所有包含"登记"的文本
                    if "登记" in page_content:
                        logger.info("页面中包含'登记'文本")
                    else:
                        logger.warning("页面中不包含'登记'文本")
                except:
                    pass
                    
                return "failed"
            
            # 等待响应，可能是正常弹窗或提示弹窗
            await asyncio.sleep(2)
            
            # 检查是否出现"该客户已有登记信息处于未认领状态"的提示
            try:
                # 检查提示弹窗的内容
                modal_content_selectors = [
                    ".ant-modal-confirm-content",
                    ".ant-modal-body",
                    ".modal-body"
                ]
                
                for content_selector in modal_content_selectors:
                    content_element = await self.page.query_selector(content_selector)
                    if content_element:
                        content_text = await content_element.inner_text()
                        logger.debug(f"检测到弹窗内容: {content_text}")
                        
                        if "该客户已有登记信息处于未认领状态" in content_text or "已有登记信息" in content_text:
                            logger.info("检测到客户已有登记信息提示，准备关闭弹窗")
                            
                            # 寻找并点击取消按钮
                            cancel_selectors = [
                                "button:has-text('取消')",
                                ".ant-btn:has-text('取消')",
                                ".ant-modal-confirm .ant-btn:first-child",
                                "button.ant-btn-default"
                            ]
                            
                            for cancel_selector in cancel_selectors:
                                try:
                                    cancel_btn = await self.page.query_selector(cancel_selector)
                                    if cancel_btn:
                                        await cancel_btn.click()
                                        logger.info("已点击取消按钮关闭提示弹窗")
                                        await asyncio.sleep(1)
                                        return "already_registered"
                                except:
                                    continue
                            
                            # 如果找不到取消按钮，尝试按ESC键
                            try:
                                await self.page.keyboard.press("Escape")
                                logger.info("已按ESC键关闭提示弹窗")
                                await asyncio.sleep(1)
                                return "already_registered"
                            except:
                                pass
                            
                            return "already_registered"
                
                # 如果没有检测到特殊提示，检查是否正常打开了还款登记弹窗
                normal_modal_selectors = [
                    ".modal-dialog", 
                    ".ant-modal", 
                    ".el-dialog"
                ]
                
                for modal_selector in normal_modal_selectors:
                    modal = await self.page.query_selector(modal_selector)
                    if modal and await modal.is_visible():
                        logger.info("还款登记弹窗已正常打开")
                        return "success"
                
                # 如果没有检测到任何弹窗，等待更长时间
                await self.page.wait_for_selector(".modal-dialog, .ant-modal, .el-dialog", timeout=8000)
                logger.info("还款登记弹窗已打开")
                return "success"
                
            except Exception as modal_error:
                logger.warning(f"检测弹窗时出现异常: {str(modal_error)}")
                # 尝试等待正常弹窗
                try:
                    await self.page.wait_for_selector(".modal-dialog, .ant-modal, .el-dialog", timeout=5000)
                    logger.info("还款登记弹窗已打开（备用检测）")
                    return "success"
                except:
                    logger.error("未能检测到还款登记弹窗")
                    return "failed"
            
        except Exception as e:
            logger.error(f"点击还款登记按钮失败: {str(e)}")
            return "failed"

    async def fill_payment_form(self, debtor_info: Dict) -> bool:
        """
        填写还款表单
        
        Args:
            debtor_info: 债务人信息
            
        Returns:
            填写是否成功
        """
        try:
            logger.info(f"开始填写还款表单: {debtor_info['debtor_name']}")
            
            payment_records = debtor_info['payment_records']
            receipt_count = debtor_info.get('receipt_count', 1)
            
            logger.info(f"总凭证数量: {receipt_count}, 实际记录数: {len(payment_records)}")
            
            if not payment_records:
                logger.warning("没有找到还款记录，无法填写表单")
                return False
            
            filled_records = 0
            
            # 填写第一条记录（默认表单已有一行）
            if await self.fill_single_payment_record(payment_records[0], payment_time, 1):
                filled_records += 1
                logger.info("第一条记录填写成功")
            else:
                logger.error("第一条记录填写失败")
                return False
            
            # 如果有多条记录，需要添加更多行
            if len(payment_records) > 1:
                logger.info(f"需要添加 {len(payment_records) - 1} 条额外记录")
                
                # 点击新增按钮添加额外的行
                for i in range(len(payment_records) - 1):
                    logger.info(f"添加第 {i + 2} 条还款记录行")
                    
                    # 点击新增还款信息按钮
                    if not await self.click_add_payment_button():
                        logger.error(f"添加第 {i + 2} 条记录失败：无法点击新增按钮")
                        break
                    
                    await asyncio.sleep(2)  # 等待新行添加
                    
                    # 填写新添加的记录
                    if await self.fill_single_payment_record(payment_records[i + 1], payment_time, i + 2):
                        filled_records += 1
                        logger.info(f"第 {i + 2} 条记录填写成功")
                    else:
                        logger.warning(f"第 {i + 2} 条记录填写失败")
            
            logger.info(f"成功填写 {filled_records}/{len(payment_records)} 条记录")
            
            # 验证表单内容
            await self.validate_form_content(debtor_info)
            
            # 提交表单前截图
            await self.take_screenshot(f"before_submit_{debtor_info['debtor_name']}_{int(time.time())}.png")
            
            # 提交表单
            if await self.submit_payment_form():
                logger.info("还款表单提交成功")
                return True
            else:
                logger.error("还款表单提交失败")
                return False
            
        except Exception as e:
            logger.error(f"填写还款表单失败: {str(e)}")
            return False

    async def validate_form_content(self, debtor_info: Dict) -> bool:
        """验证表单内容是否正确填写"""
        try:
            logger.info("验证表单内容...")
            
            # 检查必填字段是否已填写（使用实际的选择器）
            required_fields = [
                ("input[_nk='cFGw61']", "还款人"),
                (".ant-picker input[placeholder='请选择日期']", "还款时间"),
                ("input[_nk='cFGw81']", "还款金额"),
                ("input[_nk='cFGw62']", "还款账号")
            ]
            
            missing_fields = []
            filled_fields = []
            
            for selector, field_name in required_fields:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        # 检查输入值
                        value = await element.input_value()
                        if value and value.strip() != "":
                            filled_fields.append(f"{field_name}={value}")
                            logger.debug(f"字段已填写: {field_name} = {value}")
                        else:
                            missing_fields.append(field_name)
                            logger.debug(f"字段未填写: {field_name}")
                    else:
                        missing_fields.append(f"{field_name}(未找到字段)")
                        logger.debug(f"字段未找到: {field_name}")
                except Exception as e:
                    missing_fields.append(f"{field_name}(检查异常)")
                    logger.debug(f"字段检查异常: {field_name} - {str(e)}")
            
            # 检查下拉选择字段
            dropdown_fields = [
                (".ant-select[_nk='cFGw91'] .ant-select-selection-item", "还款来源"),
                (".ant-select[_nk='cFGw92'] .ant-select-selection-item", "合作方代存")
            ]
            
            for selector, field_name in dropdown_fields:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        value = await element.inner_text()
                        if value and value.strip() != "":
                            filled_fields.append(f"{field_name}={value}")
                            logger.debug(f"下拉字段已选择: {field_name} = {value}")
                        else:
                            missing_fields.append(field_name)
                    else:
                        missing_fields.append(f"{field_name}(未找到字段)")
                except Exception as e:
                    missing_fields.append(f"{field_name}(检查异常)")
                    logger.debug(f"下拉字段检查异常: {field_name} - {str(e)}")
            
            logger.info(f"已填写字段: {', '.join(filled_fields) if filled_fields else '无'}")
            
            if missing_fields:
                logger.warning(f"以下字段可能未正确填写: {', '.join(missing_fields)}")
                return False
            else:
                logger.info("表单验证通过")
                return True
                
        except Exception as e:
            logger.error(f"表单验证失败: {str(e)}")
            return False

    async def submit_payment_form(self) -> bool:
        """提交还款表单"""
        try:
            logger.info("准备提交还款表单")
            
            # 等待所有上传完成（缩短等待时间）
            await asyncio.sleep(1)
            
            # 提交按钮选择器（根据实际页面结构）
            submit_selectors = [
                "button:has-text('保存')",               # 保存按钮（最常见）
                ".ant-btn-primary:has-text('保存')",     # Ant Design主要按钮
                ".ant-btn:has-text('保存')",             # Ant Design按钮
                "button:has-text('确定')",               # 确定按钮
                "button:has-text('提交')",               # 提交按钮
                ".ant-modal-footer .ant-btn-primary",   # 模态框底部主要按钮
                ".ant-modal-footer button.ant-btn-primary", # 模态框底部主要按钮（更精确）
                ".ant-modal-footer button:last-child",  # 模态框底部最后一个按钮
                "button.ant-btn.ant-btn-primary",       # Ant Design主要按钮类组合
                "[class*='ant-btn'][class*='primary']", # 主要按钮样式
                ".submit-btn",
                ".save-btn",
                "[type='submit']"
            ]
            
            submit_clicked = False
            for selector in submit_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        # 检查按钮是否可用
                        is_enabled = await element.is_enabled()
                        is_visible = await element.is_visible()
                        
                        if is_enabled and is_visible:
                            await element.click()
                            logger.info(f"点击提交按钮: {selector}")
                            submit_clicked = True
                            break
                        else:
                            logger.debug(f"提交按钮不可用: {selector} (enabled={is_enabled}, visible={is_visible})")
                except Exception as e:
                    logger.debug(f"提交按钮点击失败: {selector} - {str(e)}")
                    continue
            
            if not submit_clicked:
                logger.error("未找到可用的提交按钮")
                return False
            
            # 等待提交响应
            await asyncio.sleep(3)
            
            # 检查是否有成功提示或错误提示
            success_indicators = [
                "text=成功",
                "text=保存成功",
                "text=提交成功",
                ".success",
                ".ant-message-success"
            ]
            
            error_indicators = [
                "text=失败",
                "text=错误",
                ".error",
                ".ant-message-error"
            ]
            
            # 检查成功提示
            for indicator in success_indicators:
                try:
                    element = await self.page.query_selector(indicator)
                    if element:
                        text = await element.inner_text()
                        logger.info(f"发现成功提示: {text}")
                        return True
                except:
                    continue
            
            # 检查错误提示
            for indicator in error_indicators:
                try:
                    element = await self.page.query_selector(indicator)
                    if element:
                        text = await element.inner_text()
                        logger.error(f"发现错误提示: {text}")
                        return False
                except:
                    continue
            
            # 如果没有明确的成功/失败提示，检查弹窗是否关闭
            try:
                modal = await self.page.query_selector(".modal-dialog, .ant-modal, .el-dialog")
                if not modal:
                    logger.info("弹窗已关闭，假定提交成功")
                    return True
                else:
                    logger.warning("弹窗仍然存在，可能提交失败")
                    return False
            except:
                logger.info("无法检查弹窗状态，假定提交成功")
                return True
            
        except Exception as e:
            logger.error(f"提交表单失败: {str(e)}")
            return False

    async def click_add_payment_button(self) -> bool:
        """
        点击新增还款信息按钮
        
        Returns:
            点击是否成功
        """
        try:
            logger.info("寻找新增还款信息按钮")
            
            # 根据用户提供的HTML结构，使用精确的选择器
            add_button_selectors = [
                ".addBtn___1t6l5",  # 精确的类名
                "div[_nk='cFGw1e']",  # 使用_nk属性
                "button:has-text('新增还款信息')",  # 按钮文本
                "div:has-text('新增还款信息')",  # div文本
                ".anticon-plus-circle",  # 加号图标
                "[aria-label='plus-circle']"  # 通过aria-label
            ]
            
            button_clicked = False
            for selector in add_button_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        # 检查元素是否可见和可用
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        
                        if is_visible and is_enabled:
                            await element.click()
                            logger.info(f"成功点击新增还款信息按钮: {selector}")
                            button_clicked = True
                            break
                        else:
                            logger.debug(f"新增按钮不可用: {selector} (visible={is_visible}, enabled={is_enabled})")
                except Exception as e:
                    logger.debug(f"新增按钮选择器失败: {selector} - {str(e)}")
                    continue
            
            if not button_clicked:
                logger.error("未找到可用的新增还款信息按钮")
                # 截图用于调试
                await self.take_screenshot(f"add_button_not_found_{int(time.time())}.png")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"点击新增还款信息按钮失败: {str(e)}")
            return False

    def format_payment_time(self, payment_time) -> str:
        """
        格式化还款时间为表单需要的格式
        
        Args:
            payment_time: 从Excel读取的还款时间（可能是各种格式）
            
        Returns:
            格式化后的日期字符串 (YYYY-MM-DD)
        """
        try:
            import pandas as pd
            from datetime import datetime
            
            # 如果已经是字符串
            if isinstance(payment_time, str):
                # 移除可能的时间部分
                if " 00:00:00" in payment_time:
                    payment_time = payment_time.split(" ")[0]
                
                # 尝试解析不同的日期格式
                date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d', '%m/%d/%Y', '%d/%m/%Y']
                
                for fmt in date_formats:
                    try:
                        dt = datetime.strptime(payment_time, fmt)
                        return dt.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
                
                # 如果都无法解析，返回原字符串
                return payment_time
            
            # 如果是pandas的Timestamp
            elif isinstance(payment_time, pd.Timestamp):
                return payment_time.strftime('%Y-%m-%d')
            
            # 如果是datetime对象
            elif hasattr(payment_time, 'strftime'):
                return payment_time.strftime('%Y-%m-%d')
            
            # 如果是数字（Excel日期序列号）
            elif isinstance(payment_time, (int, float)):
                # Excel的日期序列号转换
                try:
                    # Excel日期从1900年1月1日开始计算
                    from datetime import date, timedelta
                    excel_epoch = date(1900, 1, 1)
                    dt = excel_epoch + timedelta(days=payment_time - 2)  # Excel的bug，需要减2
                    return dt.strftime('%Y-%m-%d')
                except:
                    return str(payment_time)
            
            # 其他情况，转换为字符串
            else:
                return str(payment_time)
                
        except Exception as e:
            logger.error(f"日期格式化失败: {payment_time} -> {str(e)}")
            return str(payment_time)

    def normalize_date_format(self, date_value: str) -> str:
        """
        标准化日期格式，确保日期字段能正确填写
        
        Args:
            date_value: 日期字符串
            
        Returns:
            标准化后的日期字符串
        """
        try:
            # 移除时间部分
            if isinstance(date_value, str) and " 00:00:00" in date_value:
                date_value = date_value.split(" ")[0]
            
            # 如果已经是YYYY-MM-DD格式，直接返回
            if isinstance(date_value, str) and len(date_value) == 10 and date_value.count('-') == 2:
                parts = date_value.split('-')
                if len(parts) == 3 and len(parts[0]) == 4:
                    return date_value
            
            # 如果是YYYYMMDD格式，转换为YYYY-MM-DD
            if isinstance(date_value, str) and len(date_value) == 8 and date_value.isdigit():
                return f"{date_value[:4]}-{date_value[4:6]}-{date_value[6:8]}"
            
            return str(date_value)
            
        except Exception as e:
            logger.warning(f"日期格式标准化失败: {date_value} -> {str(e)}")
            return str(date_value)

    def format_payment_account(self, payment_account) -> str:
        """
        格式化还款账号，确保至少4位数，不足的前面补0
        
        Args:
            payment_account: 从Excel读取的还款账号（可能是数字或字符串）
            
        Returns:
            格式化后的还款账号字符串（至少4位数）
        """
        try:
            # 先转换为字符串，去除可能的小数点
            if isinstance(payment_account, (int, float)):
                # 如果是数字，转换为整数字符串
                account_str = str(int(payment_account))
            else:
                # 如果是字符串，去除前后空格
                account_str = str(payment_account).strip()
                
                # 如果字符串中包含小数点，取整数部分
                if '.' in account_str:
                    try:
                        account_str = str(int(float(account_str)))
                    except ValueError:
                        # 如果转换失败，保持原字符串但去除小数点
                        account_str = account_str.replace('.', '')
            
            # 去除非数字字符（保留数字）
            account_str = ''.join(filter(str.isdigit, account_str))
            
            # 如果为空或全为0，设置为"0000"
            if not account_str or account_str == '' or all(c == '0' for c in account_str):
                formatted_account = "0000"
            else:
                # 使用zfill确保至少4位数，不足的前面补0
                formatted_account = account_str.zfill(4)
            
            logger.info(f"还款账号格式化: {payment_account} -> {formatted_account}")
            return formatted_account
            
        except Exception as e:
            logger.error(f"还款账号格式化失败: {payment_account} -> {str(e)}")
            # 出错时返回默认值
            return "0000"

    async def fill_single_payment_record(self, record: Dict, payment_time: str, row_index: int) -> bool:
        """
        填写单条还款记录
        
        Args:
            record: 还款记录
            payment_time: 还款时间
            row_index: 行索引
            
        Returns:
            填写是否成功
        """
        try:
            logger.info(f"填写第{row_index}行还款记录")
            
            # 格式化还款时间
            formatted_time = self.format_payment_time(payment_time)
            logger.info(f"原始还款时间: {payment_time} (类型: {type(payment_time)})")
            logger.info(f"格式化后还款时间: {formatted_time}")
            
            # 等待表单稳定（缩短等待时间）
            await asyncio.sleep(1)
            
            # 根据HTML结构，使用正确的选择器
            # 调整填写顺序：先填写其他字段，最后填写日期以避免日期选择器干扰
            
            # 1. 填写还款人
            await self.fill_input_field(
                field_name="还款人",
                value=str(record['payer']),
                selectors=[
                    "input[_nk='cFGw61']",  # 根据HTML结构的实际属性
                    "input.ant-input.item___31wVl[placeholder='请输入']",
                    "input[placeholder='请输入'][type='text']",
                    ".ant-input[placeholder='请输入']",  # 更通用的选择器
                    "input.ant-input"  # 最通用的选择器
                ],
                row_index=row_index
            )
            await asyncio.sleep(0.5)  # 缩短等待时间
            
            # 2. 填写还款金额（数字输入框）
            await self.fill_input_field(
                field_name="还款金额",
                value=str(record['payment_amount']),
                selectors=[
                    "input[_nk='cFGw81']",  # 根据HTML结构的实际属性
                    "input.ant-input-number-input",
                    "input[role='spinbutton']",
                    ".ant-input-number-input"  # 更通用的选择器
                ],
                row_index=row_index
            )
            await asyncio.sleep(0.5)  # 缩短等待时间
            
            # 3. 填写还款账号
            # 使用格式化方法处理还款账号：确保至少4位数，不足的前面补0
            payment_account = record['payment_account']
            payment_account_value = self.format_payment_account(payment_account)
            
            await self.fill_input_field(
                field_name="还款账号",
                value=payment_account_value,
                selectors=[
                    "input[_nk='cFGw62']",  # 根据HTML结构的实际属性
                    "input.ant-input.item___31wVl[placeholder='请输入']",
                    "input[placeholder='请输入'][type='text']",
                    ".ant-input[placeholder='请输入']",  # 更通用的选择器
                    "input.ant-input"  # 最通用的选择器
                ],
                row_index=row_index
            )
            await asyncio.sleep(0.5)  # 缩短等待时间
            
            # 4. 选择还款来源
            await self.select_payment_source(str(record['payment_source']), row_index)
            await asyncio.sleep(0.5)  # 缩短等待时间
            
            # 5. 处理合作方代存
            is_deposit = record['is_deposit'] == '是'
            # 只有当需要代存时才操作下拉框，否则使用默认值"否"
            if is_deposit:
                await self.select_cooperation_deposit(is_deposit, row_index)
                await asyncio.sleep(0.5)  # 缩短等待时间
                
                if record['deposit_image_path']:
                    # 上传代存图片（收据文件）
                    logger.info("上传代存收据文件")
                    await self.upload_file(record['deposit_image_path'], "代存收据", row_index)
                    await asyncio.sleep(2)
            else:
                logger.info(f"第{row_index}行：是否代存为否，跳过合作方代存操作（使用默认值）")
            
            # 6. 最后填写还款时间（日期选择器），避免干扰其他字段
            await self.fill_date_field(
                field_name="还款时间",
                value=formatted_time,
                row_index=row_index
            )
            await asyncio.sleep(1)
            
            # 7. 最后上传还款凭证（使用完整文件路径）
            if record['file_path']:
                await self.upload_file(record['file_path'], "还款凭证", row_index)
                await asyncio.sleep(2)  # 缩短文件上传等待时间
            
            logger.info(f"第{row_index}行还款记录填写完成")
            return True
            
        except Exception as e:
            logger.error(f"填写第{row_index}行记录失败: {str(e)}")
            return False

    async def fill_input_field(self, field_name: str, value: str, selectors: List[str], row_index: int = 1) -> bool:
        """填写输入字段的通用方法
        
        Args:
            field_name: 字段名称
            value: 要填写的值
            selectors: 选择器列表
            row_index: 行索引（从1开始）
        """
        try:
            for selector in selectors:
                try:
                    logger.debug(f"尝试选择器: {selector} (行 {row_index})")
                    
                    # 获取所有匹配的元素
                    elements = await self.page.query_selector_all(selector)
                    logger.debug(f"找到 {len(elements)} 个匹配的元素")
                    
                    # 根据行索引选择元素（索引从0开始，但row_index从1开始）
                    element_index = row_index - 1
                    if element_index < len(elements):
                        element = elements[element_index]
                        
                        # 检查元素是否可见和可用
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        
                        logger.debug(f"选择元素 {element_index}: {selector}, visible={is_visible}, enabled={is_enabled}")
                        
                        if is_visible and is_enabled:
                            # 获取元素的readonly属性
                            readonly_attr = await element.get_attribute("readonly")
                            
                            # 对于不同类型的输入框使用不同的方法
                            if "ant-picker" in selector or readonly_attr == "readonly":
                                # 日期选择器需要特殊处理
                                await element.click()
                                await asyncio.sleep(0.5)
                                # 等待日期选择器打开，然后直接输入日期
                                await self.page.keyboard.type(value)
                                await self.page.keyboard.press("Escape")  # 关闭日期选择器
                            else:
                                # 普通输入框
                                await element.click()
                                await asyncio.sleep(0.3)
                                # 先清空内容
                                await element.fill("")
                                await asyncio.sleep(0.3)
                                # 再输入新内容
                                await element.fill(value)
                            
                            logger.info(f"填写{field_name}(行{row_index}): {value} (使用选择器: {selector})")
                            return True
                        else:
                            logger.debug(f"元素不可用: {selector}")
                    else:
                        logger.debug(f"行索引 {row_index} 超出范围，只有 {len(elements)} 个元素")
                    
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {str(e)}")
                    continue
                    
            # 如果所有选择器都失败，尝试截图进行调试
            await self.take_screenshot(f"field_not_found_{field_name}_row{row_index}_{int(time.time())}.png")
            logger.warning(f"未找到{field_name}输入字段(行{row_index})")
            return False
        except Exception as e:
            logger.error(f"填写{field_name}失败(行{row_index}): {str(e)}")
            return False

    async def select_dropdown_option(self, field_name: str, value: str, selectors: List[str]) -> bool:
        """选择下拉选项的通用方法"""
        try:
            # 先尝试点击选择器打开下拉菜单
            dropdown_opened = False
            for selector in selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        await element.click()
                        dropdown_opened = True
                        logger.info(f"打开{field_name}下拉菜单")
                        await asyncio.sleep(1)
                        break
                except:
                    continue
            
            if dropdown_opened:
                # 查找选项并点击
                option_selectors = [
                    f"text={value}",
                    f".ant-select-item-option:has-text('{value}')",
                    f"[title='{value}']",
                    f"div:has-text('{value}')"
                ]
                
                for opt_selector in option_selectors:
                    try:
                        option = await self.page.query_selector(opt_selector)
                        if option:
                            await option.click()
                            logger.info(f"选择{field_name}: {value}")
                            return True
                    except:
                        continue
                
                # 如果没找到选项，尝试输入搜索
                try:
                    search_input = await self.page.query_selector(".ant-select-selection-search-input")
                    if search_input:
                        await search_input.fill(value)
                        await asyncio.sleep(1)
                        await self.page.keyboard.press("Enter")
                        logger.info(f"通过搜索选择{field_name}: {value}")
                        return True
                except:
                    pass
            
            logger.warning(f"未能选择{field_name}: {value}")
            return False
        except Exception as e:
            logger.error(f"选择{field_name}失败: {str(e)}")
            return False

    async def select_cooperation_deposit(self, is_deposit: bool, row_index: int) -> bool:
        """选择合作方代存选项"""
        try:
            value = "是" if is_deposit else "否"
            
            # 查找合作方代存选择器的多种可能性
            selectors = [
                ".ant-select[_nk='cFGw92'] .ant-select-selector",
                ".ant-select[_nk='cFGw92']",
                ".ant-select .ant-select-selector"  # 更通用的选择器
            ]
            
            clicked = False
            for selector in selectors:
                try:
                    # 获取所有匹配的元素
                    elements = await self.page.query_selector_all(selector)
                    logger.debug(f"找到 {len(elements)} 个合作方代存下拉框")
                    
                    # 根据行索引选择元素
                    element_index = row_index - 1
                    if element_index < len(elements):
                        element = elements[element_index]
                        
                        if await element.is_visible() and await element.is_enabled():
                            await element.click()
                            logger.info(f"打开合作方代存下拉菜单(行{row_index}): {selector}")
                            clicked = True
                            break
                        else:
                            logger.debug(f"下拉框不可用(行{row_index}): {selector}")
                    else:
                        logger.debug(f"行索引 {row_index} 超出范围，只有 {len(elements)} 个下拉框")
                        
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {str(e)}")
                    continue
            
            if not clicked:
                logger.warning(f"未找到合作方代存选择器(行{row_index})")
                return False
                
            await asyncio.sleep(2)  # 增加等待时间
            
            # 尝试多次查找和点击选项，而不是依赖单次等待
            success = False
            max_attempts = 3
            
            for attempt in range(max_attempts):
                try:
                    logger.debug(f"尝试选择合作方代存选项(行{row_index}, 第{attempt+1}次)")
                    
                    # 查找并点击对应的选项
                    option_selector = f".ant-select-item-option[title='{value}']"
                    option = await self.page.query_selector(option_selector)
                    if option and await option.is_visible():
                        await option.click()
                        logger.info(f"选择合作方代存(行{row_index}): {value}")
                        await asyncio.sleep(1)
                        success = True
                        break
                    
                    # 如果没找到精确匹配，尝试文本匹配
                    options = await self.page.query_selector_all(".ant-select-item-option")
                    logger.debug(f"找到 {len(options)} 个下拉选项")
                    
                    for i, opt in enumerate(options):
                        try:
                            if await opt.is_visible():
                                text = await opt.inner_text()
                                logger.debug(f"选项 {i}: '{text}'")
                                if text == value:
                                    await opt.click()
                                    logger.info(f"选择合作方代存(行{row_index}, 通过文本匹配): {value}")
                                    await asyncio.sleep(1)
                                    success = True
                                    break
                        except Exception as e:
                            logger.debug(f"检查选项 {i} 失败: {str(e)}")
                            continue
                    
                    if success:
                        break
                        
                    # 如果这次尝试失败，等待后重试
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.debug(f"第{attempt+1}次尝试失败: {str(e)}")
                    await asyncio.sleep(1)
                    continue
            
            if not success:
                logger.warning(f"未能选择合作方代存(行{row_index}): {value}")
                await self.take_screenshot(f"cooperation_deposit_failed_row{row_index}_{int(time.time())}.png")
            
            return success
            
        except Exception as e:
            logger.error(f"选择合作方代存失败: {str(e)}")
            await self.take_screenshot(f"cooperation_deposit_error_{int(time.time())}.png")
            return False

    async def fill_date_field(self, field_name: str, value: str, row_index: int = 1) -> bool:
        """填写日期字段"""
        try:
            # 确保日期格式正确
            formatted_value = self.normalize_date_format(value)
            
            logger.info(f"尝试填写{field_name}(行{row_index}): {formatted_value}")
            
            # 查找日期选择器
            date_selectors = [
                ".ant-picker input[placeholder='请选择日期']",
                ".ant-picker-input input",
                "input[placeholder='请选择日期']"
            ]
            
            for selector in date_selectors:
                try:
                    # 获取所有匹配的日期选择器
                    elements = await self.page.query_selector_all(selector)
                    logger.debug(f"找到 {len(elements)} 个日期选择器")
                    
                    # 根据行索引选择元素
                    element_index = row_index - 1
                    if element_index < len(elements):
                        element = elements[element_index]
                        
                        if await element.is_visible():
                            logger.debug(f"找到日期选择器(行{row_index}): {selector}")
                            
                            # 点击日期输入框打开日期选择器
                            await element.click()
                            logger.debug("点击日期输入框")
                            await asyncio.sleep(1)
                        
                        # 方法1：直接输入日期文本
                        try:
                            # 清空现有内容
                            await element.fill("")
                            await asyncio.sleep(0.5)
                            
                            # 输入日期
                            await element.fill(formatted_value)
                            await asyncio.sleep(1)
                            
                            # 按Enter确认日期
                            await self.page.keyboard.press("Enter")
                            logger.debug("按Enter确认日期")
                            await asyncio.sleep(1)
                            
                            # 验证日期是否已填入
                            current_value = await element.input_value()
                            if current_value and current_value.strip() != "":
                                logger.info(f"日期填写成功(方法1): {field_name}(行{row_index}) = {current_value}")
                                return True
                            
                        except Exception as e:
                            logger.debug(f"方法1失败: {str(e)}")
                        
                        # 方法2：如果方法1失败，尝试键盘输入
                        try:
                            # 重新点击并清空
                            await element.click()
                            await asyncio.sleep(0.5)
                            
                            # 全选并删除现有内容
                            await self.page.keyboard.press("Control+a")
                            await self.page.keyboard.press("Delete")
                            await asyncio.sleep(0.5)
                            
                            # 逐字符输入日期
                            await self.page.keyboard.type(formatted_value)
                            await asyncio.sleep(1)
                            
                            # 按Tab或Enter确认
                            await self.page.keyboard.press("Tab")
                            await asyncio.sleep(1)
                            
                            # 验证日期是否已填入
                            current_value = await element.input_value()
                            if current_value and current_value.strip() != "":
                                logger.info(f"日期填写成功(方法2): {field_name}(行{row_index}) = {current_value}")
                                return True
                                
                        except Exception as e:
                            logger.debug(f"方法2失败: {str(e)}")
                        
                        # 方法3：使用JavaScript直接设置值
                        try:
                            # 使用JavaScript设置input的值
                            await self.page.evaluate(f"""
                                const input = document.querySelector('{selector}');
                                if (input) {{
                                    input.value = '{formatted_value}';
                                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                }}
                            """)
                            await asyncio.sleep(1)
                            
                            # 验证日期是否已填入
                            current_value = await element.input_value()
                            if current_value and current_value.strip() != "":
                                logger.info(f"日期填写成功(方法3): {field_name}(行{row_index}) = {current_value}")
                                # 点击空白区域关闭日期选择器
                                await self.page.keyboard.press("Escape")
                                await asyncio.sleep(1)
                                return True
                                
                        except Exception as e:
                            logger.debug(f"方法3失败: {str(e)}")
                        
                        # 如果都失败了，最后尝试点击空白区域关闭选择器
                        try:
                            await self.page.keyboard.press("Escape")
                            logger.debug("按Escape关闭日期选择器")
                            await asyncio.sleep(1)
                        except:
                            pass
                    else:
                        logger.debug(f"行索引 {row_index} 超出范围，只有 {len(elements)} 个日期字段")
                        
                except Exception as e:
                    logger.debug(f"日期选择器 {selector} 失败: {str(e)}")
                    continue
            
            logger.warning(f"未能成功填写{field_name}(行{row_index})日期字段")
            await self.take_screenshot(f"date_fill_failed_{field_name}_row{row_index}_{int(time.time())}.png")
            return False
            
        except Exception as e:
            logger.error(f"填写{field_name}(行{row_index})失败: {str(e)}")
            return False

    async def select_payment_source(self, value: str, row_index: int) -> bool:
        """选择还款来源"""
        try:
            # 根据实际HTML结构，还款来源有：执行还款、本人还款、他人代还、自然回款、银行批转
            value_mapping = {
                "执行还款": "执行还款",
                "本人还款": "本人还款", 
                "他人代还": "他人代还",
                "自然回款": "自然回款",
                "银行批转": "银行批转",
                "银行转账": "本人还款",  # 兼容性映射
                "现金": "本人还款",      # 兼容性映射
                "支付宝": "本人还款",    # 兼容性映射
                "微信": "本人还款"       # 兼容性映射
            }
            
            target_value = value_mapping.get(value, "本人还款")  # 默认选择本人还款
            
            # 查找还款来源选择器
            selectors = [
                ".ant-select[_nk='cFGw91'] .ant-select-selector",
                ".ant-select[_nk='cFGw91']",
                ".ant-select .ant-select-selector"  # 更通用的选择器
            ]
            
            clicked = False
            for selector in selectors:
                try:
                    # 获取所有匹配的元素
                    elements = await self.page.query_selector_all(selector)
                    logger.debug(f"找到 {len(elements)} 个还款来源下拉框")
                    
                    # 根据行索引选择元素
                    element_index = row_index - 1
                    if element_index < len(elements):
                        element = elements[element_index]
                        
                        if await element.is_visible() and await element.is_enabled():
                            await element.click()
                            logger.info(f"打开还款来源下拉菜单(行{row_index})")
                            clicked = True
                            break
                        else:
                            logger.debug(f"还款来源下拉框不可用(行{row_index}): {selector}")
                    else:
                        logger.debug(f"行索引 {row_index} 超出范围，只有 {len(elements)} 个还款来源下拉框")
                        
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {str(e)}")
                    continue
            
            if not clicked:
                logger.warning(f"未找到还款来源选择器(行{row_index})")
                return False
                
            await asyncio.sleep(2)  # 给下拉菜单更多时间加载
            
            # 尝试多次查找和点击选项，而不是依赖单次等待
            success = False
            max_attempts = 3
            
            for attempt in range(max_attempts):
                try:
                    logger.debug(f"尝试选择还款来源选项(行{row_index}, 第{attempt+1}次)")
                    
                    # 查找并点击对应的选项
                    option_selector = f".ant-select-item-option[title='{target_value}']"
                    option = await self.page.query_selector(option_selector)
                    if option and await option.is_visible():
                        await option.click()
                        logger.info(f"选择还款来源(行{row_index}): {target_value}")
                        await asyncio.sleep(1)
                        success = True
                        break
                    
                    # 如果没找到精确匹配，尝试文本匹配
                    options = await self.page.query_selector_all(".ant-select-item-option")
                    logger.debug(f"找到 {len(options)} 个下拉选项")
                    
                    for i, opt in enumerate(options):
                        try:
                            if await opt.is_visible():
                                text = await opt.inner_text()
                                logger.debug(f"选项 {i}: '{text}'")
                                if text == target_value:
                                    await opt.click()
                                    logger.info(f"选择还款来源(行{row_index}, 通过文本匹配): {target_value}")
                                    await asyncio.sleep(1)
                                    success = True
                                    break
                        except Exception as e:
                            logger.debug(f"检查选项 {i} 失败: {str(e)}")
                            continue
                    
                    if success:
                        break
                        
                    # 如果这次尝试失败，等待后重试
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.debug(f"第{attempt+1}次尝试失败: {str(e)}")
                    await asyncio.sleep(1)
                    continue
            
            if not success:
                logger.warning(f"未能选择还款来源(行{row_index}): {value} -> {target_value}")
                await self.take_screenshot(f"payment_source_failed_row{row_index}_{int(time.time())}.png")
            
            return success
            
        except Exception as e:
            logger.error(f"选择还款来源失败: {str(e)}")
            return False

    async def upload_file(self, file_path: str, description: str, row_index: int = 1) -> bool:
        """上传文件"""
        try:
            if not Path(file_path).exists():
                logger.warning(f"文件不存在: {file_path}")
                return False
            
            logger.info(f"开始上传文件: {description}(行{row_index})")
            
            # 等待上传区域稳定
            await asyncio.sleep(1)
            
            success = False
            is_deposit_file = "代存" in description or "收据" in description
            
            # 获取所有文件上传元素
            file_elements = await self.page.query_selector_all("input[type='file']")
            logger.info(f"页面中找到 {len(file_elements)} 个文件上传元素")
            
            # 调试信息：显示每个file input元素的位置和可见性，并尝试识别类型
            receipt_indices = []  # 收据文件上传索引
            voucher_indices = []  # 还款凭证上传索引
            
            for i, element in enumerate(file_elements):
                try:
                    is_visible = await element.is_visible()
                    is_enabled = await element.is_enabled()
                    
                    # 尝试通过多种方式获取上传区域的上下文信息
                    contexts = []
                    try:
                        # 获取父元素及其祖先元素的文本
                        parent_text = await element.evaluate("""
                            el => {
                                let text = '';
                                let current = el;
                                for (let i = 0; i < 5 && current; i++) {
                                    current = current.parentElement;
                                    if (current && current.textContent) {
                                        text += current.textContent + ' ';
                                    }
                                }
                                return text;
                            }
                        """)
                        contexts.append(parent_text)
                    except:
                        pass
                    
                    context_text = ' '.join(contexts).lower()
                    
                    # 根据上下文文本判断上传区域类型
                    if any(keyword in context_text for keyword in ['收据', '代存', 'receipt']):
                        receipt_indices.append(i)
                        area_type = "收据文件区域"
                    elif any(keyword in context_text for keyword in ['还款凭证', '凭证', 'voucher']):
                        voucher_indices.append(i)
                        area_type = "还款凭证区域"
                    else:
                        area_type = "未知区域"
                    
                    logger.debug(f"文件上传元素 {i}: visible={is_visible}, enabled={is_enabled}, type={area_type}, context='{context_text[:100]}...'")
                    
                except Exception as e:
                    logger.debug(f"文件上传元素 {i}: 无法获取状态信息 - {str(e)}")
            
            logger.info(f"检测到的上传区域分布 - 收据文件: {receipt_indices}, 还款凭证: {voucher_indices}")
            
            
            # 根据文件类型和智能检测结果确定目标索引
            if is_deposit_file:
                # 代存收据文件：优先使用检测到的收据文件索引
                if receipt_indices:
                    # 根据行数选择对应的收据文件索引
                    target_index = receipt_indices[min(row_index - 1, len(receipt_indices) - 1)]
                    logger.info(f"代存收据上传到第{row_index}行，使用智能检测的索引: {target_index} (收据文件区域)")
                else:
                    # 回退到奇数索引策略
                    target_index = (row_index - 1) * 2 + 1  # 收据文件在奇数索引
                    logger.info(f"代存收据上传到第{row_index}行，使用奇数索引策略: {target_index} (收据文件区域)")
            else:
                # 还款凭证文件：优先使用检测到的还款凭证索引
                if voucher_indices:
                    # 根据行数选择对应的还款凭证索引
                    target_index = voucher_indices[min(row_index - 1, len(voucher_indices) - 1)]
                    logger.info(f"还款凭证上传到第{row_index}行，使用智能检测的索引: {target_index} (还款凭证区域)")
                else:
                    # 回退到偶数索引策略
                    target_index = (row_index - 1) * 2  # 还款凭证在偶数索引
                    logger.info(f"还款凭证上传到第{row_index}行，使用偶数索引策略: {target_index} (还款凭证区域)")
            
            # 检查索引是否有效，如果超出范围则使用备选策略
            if target_index >= len(file_elements):
                logger.warning(f"计算的索引({target_index})超出元素总数({len(file_elements)})，使用备选策略")
                if is_deposit_file and receipt_indices:
                    # 对于代存文件，使用最后一个检测到的收据索引
                    target_index = receipt_indices[-1]
                elif not is_deposit_file and voucher_indices:
                    # 对于还款凭证，使用最后一个检测到的凭证索引
                    target_index = voucher_indices[-1]
                else:
                    # 最后的备选方案：使用简单的数学计算
                    if is_deposit_file:
                        target_index = len(file_elements) - 1 if len(file_elements) % 2 == 0 else len(file_elements) - 2
                        target_index = max(1, target_index)
                    else:
                        target_index = len(file_elements) - 2 if len(file_elements) >= 2 else 0
                        target_index = max(0, target_index)
                
                logger.info(f"调整后的目标索引: {target_index}")
            
            # 策略1: 尝试使用计算出的目标索引上传
            if target_index < len(file_elements):
                try:
                    element = file_elements[target_index]
                    is_visible = await element.is_visible()
                    is_enabled = await element.is_enabled()
                    
                    logger.debug(f"上传元素状态(行{row_index}, 索引{target_index}): visible={is_visible}, enabled={is_enabled}")
                    
                    # 对于隐藏的file input，我们仍然尝试设置文件
                    if is_enabled:  # 只要enabled就尝试
                        await element.set_input_files(file_path)
                        logger.info(f"上传{description}成功(行{row_index}): {Path(file_path).name} (索引 {target_index})")
                        success = True
                    else:
                        logger.warning(f"上传元素不可用(行{row_index}, 索引{target_index}): enabled={is_enabled}")
                        
                except Exception as e:
                    logger.debug(f"目标索引上传失败(行{row_index}, 索引{target_index}): {str(e)}")
            else:
                logger.warning(f"目标索引超出范围(行{row_index}, 索引{target_index}, 总数{len(file_elements)})")
            
            # 策略2: 如果策略1失败，尝试点击对应的拖拽上传区域
            if not success:
                logger.info(f"尝试点击拖拽上传区域(行{row_index}, {description})")
                
                # 查找拖拽上传区域
                drag_selectors = [
                    ".ant-upload-drag-container",
                    ".ant-upload-drag",
                    ".uploadIcon___27dku",
                    ".uploadText___12272",
                    "[class*='upload-drag']",
                    "[class*='upload'][class*='container']"
                ]
                
                for selector in drag_selectors:
                    try:
                        # 获取所有拖拽区域
                        drag_elements = await self.page.query_selector_all(selector)
                        logger.debug(f"找到 {len(drag_elements)} 个拖拽上传区域: {selector}")
                        
                        # 使用相同的索引逻辑
                        if target_index < len(drag_elements):
                            drag_element = drag_elements[target_index]
                            if await drag_element.is_visible():
                                # 点击拖拽区域
                                await drag_element.click()
                                logger.info(f"点击拖拽上传区域(行{row_index}, 索引{target_index}): {selector}")
                                await asyncio.sleep(1)
                                
                                # 重新查找file input（点击后可能会出现新的）
                                new_file_elements = await self.page.query_selector_all("input[type='file']")
                                if target_index < len(new_file_elements):
                                    try:
                                        await new_file_elements[target_index].set_input_files(file_path)
                                        logger.info(f"通过点击拖拽区域上传{description}成功(行{row_index}): {Path(file_path).name}")
                                        success = True
                                        break
                                    except Exception as e:
                                        logger.debug(f"点击后上传失败: {str(e)}")
                                        
                    except Exception as e:
                        logger.debug(f"拖拽区域选择器 {selector} 失败: {str(e)}")
                        continue
                        
                    if success:
                        break
            
            # 策略3: 如果前面都失败，尝试备选方案
            if not success:
                logger.warning(f"主要策略失败，尝试备选方案(行{row_index}, {description})")
                
                # 根据文件类型和智能检测结果选择备选索引
                if is_deposit_file:
                    # 代存文件：优先使用检测到的收据文件索引
                    if receipt_indices:
                        candidate_indices = receipt_indices.copy()
                        logger.info(f"代存收据备选方案：优先尝试检测到的收据索引 {candidate_indices}")
                    else:
                        # 回退到奇数索引策略
                        candidate_indices = [i for i in range(1, len(file_elements), 2)]
                        logger.info(f"代存收据备选方案：使用奇数索引策略 {candidate_indices[:3]}...")
                    # 添加其他索引作为最后备选
                    candidate_indices.extend([i for i in range(len(file_elements)) if i not in candidate_indices])
                else:
                    # 还款凭证：优先使用检测到的还款凭证索引
                    if voucher_indices:
                        candidate_indices = voucher_indices.copy()
                        logger.info(f"还款凭证备选方案：优先尝试检测到的凭证索引 {candidate_indices}")
                    else:
                        # 回退到偶数索引策略
                        candidate_indices = [i for i in range(0, len(file_elements), 2)]
                        logger.info(f"还款凭证备选方案：使用偶数索引策略 {candidate_indices[:3]}...")
                    # 添加其他索引作为最后备选
                    candidate_indices.extend([i for i in range(len(file_elements)) if i not in candidate_indices])
                
                # 按优先级顺序尝试候选索引
                for i in candidate_indices:
                    try:
                        element = file_elements[i]
                        if await element.is_enabled():
                            await element.set_input_files(file_path)
                            logger.info(f"使用备选索引上传{description}成功(行{row_index}): {Path(file_path).name} (索引{i})")
                            success = True
                            break
                    except Exception as e:
                        logger.debug(f"备选索引 {i} 上传失败: {str(e)}")
                        continue
            
            if not success:
                logger.error(f"所有上传方法都失败: {description}(行{row_index})")
                # 截图用于调试
                await self.take_screenshot(f"upload_failed_{description}_row{row_index}_{int(time.time())}.png")
                
            return success
            
        except Exception as e:
            logger.error(f"上传文件失败: {description}(行{row_index}) = {file_path}, 错误: {str(e)}")
            return False

    async def logout_user(self) -> bool:
        """
        登出当前用户

        Returns:
            登出是否成功
        """
        try:
            if not self.current_user:
                return True

            logger.info(f"登出用户: {self.current_user}")

            # 先点击用户名下拉菜单
            user_menu_selectors = [
                ".ant-dropdown-trigger",  # Ant Design下拉触发器
                "[class*='user']",  # 包含user的类名
                "[class*='dropdown']",  # 包含dropdown的类名
                ".user-menu",
                "[data-testid='user-menu']"
            ]
            
            menu_clicked = False
            for selector in user_menu_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        await element.click()
                        logger.info(f"点击用户菜单: {selector}")
                        menu_clicked = True
                        await asyncio.sleep(1)  # 等待下拉菜单出现
                        break
                except:
                    continue
            
            # 如果没找到下拉菜单，尝试直接寻找用户名文本
            if not menu_clicked:
                try:
                    # 尝试点击用户名文本
                    username_element = await self.page.query_selector(f"text={self.current_user}")
                    if username_element:
                        await username_element.click()
                        logger.info("点击用户名")
                        menu_clicked = True
                        await asyncio.sleep(1)
                except:
                    pass
            
            # 查找并点击退出登录选项
            logout_selectors = [
                "text=退出系统",  # 实际的退出文本
                "text=退出登录",
                "text=退出",
                "text=登出",
                "a:has-text('退出系统')",
                "button:has-text('退出系统')",
                "a:has-text('退出登录')",
                "button:has-text('退出登录')",
                ".logout-item",
                "[data-testid='logout']"
            ]
            
            logout_clicked = False
            for selector in logout_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        await element.click()
                        logger.info(f"点击退出登录: {selector}")
                        logout_clicked = True
                        break
                except:
                    continue
            
            if not logout_clicked:
                logger.warning("未找到退出登录选项，尝试直接导航到登录页面")
                await self.page.goto(self.config['settings']['base_url'])

            # 等待登出完成（检查是否回到登录页面）
            try:
                await self.page.wait_for_selector("#username", timeout=10000)
                logger.info("已返回登录页面")
            except:
                # 如果没有找到登录元素，直接导航到登录页面
                logger.info("强制导航到登录页面")
                await self.page.goto(self.config['settings']['base_url'])
                await self.page.wait_for_selector("#username", timeout=10000)

            self.current_user = None
            logger.info("用户登出成功")
            return True

        except Exception as e:
            logger.error(f"用户登出失败: {str(e)}")
            # 登出失败时截图
            await self.take_screenshot(f"logout_error_{self.current_user}_{int(time.time())}.png")
            self.current_user = None  # 重置状态
            return False

    async def take_screenshot(self, filename: str = None):
        """截图"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.png"
            
            # 确保截图保存在png文件夹中
            if not os.path.dirname(filename):  # 如果filename没有路径信息
                screenshot_path = os.path.join('png', filename)
            else:
                screenshot_path = filename
            
            await self.page.screenshot(path=screenshot_path, full_page=True)
            logger.info(f"截图保存: {screenshot_path}")
            
        except Exception as e:
            logger.error(f"截图失败: {str(e)}")

    async def process_user_debtors(self, username: str, debtors_list: List[Dict]) -> bool:
        """
        处理单个用户的所有债务人

        Args:
            username: 用户名
            debtors_list: 债务人列表

        Returns:
            处理是否成功
        """
        try:
            logger.info(f"开始处理用户 {username} 的 {len(debtors_list)} 个债务人")

            success_count = 0

            for i, debtor_info in enumerate(debtors_list, 1):
                logger.info(f"处理第 {i}/{len(debtors_list)} 个债务人: {debtor_info['debtor_name']}")

                try:
                    # 导航到债务人页面
                    if not await self.navigate_to_debtor_page(debtor_info['debtor_no'], debtor_info['debtor_name']):
                        continue

                    # 点击还款登记
                    payment_registration_result = await self.click_payment_registration()
                    
                    if payment_registration_result == "failed":
                        logger.error(f"债务人 {debtor_info['debtor_name']} 点击还款登记失败")
                        self.processed_debtors[debtor_info['debtor_name']] = "点击还款登记失败"
                        await self.take_screenshot(f"payment_reg_failed_{debtor_info['debtor_name']}_{int(time.time())}.png")
                        continue
                    elif payment_registration_result == "already_registered":
                        logger.info(f"债务人 {debtor_info['debtor_name']} 已有登记信息处于未认领状态，跳过处理")
                        self.processed_debtors[debtor_info['debtor_name']] = "已有登记信息处于未认领状态"
                        await self.take_screenshot(f"already_registered_{debtor_info['debtor_name']}_{int(time.time())}.png")
                        continue
                    elif payment_registration_result != "success":
                        logger.error(f"债务人 {debtor_info['debtor_name']} 还款登记未知错误: {payment_registration_result}")
                        self.processed_debtors[debtor_info['debtor_name']] = f"还款登记未知错误: {payment_registration_result}"
                        continue

                    # 填写还款表单
                    if await self.fill_payment_form(debtor_info):
                        success_count += 1
                        logger.info(f"债务人 {debtor_info['debtor_name']} 处理成功")
                        
                        # 记录成功处理的债务人
                        self.processed_debtors[debtor_info['debtor_name']] = True
                        
                        # 成功后截图
                        await self.take_screenshot(f"success_{debtor_info['debtor_name']}_{int(time.time())}.png")
                    else:
                        logger.error(f"债务人 {debtor_info['debtor_name']} 处理失败")
                        
                        # 记录失败的债务人
                        self.processed_debtors[debtor_info['debtor_name']] = "填写表单失败"
                        
                        # 失败后截图
                        await self.take_screenshot(f"failed_{debtor_info['debtor_name']}_{int(time.time())}.png")

                    # 操作间隔（缩短等待时间）
                    await asyncio.sleep(min(self.config['settings']['delay_between_operations'] / 1000, 1))  # 最大1秒

                except Exception as e:
                    logger.error(f"处理债务人 {debtor_info['debtor_name']} 时发生异常: {str(e)}")
                    await self.take_screenshot(f"error_{debtor_info['debtor_name']}_{int(time.time())}.png")
                    continue

            logger.info(f"用户 {username} 处理完成，成功: {success_count}/{len(debtors_list)}")
            return success_count == len(debtors_list)

        except Exception as e:
            logger.error(f"处理用户 {username} 的债务人时发生异常: {str(e)}")
            return False

    async def run(self, folder_path: str, headless: bool = False) -> bool:
        """
        运行自动化流程
        
        Args:
            folder_path: 包含Excel文件的文件夹路径
            headless: 是否无头模式运行
            
        Returns:
            运行是否成功
        """
        try:
            logger.info("开始运行还款登记自动化流程")
            
            # 读取文件夹中的Excel数据
            sheet1_df, sheet2_df, attention_files = self.read_excel_data(folder_path)
            
            if attention_files:
                logger.info(f"创建了 {len(attention_files)} 个需要关注的文件:")
                for file in attention_files:
                    logger.info(f"  - {file}")
            
            # 存储数据框引用以便后续更新状态
            self.sheet1_df = sheet1_df
            self.sheet2_df = sheet2_df

            # 处理用户数据
            users_data = self.process_user_data(sheet1_df, sheet2_df)
            
            if not users_data:
                logger.warning("没有找到可处理的用户数据")
                return False

            total_users = len(users_data)
            success_users = 0

            # 按用户循环处理
            for i, (username, debtors_list) in enumerate(users_data.items(), 1):
                logger.info(f"处理第 {i}/{total_users} 个用户: {username}")

                try:
                    # 为每个用户重新初始化浏览器
                    logger.info(f"为用户 {username} 初始化新的浏览器实例")
                    await self.init_browser(headless=headless)

                    # 登录用户
                    if not await self.login_user(username):
                        logger.error(f"用户 {username} 登录失败，跳过")
                        await self.close_browser()
                        continue

                    # 处理该用户的所有债务人
                    if await self.process_user_debtors(username, debtors_list):
                        success_users += 1

                    logger.info(f"用户 {username} 处理完成，关闭浏览器")

                except Exception as e:
                    logger.error(f"处理用户 {username} 时发生异常: {str(e)}")

                finally:
                    # 确保每个用户处理完后都关闭浏览器
                    await self.close_browser()
                    
                    # 用户间隔
                    if i < total_users:
                        logger.info(f"等待 3 秒后处理下一个用户...")
                        await asyncio.sleep(3)

            logger.info(f"自动化流程完成，成功处理用户: {success_users}/{total_users}")
            
            # 更新Excel中成功处理的债务人状态
            self.update_processed_debtors_status()
            
            # 生成合并的结果文件
            output_filename = self.generate_combined_result_file(folder_path, attention_files)
            
            return success_users == total_users

        except Exception as e:
            logger.error(f"自动化流程运行失败: {str(e)}")
            return False

    def generate_combined_result_file(self, folder_path: str, attention_files: List[str]) -> str:
        """
        生成合并的结果文件
        
        Args:
            folder_path: 源文件夹路径
            attention_files: 需要关注的文件列表
            
        Returns:
            生成的结果文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"还款登记处理结果_{timestamp}.xlsx"
            
            with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
                # 保存处理结果明细
                if self.sheet1_df is not None:
                    self.sheet1_df.to_excel(writer, sheet_name='处理结果明细', index=False)
                
                # 保存还款对账明细
                if self.sheet2_df is not None:
                    self.sheet2_df.to_excel(writer, sheet_name='还款对账明细', index=False)
                
                # 生成处理统计
                stats_data = self.generate_processing_statistics()
                if stats_data:
                    stats_df = pd.DataFrame(stats_data)
                    stats_df.to_excel(writer, sheet_name='处理统计', index=False)
            
            # 应用高亮格式到失败的记录
            self.apply_highlighting_to_failed_records(output_filename)
            
            logger.info(f"合并结果文件已生成: {output_filename}")
            return output_filename
            
        except Exception as e:
            logger.error(f"生成合并结果文件失败: {str(e)}")
            return ""

    def apply_highlighting_to_failed_records(self, excel_filename: str):
        """对失败的记录应用高亮格式"""
        try:
            from openpyxl import load_workbook
            from openpyxl.styles import PatternFill, Font
            
            # 定义高亮样式
            fail_fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')  # 浅红色背景
            fail_font = Font(color='CC0000', bold=True)  # 深红色粗体字体
            
            # 获取失败的债务人名单
            failed_names = []
            if self.processing_summary['failed_debtors']:
                failed_names = [debtor['name'] for debtor in self.processing_summary['failed_debtors']]
            
            if not failed_names:
                return
            
            # 加载工作簿
            wb = load_workbook(excel_filename)
            
            # 高亮处理结果明细表
            if '处理结果明细' in wb.sheetnames:
                ws1 = wb['处理结果明细']
                col_config = self.config['excel_config']['key_columns']
                payer_col = col_config.get('payer', '还款人')
                
                # 查找还款人列的索引
                payer_col_index = None
                for col_idx, cell in enumerate(ws1[1], 1):  # 第一行是标题行
                    if cell.value == payer_col:
                        payer_col_index = col_idx
                        break
                
                if payer_col_index:
                    # 遍历数据行，高亮失败的记录
                    for row_idx in range(2, ws1.max_row + 1):  # 从第2行开始（跳过标题）
                        payer_cell = ws1.cell(row=row_idx, column=payer_col_index)
                        if payer_cell.value in failed_names:
                            # 高亮整行
                            for col_idx in range(1, ws1.max_column + 1):
                                cell = ws1.cell(row=row_idx, column=col_idx)
                                cell.fill = fail_fill
                                cell.font = fail_font
            
            # 高亮还款对账明细表
            if '还款对账明细' in wb.sheetnames:
                ws2 = wb['还款对账明细']
                col_config = self.config['excel_config']['key_columns']
                debtor_name_col = col_config.get('debtor_name', '债务人姓名')
                
                # 查找债务人姓名列的索引
                debtor_name_col_index = None
                for col_idx, cell in enumerate(ws2[1], 1):  # 第一行是标题行
                    if cell.value == debtor_name_col:
                        debtor_name_col_index = col_idx
                        break
                
                if debtor_name_col_index:
                    # 遍历数据行，高亮失败的记录
                    for row_idx in range(2, ws2.max_row + 1):  # 从第2行开始（跳过标题）
                        debtor_name_cell = ws2.cell(row=row_idx, column=debtor_name_col_index)
                        if debtor_name_cell.value in failed_names:
                            # 高亮整行
                            for col_idx in range(1, ws2.max_column + 1):
                                cell = ws2.cell(row=row_idx, column=col_idx)
                                cell.fill = fail_fill
                                cell.font = fail_font
            
            # 保存修改
            wb.save(excel_filename)
            logger.info(f"已对 {len(failed_names)} 个失败记录应用高亮格式")
            
        except Exception as e:
            logger.warning(f"应用高亮格式失败: {str(e)}")

    def display_final_summary(self):
        """显示最终处理汇总"""
        try:
            
            summary = self.processing_summary
            total_count = summary['success_count'] + summary['failed_count']
            
            print("\n" + "="*60)
            print("📊 还款登记处理结果汇总")
            print("="*60)
            print(f"✅ 总处理数量: {total_count} 个债务人")
            print(f"✅ 成功处理: {summary['success_count']} 个")
            print(f"❌ 失败处理: {summary['failed_count']} 个")
            
            if summary['failed_count'] > 0:
                print(f"\n❌ 失败的债务人明细:")
                print("-" * 40)
                for i, failed_debtor in enumerate(summary['failed_debtors'], 1):
                    print(f"{i:2d}. {failed_debtor['name']} - {failed_debtor['status']}")
                
                print(f"\n⚠️  失败记录已在Excel文件中用红色高亮标记")
                print(f"💡 请手动处理失败的 {summary['failed_count']} 个债务人")
            else:
                print("\n🎉 所有债务人都已成功处理!")
            
            # 根据结果显示相应的状态消息
            if summary['failed_count'] > 0:
                print(f"\n⚠️  还款登记自动化流程部分完成，有 {summary['failed_count']} 个失败记录需要处理。")
            else:
                print(f"\n🎉 还款登记自动化流程全部成功完成！")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"显示最终汇总失败: {str(e)}")
            print("\n还款登记自动化流程执行完成，但显示汇总信息时出错。")

    def generate_processing_statistics(self) -> List[Dict]:
        """生成处理统计数据"""
        try:
            stats_data = []
            
            # 用户统计
            user_stats = {}
            for debtor_name, status in self.processed_debtors.items():
                # 这里需要根据debt_name找到对应的催收员
                # 简化处理，直接统计状态
                status_key = "成功处理" if status is True else str(status)
                user_stats[status_key] = user_stats.get(status_key, 0) + 1
            
            for status, count in user_stats.items():
                stats_data.append({
                    '处理状态': status,
                    '债务人数量': count,
                    '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            return stats_data
            
        except Exception as e:
            logger.error(f"生成处理统计失败: {str(e)}")
            return []

    def create_combined_attention_file(self, all_attention_records: List[Dict]) -> str:
        """
        创建统一的需要关注的还款登记信息文件（包含所有源文件的记录）
        
        Args:
            all_attention_records: 来自所有文件的需要关注的记录列表
            
        Returns:
            创建的文件路径
        """
        try:
            if not all_attention_records:
                return ""
            
            # 生成文件名（使用完整时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"需做还款登记信息_{timestamp}.xlsx"
            
            # 转换为DataFrame
            df = pd.DataFrame(all_attention_records)
            
            # 按源文件分组统计
            source_file_stats = df['源文件'].value_counts()
            
            # 保存到Excel文件，包含多个Sheet
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 主Sheet：所有需要关注的记录
                df.to_excel(writer, sheet_name="需要关注的记录", index=False)
                
                # 统计Sheet：按源文件分组统计
                stats_data = []
                for source_file, count in source_file_stats.items():
                    stats_data.append({
                        '源文件': source_file,
                        '需要关注的记录数': count,
                        '处理时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name="统计信息", index=False)
            
            logger.info(f"创建统一的需要关注文件: {filename}")
            logger.info(f"  - 总记录数: {len(all_attention_records)}")
            logger.info(f"  - 涉及文件数: {len(source_file_stats)}")
            for source_file, count in source_file_stats.items():
                logger.info(f"    * {source_file}: {count}条记录")
            
            return filename
            
        except Exception as e:
            logger.error(f"创建统一关注文件失败: {str(e)}")
            return ""

    def detect_sheet_name(self, available_sheets: List[str], preferred_name: str, fallback_keywords: List[str]) -> str:
        """
        自动检测正确的Sheet名称
        
        Args:
            available_sheets: Excel文件中可用的Sheet名称列表
            preferred_name: 首选的Sheet名称
            fallback_keywords: 备选关键词列表
            
        Returns:
            检测到的Sheet名称
        """
        # 首先尝试精确匹配
        if preferred_name in available_sheets:
            return preferred_name
        
        # 尝试关键词匹配
        for keyword in fallback_keywords:
            for sheet_name in available_sheets:
                if keyword in sheet_name:
                    logger.info(f"通过关键词'{keyword}'匹配到Sheet: {sheet_name}")
                    return sheet_name
        
        # 如果都没有匹配到，返回第一个可用的Sheet（但记录警告）
        if available_sheets:
            logger.warning(f"未找到匹配的Sheet，使用第一个可用Sheet: {available_sheets[0]}")
            return available_sheets[0]
        
        raise ValueError("Excel文件中没有可用的Sheet")

    def read_excel_data(self, folder_path: str) -> Tuple[pd.DataFrame, pd.DataFrame, List[str]]:
        """
        读取文件夹中所有包含"还款凭证解析"的Excel文件的两个sheet
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            (合并后的sheet1_df, 合并后的sheet2_df, 创建的关注文件列表)
        """
        try:
            # 获取所有符合条件的Excel文件
            excel_files = self.get_excel_files_from_folder(folder_path)
            
            if not excel_files:
                raise ValueError(f"在文件夹 {folder_path} 中未找到包含'还款凭证解析'的Excel文件")
            
            sheet1_name = self.config['excel_config']['sheet1_name']
            sheet2_name = self.config['excel_config']['sheet2_name']
            
            all_sheet1_data = []
            all_sheet2_data = []
            all_attention_records = []
            attention_files = []
            
            for excel_file in excel_files:
                try:
                    logger.info(f"处理文件: {os.path.basename(excel_file)}")
                    
                    # 读取两个sheet
                    sheet1_df = pd.read_excel(excel_file, sheet_name=sheet1_name)
                    sheet2_df = pd.read_excel(excel_file, sheet_name=sheet2_name)
                    
                    logger.info(f"  - {sheet1_name}: {len(sheet1_df)}行")
                    logger.info(f"  - {sheet2_name}: {len(sheet2_df)}行")
                    
                    # 处理结清验证状态，分离需要关注的记录
                    filtered_sheet1_df, attention_records = self.process_settlement_status(sheet1_df)
                    
                    # 收集需要关注的记录，但不立即创建文件
                    if attention_records:
                        # 为每条记录添加源文件信息
                        for record in attention_records:
                            record['源文件'] = os.path.basename(excel_file)
                        all_attention_records.extend(attention_records)
                        logger.info(f"  - 收集到 {len(attention_records)} 条需要关注的记录")
                    
                    # 添加文件来源标识
                    filtered_sheet1_df['源文件'] = os.path.basename(excel_file)
                    sheet2_df['源文件'] = os.path.basename(excel_file)
                    
                    all_sheet1_data.append(filtered_sheet1_df)
                    all_sheet2_data.append(sheet2_df)
                    
                except Exception as e:
                    logger.error(f"处理文件 {excel_file} 失败: {str(e)}")
                    continue
            
            if not all_sheet1_data or not all_sheet2_data:
                raise ValueError("没有成功读取到任何有效的Excel数据")
            
            # 合并所有数据
            combined_sheet1_df = pd.concat(all_sheet1_data, ignore_index=True)
            combined_sheet2_df = pd.concat(all_sheet2_data, ignore_index=True)
            
            # 在处理完所有文件后，统一创建关注文件
            if all_attention_records:
                attention_file = self.create_combined_attention_file(all_attention_records)
                if attention_file:
                    attention_files.append(attention_file)
                    logger.info(f"统一创建关注文件: {attention_file}, 包含来自 {len(excel_files)} 个文件的 {len(all_attention_records)} 条记录")
            
            logger.info(f"合并完成 - {sheet1_name}: {len(combined_sheet1_df)}行, {sheet2_name}: {len(combined_sheet2_df)}行")
            logger.info(f"总计需要关注的记录: {len(all_attention_records)}条")
            
            return combined_sheet1_df, combined_sheet2_df, attention_files
            
        except Exception as e:
            logger.error(f"读取Excel数据失败: {str(e)}")
            raise


async def main():
    """主函数"""
    try:
        # 设置文件夹路径
        folder_path = r"D:\还款登记操作"
        
        print("正在处理文件夹中的还款凭证解析文件...")
        print(f"目标文件夹: {folder_path}")

        # 检查文件夹是否存在
        if not os.path.exists(folder_path):
            print(f"错误：文件夹 {folder_path} 不存在！")
            return
        
        # 创建自动化机器人
        bot = PaymentRegistrationBotPlaywright()
        
        # 先检查文件夹中是否有符合条件的Excel文件
        excel_files = bot.get_excel_files_from_folder(folder_path)
        if not excel_files:
            print(f"错误：在文件夹 {folder_path} 中未找到包含'还款凭证解析'的Excel文件！")
            return
        
        print(f"找到 {len(excel_files)} 个符合条件的Excel文件：")
        for file in excel_files:
            print(f"  - {os.path.basename(file)}")

        # 运行自动化流程 (headless=False 显示浏览器窗口)
        success = await bot.run(folder_path, headless=False)

        # 显示最终处理汇总（无论成功还是失败）
        bot.display_final_summary()

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"程序执行失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main()) 
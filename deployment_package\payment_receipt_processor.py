#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
民生4期回款凭证处理脚本
功能：读取指定文件夹下的所有图片文件，进行OCR识别，并将结果保存到Excel中
作者：AI Assistant
日期：2025-01-20
"""

import os
import re
import zipfile
import rarfile
import py7zr
import pandas as pd
from datetime import datetime
from pathlib import Path
from PIL import Image
import logging
from typing import Dict, List, Tuple, Optional
import time

# DuckDB支持（轻量级数据库）
try:
    import duckdb
    DUCKDB_AVAILABLE = True
except ImportError:
    DUCKDB_AVAILABLE = False
    print("提示: DuckDB 未安装，使用pandas查询模式。安装命令: pip install duckdb")

# OCR库导入 - 按兼容性和稳定性排序
# 1. Tesseract（成熟稳定的OCR引擎）
try:
    import pytesseract
    PYTESSERACT_AVAILABLE = True
except ImportError:
    PYTESSERACT_AVAILABLE = False

# 2. Ollama OCR（智能识别，作为增强选项）
try:
    from ollama_ocr import OCRProcessor
    OLLAMA_OCR_AVAILABLE = True
except ImportError:
    OLLAMA_OCR_AVAILABLE = False

# 3. EasyOCR（备用，如果版本兼容）
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

# 4. PaddleOCR（高性能OCR引擎，优先推荐）
try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

# 5. 豆包Seed-1.6（AI视觉理解，最优中文识别）
try:
    from openai import OpenAI
    import json
    import base64
    from io import BytesIO
    DOUBAO_AVAILABLE = True
except ImportError:
    DOUBAO_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('payment_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 抑制第三方库的调试信息
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("openai").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("requests").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)


class DoubaoOCRProcessor:
    """豆包Seed-1.6 API OCR处理器"""
    
    def __init__(self, api_key: str, base_url: str = "https://ark.cn-beijing.volces.com/api/v3"):
        """
        初始化豆包OCR处理器
        
        Args:
            api_key: 豆包API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url
        
        # 抑制HTTP调试信息
        import logging
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("openai").setLevel(logging.WARNING)
        logging.getLogger("httpcore").setLevel(logging.WARNING)
        
        # 初始化豆包客户端
        self.client = OpenAI(
            base_url=base_url,
            api_key=api_key,
        )
        
        # API调用统计
        self.api_calls = 0
        self.total_cost = 0.0
        self.success_count = 0
        self.failure_count = 0
        
    def encode_image_to_base64(self, image_path: str) -> str:
        """
        将图片编码为base64格式，带压缩优化
        
        Args:
            image_path: 图片路径
            
        Returns:
            base64编码的图片数据
        """
        try:
            # 打开并压缩图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（确保兼容性）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 获取原始尺寸
                width, height = img.size
                max_size = 1024  # 最大边长限制
                
                # 如果图片太大，进行压缩
                if max(width, height) > max_size:
                    if width > height:
                        new_width = max_size
                        new_height = int(height * (max_size / width))
                    else:
                        new_height = max_size
                        new_width = int(width * (max_size / height))
                    
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    # logger.debug(f"图片已压缩: {width}x{height} -> {new_width}x{new_height}")
                
                # 保存到内存并编码
                buffer = BytesIO()
                img.save(buffer, format='JPEG', quality=85, optimize=True)
                encoded_string = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                return encoded_string
                
        except Exception as e:
            logger.error(f"图片编码失败: {str(e)}")
            return ""
    
    def create_payment_analysis_prompt(self) -> str:
        """
        创建专门用于支付凭证分析的提示词
        
        Returns:
            格式化的提示词
        """
        return """请仔细分析这张支付凭证图片，提取以下关键信息并以JSON格式返回：

{
  "付款方姓名": "从图片中识别的付款人/转账人姓名（如果有星号遮挡，请尽量识别可见部分，包括星号）",
  "交易时间": "交易发生的时间（格式：YYYY-MM-DD HH:MM:SS，如果只有日期则补充00:00:00）",
  "交易账户": "付款方的银行账户号码（重点关注卡号/账号的后4位数字）",
  "交易金额": "转账金额（仅数字，不包含货币符号）",
  "收款方信息": "收款人/收款账户信息",
  "备注附言": "转账备注、用途说明、摘要等附加信息",
  "银行名称": "发起转账的银行名称",
  "置信度": "对识别结果的置信度评分（0-1之间的小数）"
}

分析要求：
1. 特别关注中文文字的识别准确性
2. 对于姓名中的星号遮挡，必须保留星号在结果中，如：张*明、李**等
3. 账户号码重点提取后4位数字，不要前4位
4. 金额识别要准确，避免将其他数字误识别为金额
5. 如果某个字段无法识别，设置为空字符串""
6. 备注附言要完整提取，包含所有相关信息

请仅返回JSON格式的结果，不要包含其他说明文字。"""

    def analyze_payment_image(self, image_path: str) -> Dict:
        """
        使用豆包Seed-1.6 API分析支付凭证图片
        
        Args:
            image_path: 图片路径
            
        Returns:
            识别结果字典
        """
        try:
            self.api_calls += 1
            # logger.debug(f"🤖 [API调用 #{self.api_calls}] 分析图片: {os.path.basename(image_path)}")
            
            # 编码图片
            base64_image = self.encode_image_to_base64(image_path)
            if not base64_image:
                self.failure_count += 1
                return {"success": False, "error": "图片编码失败"}
            
            # 发送API请求 - 使用豆包Seed-1.6格式
            start_time = time.time()
            
            response = self.client.chat.completions.create(
                model="doubao-seed-1-6-250615",  # 豆包Seed-1.6模型
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            },
                            {
                                "type": "text", 
                                "text": self.create_payment_analysis_prompt()
                            }
                        ],
                    }
                ],
                temperature=0.1,
                max_tokens=2048
            )
            
            processing_time = time.time() - start_time
            
            # 解析响应
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content
                # logger.debug(f"🤖 AI响应内容: {content[:100]}...")
                
                # 计算成本（豆包定价估算）
                cost = 0.001  # 估算每次调用成本
                self.total_cost += cost
                
                # 提取JSON部分
                try:
                    # 尝试直接解析JSON
                    if content.strip().startswith('{'):
                        parsed_result = json.loads(content.strip())
                    else:
                        # 提取JSON代码块
                        json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', content, re.DOTALL)
                        if json_match:
                            parsed_result = json.loads(json_match.group(1))
                        else:
                            # 尝试提取第一个完整的JSON对象
                            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', content)
                            if json_match:
                                parsed_result = json.loads(json_match.group(0))
                            else:
                                raise ValueError("无法提取JSON数据")
                    
                    # 验证必要字段并设置默认值
                    required_fields = [
                        '付款方姓名', '交易时间', '交易账户', '交易金额', 
                        '收款方信息', '备注附言', '银行名称', '置信度'
                    ]
                    
                    for field in required_fields:
                        if field not in parsed_result:
                            parsed_result[field] = ""
                    
                    # 设置置信度默认值
                    if not parsed_result.get('置信度'):
                        parsed_result['置信度'] = 0.8
                    
                    self.success_count += 1
                    # logger.debug(f"✅ AI分析成功 - 置信度: {parsed_result.get('置信度', 'N/A')}")
                    
                    return {
                        'success': True,
                        'data': parsed_result,
                        'cost': cost,
                        'processing_time': processing_time
                    }
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON解析失败: {str(e)}")
                    self.failure_count += 1
                    return {
                        'success': False,
                        'error': f'JSON解析失败: {str(e)}',
                        'raw_content': content
                    }
            else:
                logger.error("❌ API响应为空")
                self.failure_count += 1
                return {
                    'success': False,
                    'error': 'API响应为空'
                }
                    
        except Exception as e:
            logger.error(f"❌ API调用失败: {str(e)}")
            self.failure_count += 1
            return {'success': False, 'error': f'API调用失败: {str(e)}'}
    
    def get_statistics(self) -> Dict:
        """
        获取API调用统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'api_calls': self.api_calls,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'success_rate': self.success_count / self.api_calls if self.api_calls > 0 else 0,
            'total_cost': self.total_cost,
            'average_cost_per_call': self.total_cost / self.api_calls if self.api_calls > 0 else 0
        }


class DebtorDatabase:
    """
    债务人数据库管理类 - 使用DuckDB提供高效查询（持久化版本）
    """
    
    def __init__(self, excel_file: str, db_file: str = "debtors.duckdb"):
        """
        初始化债务人数据库
        
        Args:
            excel_file: 债务人明细Excel文件路径
            db_file: DuckDB数据库文件路径
        """
        self.excel_file = excel_file
        self.db_file = db_file
        self.conn = None
        self.use_duckdb = DUCKDB_AVAILABLE
        self.data_loaded = False
        self.excel_mtime = None  # Excel文件修改时间
        
        if self.use_duckdb:
            try:
                # 创建或连接到本地数据库文件
                self.conn = duckdb.connect(self.db_file)
                logger.info(f"DuckDB数据库连接成功: {self.db_file}")
            except Exception as e:
                logger.warning(f"DuckDB初始化失败，回退到pandas模式: {e}")
                self.use_duckdb = False
                self.conn = None
    
    def _check_excel_updated(self) -> bool:
        """
        检查Excel文件是否已更新
        
        Returns:
            如果Excel文件比数据库新，返回True
        """
        if not os.path.exists(self.excel_file):
            return False
            
        # 获取Excel文件修改时间
        excel_mtime = os.path.getmtime(self.excel_file)
        
        if not os.path.exists(self.db_file):
            # 数据库文件不存在，需要创建
            self.excel_mtime = excel_mtime
            return True
            
        # 获取数据库文件修改时间
        db_mtime = os.path.getmtime(self.db_file)
        
        # 如果Excel文件更新
        if excel_mtime > db_mtime:
            self.excel_mtime = excel_mtime
            logger.info("检测到Excel文件已更新，需要重新导入数据")
            return True
            
        self.excel_mtime = excel_mtime
        return False
    
    def _check_table_exists(self) -> bool:
        """
        检查数据库中是否存在debtors表
        
        Returns:
            表是否存在
        """
        if not self.use_duckdb or not self.conn:
            return False
            
        try:
            result = self.conn.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_name = 'debtors'
            """).fetchone()
            
            return result and result[0] > 0
            
        except Exception as e:
            # logger.debug(f"检查表存在性失败: {e}")
            return False
    
    def load_data(self) -> bool:
        """
        加载债务人数据到数据库（智能增量更新）
        
        Returns:
            加载是否成功
        """
        try:
            if not os.path.exists(self.excel_file):
                logger.warning(f"债务人明细文件不存在：{self.excel_file}")
                return False
            
            if self.use_duckdb and self.conn:
                # 检查是否需要重新导入数据
                table_exists = self._check_table_exists()
                excel_updated = self._check_excel_updated()
                
                if table_exists and not excel_updated:
                    # 表存在且Excel未更新，直接使用现有数据
                    count_result = self.conn.execute("SELECT COUNT(*) FROM debtors").fetchone()
                    record_count = count_result[0] if count_result else 0
                    logger.info(f"使用现有DuckDB数据：{record_count:,} 条记录")
                    self.data_loaded = True
                    return True
                
                # 需要重新导入数据
                logger.info("正在导入债务人数据到DuckDB数据库...")
                
                # 删除现有表（如果存在）
                if table_exists:
                    self.conn.execute("DROP TABLE IF EXISTS debtors")
                    logger.info("已删除旧的数据表")
                
                # 创建表并加载数据
                create_sql = f"""
                CREATE TABLE debtors AS 
                SELECT * FROM read_xlsx('{self.excel_file}')
                """
                
                self.conn.execute(create_sql)
                
                # 获取记录数量
                count_result = self.conn.execute("SELECT COUNT(*) FROM debtors").fetchone()
                record_count = count_result[0] if count_result else 0
                
                logger.info(f"DuckDB成功导入债务人数据：{record_count:,} 条记录")
                
                # 创建索引以提高查询性能
                try:
                    logger.info("正在创建数据库索引...")
                    self.conn.execute("CREATE INDEX IF NOT EXISTS idx_identity_no ON debtors(identity_no)")
                    self.conn.execute("CREATE INDEX IF NOT EXISTS idx_case_no ON debtors(case_no)")
                    self.conn.execute("CREATE INDEX IF NOT EXISTS idx_original_loan_case_no ON debtors(original_loan_case_no)")
                    logger.info("数据库索引创建完成")
                except Exception as e:
                    logger.warning(f"索引创建失败（不影响功能）: {e}")
                
                # 执行VACUUM优化数据库
                try:
                    self.conn.execute("VACUUM")
                    # logger.debug("数据库优化完成")
                except Exception as e:
                    # logger.debug(f"数据库优化失败（可忽略）: {e}")
                    pass
                
            else:
                # 回退到pandas模式
                logger.info("使用pandas加载债务人数据...")
                self.df = pd.read_excel(self.excel_file)
                logger.info(f"pandas成功加载债务人数据：{len(self.df):,} 条记录")
            
            self.data_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"加载债务人数据失败：{str(e)}")
            return False
    
    def get_database_info(self) -> Dict:
        """
        获取数据库详细信息
        
        Returns:
            数据库信息字典
        """
        info = {
            'database_file': self.db_file,
            'excel_file': self.excel_file,
            'database_exists': os.path.exists(self.db_file),
            'excel_exists': os.path.exists(self.excel_file),
            'using_duckdb': self.use_duckdb,
            'data_loaded': self.data_loaded
        }
        
        if os.path.exists(self.db_file):
            info['database_size'] = f"{os.path.getsize(self.db_file) / 1024 / 1024:.2f} MB"
            info['database_mtime'] = datetime.fromtimestamp(os.path.getmtime(self.db_file)).strftime("%Y-%m-%d %H:%M:%S")
        
        if os.path.exists(self.excel_file):
            info['excel_size'] = f"{os.path.getsize(self.excel_file) / 1024 / 1024:.2f} MB"
            info['excel_mtime'] = datetime.fromtimestamp(os.path.getmtime(self.excel_file)).strftime("%Y-%m-%d %H:%M:%S")
        
        return info
    
    def query_debtor_info(self, identity_no: str = None, contract_no: str = None) -> Dict:
        """
        查询债务人信息
        
        Args:
            identity_no: 身份证号
            contract_no: 合同号
            
        Returns:
            债务人信息字典
        """
        result = {
            'debtor_no': '',
            'collecter_user_name': '',
            'total_principal': 0.0,
            'remaining_principal': 0.0,
            'debtor_name': '',
            'identity_no': '',
            'found': False,
            'search_method': ''
        }
        
        if not self.data_loaded:
            return result
        
        try:
            if self.use_duckdb and self.conn:
                return self._query_with_duckdb(identity_no, contract_no)
            else:
                return self._query_with_pandas(identity_no, contract_no)
                
        except Exception as e:
            logger.error(f"查询债务人信息时出错：{str(e)}")
            return result
    
    def _query_with_duckdb(self, identity_no: str = None, contract_no: str = None) -> Dict:
        """
        使用DuckDB查询债务人信息
        """
        result = {
            'debtor_no': '',
            'collecter_user_name': '',
            'total_principal': 0.0,
            'remaining_principal': 0.0,
            'debtor_name': '',
            'identity_no': '',
            'found': False,
            'search_method': ''
        }
        
        # 优先使用身份证号查询
        if identity_no and identity_no.strip():
            sql = """
            SELECT debtor_no, collecter_user_name, total_principal, remaining_principal, name, identity_no
            FROM debtors 
            WHERE identity_no = ? 
            LIMIT 1
            """
            
            query_result = self.conn.execute(sql, [identity_no.strip()]).fetchone()
            
            if query_result:
                result['debtor_no'] = str(query_result[0])
                result['collecter_user_name'] = str(query_result[1])
                result['total_principal'] = float(query_result[2]) if query_result[2] else 0.0
                result['remaining_principal'] = float(query_result[3]) if query_result[3] else 0.0
                result['debtor_name'] = str(query_result[4]) if query_result[4] else ''
                result['identity_no'] = str(query_result[5]) if query_result[5] else ''
                result['found'] = True
                result['search_method'] = '身份证号'
                # logger.debug(f"DuckDB通过身份证号 {identity_no} 找到债务人信息")
                return result
        
        # 如果身份证号查询失败，尝试合同号查询
        if contract_no and contract_no.strip():
            sql = """
            SELECT debtor_no, collecter_user_name, total_principal, remaining_principal, name, identity_no
            FROM debtors 
            WHERE case_no = ? OR original_loan_case_no = ? 
            LIMIT 1
            """
            
            query_result = self.conn.execute(sql, [contract_no.strip(), contract_no.strip()]).fetchone()
            
            if query_result:
                result['debtor_no'] = str(query_result[0])
                result['collecter_user_name'] = str(query_result[1])
                result['total_principal'] = float(query_result[2]) if query_result[2] else 0.0
                result['remaining_principal'] = float(query_result[3]) if query_result[3] else 0.0
                result['debtor_name'] = str(query_result[4]) if query_result[4] else ''
                result['identity_no'] = str(query_result[5]) if query_result[5] else ''
                result['found'] = True
                result['search_method'] = '合同号'
                # logger.debug(f"DuckDB通过合同号 {contract_no} 找到债务人信息")
                return result
        
        # logger.debug(f"DuckDB未找到债务人信息：身份证号={identity_no}, 合同号={contract_no}")
        return result
    
    def _query_with_pandas(self, identity_no: str = None, contract_no: str = None) -> Dict:
        """
        使用pandas查询债务人信息（回退方案）
        """
        result = {
            'debtor_no': '',
            'collecter_user_name': '',
            'total_principal': 0.0,
            'remaining_principal': 0.0,
            'debtor_name': '',
            'identity_no': '',
            'found': False,
            'search_method': ''
        }
        
        # 优先使用身份证号查询
        if identity_no and identity_no.strip():
            mask = self.df['identity_no'] == identity_no.strip()
            matches = self.df[mask]
            
            if not matches.empty:
                first_match = matches.iloc[0]
                result['debtor_no'] = str(first_match['debtor_no'])
                result['collecter_user_name'] = str(first_match['collecter_user_name'])
                result['total_principal'] = float(first_match['total_principal']) if pd.notna(first_match['total_principal']) else 0.0
                result['remaining_principal'] = float(first_match['remaining_principal']) if pd.notna(first_match['remaining_principal']) else 0.0
                result['debtor_name'] = str(first_match['name']) if pd.notna(first_match['name']) else ''
                result['identity_no'] = str(first_match['identity_no']) if pd.notna(first_match['identity_no']) else ''
                result['found'] = True
                result['search_method'] = '身份证号'
                # logger.debug(f"pandas通过身份证号 {identity_no} 找到债务人信息")
                return result
        
        # 如果身份证号查询失败，尝试合同号查询
        if contract_no and contract_no.strip():
            mask1 = self.df['case_no'] == contract_no.strip()
            matches1 = self.df[mask1]
            
            mask2 = self.df['original_loan_case_no'] == contract_no.strip()
            matches2 = self.df[mask2]
            
            matches = pd.concat([matches1, matches2]).drop_duplicates()
            
            if not matches.empty:
                first_match = matches.iloc[0]
                result['debtor_no'] = str(first_match['debtor_no'])
                result['collecter_user_name'] = str(first_match['collecter_user_name'])
                result['total_principal'] = float(first_match['total_principal']) if pd.notna(first_match['total_principal']) else 0.0
                result['remaining_principal'] = float(first_match['remaining_principal']) if pd.notna(first_match['remaining_principal']) else 0.0
                result['debtor_name'] = str(first_match['name']) if pd.notna(first_match['name']) else ''
                result['identity_no'] = str(first_match['identity_no']) if pd.notna(first_match['identity_no']) else ''
                result['found'] = True
                result['search_method'] = '合同号'
                # logger.debug(f"pandas通过合同号 {contract_no} 找到债务人信息")
                return result
        
        # logger.debug(f"pandas未找到债务人信息：身份证号={identity_no}, 合同号={contract_no}")
        return result
    
    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        if not self.data_loaded:
            return {}
        
        try:
            if self.use_duckdb and self.conn:
                # 使用DuckDB获取统计信息
                total_count = self.conn.execute("SELECT COUNT(*) FROM debtors").fetchone()[0]
                identity_count = self.conn.execute("SELECT COUNT(*) FROM debtors WHERE identity_no IS NOT NULL AND identity_no != ''").fetchone()[0]
                case_count = self.conn.execute("SELECT COUNT(*) FROM debtors WHERE case_no IS NOT NULL AND case_no != ''").fetchone()[0]
                
                return {
                    'total_records': total_count,
                    'with_identity_no': identity_count,
                    'with_case_no': case_count,
                    'database_type': 'DuckDB'
                }
            else:
                # 使用pandas获取统计信息
                total_count = len(self.df)
                identity_count = self.df['identity_no'].notna().sum()
                case_count = self.df['case_no'].notna().sum()
                
                return {
                    'total_records': total_count,
                    'with_identity_no': identity_count,
                    'with_case_no': case_count,
                    'database_type': 'pandas'
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败：{str(e)}")
            return {}
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            # logger.debug("DuckDB数据库连接已关闭")

class PaymentReceiptProcessor:
    def __init__(self, base_folder: str, ocr_engine: str = 'auto'):
        """
        初始化处理器
        
        Args:
            base_folder: 基础文件夹路径
            ocr_engine: OCR引擎选择 ('auto', 'tesseract', 'easyocr', 'paddleocr', 'none')
        """
        self.base_folder = Path(base_folder)
        self.results = []
        self.statistics = {
            'total_images': 0,
            'processed_images': 0,
            'ocr_success': 0,
            'account_extracted': 0,
            'filename_parsed': 0,
            'parse_success': 0
        }
        
        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 支持的压缩文件格式
        self.archive_extensions = {'.zip', '.rar', '.7z'}
        
        # Excel列名 - 增强版：添加匹配验证列和结清验证状态
        self.excel_columns = [
            '文件夹名称', '完整文件路径', '还款人', '还款时间', '图片OCR识别金额',
            '还款账号', '还款来源', '是否代存', '代存图片路径', '图片文件名金额',
            '图片文件名姓名', '图片身份证后4位', 'debtor_no', '失败原因', 
            'OCR文件名匹配状态', 'OCR准确度评估', '结清验证状态'
        ]
        
        # 初始化OCR引擎 - 智能选择最佳OCR引擎
        self.ocr_engine_type = self._select_ocr_engine(ocr_engine)
        self.ocr = self._init_ocr_engine(self.ocr_engine_type)
        
        # 初始化债务人数据库
        self.debtor_db = self._init_debtor_database()
        
        # 用于统计二级目录的处理结果
        self.directory_stats = {}
        
        # 还款对账明细数据（新增）
        self.reconciliation_data = []
        
        # 跟踪已处理的Excel文件，避免重复（新增）
        self.processed_excel_files = set()
        
        # 缓存：记录文件夹中的人名但数据库中找不到的情况（用于黄色标识）
        self.missing_debtor_names = set()
        
        logger.info(f"处理器初始化完成，OCR引擎: {self.ocr_engine_type}")

    def _scan_reconciliation_excel(self, folder_path: Path) -> List[Dict]:
        """
        扫描子文件夹中的还款对账明细Excel文件
        
        Args:
            folder_path: 子文件夹路径
            
        Returns:
            还款对账明细数据列表
        """
        reconciliation_records = []
        
        try:
            logger.info(f"🔍 扫描还款对账明细Excel: {folder_path.name}")
            
            # 查找Excel文件（还款对账明细）
            excel_files = []
            for pattern in ['*还款对账明细*.xlsx', '*还款对账明细*.xls', '*对账明细*.xlsx', '*对账明细*.xls']:
                found_files = list(folder_path.glob(pattern))
                # 🔧 过滤掉Excel临时文件（以~$开头）
                filtered_files = [f for f in found_files if not f.name.startswith('~$')]
                excel_files.extend(filtered_files)
            
            if not excel_files:
                logger.info(f"   ⚠️ 未找到还款对账明细Excel文件")
                return reconciliation_records
            
            for excel_file in excel_files:
                # 检查是否已处理过此Excel文件（使用完整路径避免重复）
                excel_file_key = str(excel_file.resolve())
                if excel_file_key in self.processed_excel_files:
                    logger.info(f"   ⏭️ 跳过已处理的Excel文件: {excel_file.name}")
                    continue
                
                # 标记为已处理
                self.processed_excel_files.add(excel_file_key)
                logger.info(f"   📊 读取Excel文件: {excel_file.name}")
                
                try:
                    # 读取Excel文件
                    df = pd.read_excel(excel_file, sheet_name=0)
                    
                    logger.info(f"   📋 Excel文件包含 {len(df)} 行数据")
                    
                    # 规范化列名（去除空格和特殊字符）
                    df.columns = df.columns.str.strip()
                    
                    # 可能的列名映射（根据用户要求修正：现金回收->还款金额，还款类型->类型）
                    column_mapping = {
                        '还款时间': ['还款时间', '时间', '交易时间', '支付时间'],
                        '债务人姓名': ['债务人姓名', '姓名', '债务人', '客户姓名', '还款人'],
                        '合同号': ['合同号', '合同编号', '案件号', '借款合同号'],
                        '还款金额': ['还款金额', '金额', '支付金额', '转账金额', '现金回收', '现金回收部分'],
                        '类型': ['类型', '还款类型', '还款类型（部分还款/结清）', '部分还款/结清', '支付类型', '交易类型'],
                        '身份证号': ['身份证号', '身份证', '证件号', '身份证号码']
                    }
                    
                    # 查找实际列名（修正：避免"业务类型"被误匹配为"类型"）
                    actual_columns = {}
                    for standard_name, possible_names in column_mapping.items():
                        for col in df.columns:
                            col_str = str(col).strip()
                            # 对于"类型"字段，排除"业务类型"的干扰
                            if standard_name == '类型':
                                # 精确匹配或包含特定关键字，但排除"业务类型"
                                if col_str == '业务类型':
                                    continue  # 跳过业务类型
                                if any(name in col_str for name in possible_names):
                                    actual_columns[standard_name] = col
                                    break
                            else:
                                # 其他字段使用原来的匹配逻辑
                                if any(name in col_str for name in possible_names):
                                    actual_columns[standard_name] = col
                                    break
                    
                    logger.info(f"   🔑 识别的列名映射: {actual_columns}")
                    
                    # 处理每一行数据
                    for idx, row in df.iterrows():
                        try:
                            # 提取数据
                            record = {
                                '文件夹名称': folder_path.name,
                                '还款时间': '',
                                '债务人姓名': '',
                                '合同号': '',
                                '还款金额': 0.0,
                                '类型': '',
                                '身份证号': '',
                                'debtor_no': '',
                                'collector_user_name': '',
                                'total_principal': 0.0,
                                'remaining_principal': 0.0,
                                '图片文件数量': 0,
                                'Excel文件': excel_file.name,
                                '行号': idx + 2  # Excel行号（包含标题行）
                            }
                            
                            # 填充数据
                            for standard_name, excel_col in actual_columns.items():
                                if excel_col in df.columns:
                                    value = row[excel_col]
                                    if pd.notna(value):
                                        if standard_name == '还款金额':
                                            try:
                                                record[standard_name] = float(str(value).replace(',', ''))
                                            except (ValueError, TypeError):
                                                record[standard_name] = 0.0
                                        else:
                                            record[standard_name] = str(value).strip()
                            
                            # 跳过空记录
                            if not record['债务人姓名'] and not record['合同号']:
                                continue
                            
                            # 🔍 添加调试信息
                            logger.info(f"   🔍 准备匹配债务人:")
                            logger.info(f"      债务人姓名: '{record.get('债务人姓名', '')}'")
                            logger.info(f"      合同号: '{record.get('合同号', '')}'")
                            logger.info(f"      身份证号: '{record.get('身份证号', '')}'")
                            
                            # 进行债务人匹配
                            debtor_info = self._match_debtor_info(record)
                            if debtor_info:
                                record.update(debtor_info)
                                logger.info(f"   ✅ 匹配成功: debtor_no={debtor_info.get('debtor_no')}, 方式={debtor_info.get('匹配方式')}")
                                
                                # 添加地区信息（基于催收员映射）
                                collector_region_mapping = {
                                    'TX': '吉林',
                                    'CY': '黑龙江', 
                                    'syyb': '辽宁'
                                }
                                collector_user = record.get('collector_user_name', '')
                                record['地区'] = collector_region_mapping.get(collector_user, '未知地区')
                            else:
                                logger.warning(f"   ❌ 匹配失败: {record.get('债务人姓名')}")
                                record['地区'] = '未知地区'
                            
                            # 统计该债务人的图片文件数量
                            record['图片文件数量'] = self._count_debtor_images(folder_path, record['债务人姓名'])
                            
                            reconciliation_records.append(record)
                            
                            # logger.debug(f"   ✅ 处理记录: {record['债务人姓名']} - {record['还款金额']}元")
                            
                        except Exception as e:
                            logger.warning(f"   ⚠️ 处理第{idx+2}行数据失败: {str(e)}")
                            continue
                    
                    logger.info(f"   ✅ 成功处理 {len(reconciliation_records)} 条对账明细记录")
                    
                except Exception as e:
                    logger.error(f"   ❌ 读取Excel文件失败: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"❌ 扫描还款对账明细失败: {str(e)}")
        
        return reconciliation_records

    def _match_debtor_info(self, record: Dict) -> Optional[Dict]:
        """
        智能匹配债务人信息
        
        优先级：身份证号 > 合同号 > 姓名+数字字母匹配身份证后4位
        
        Args:
            record: 还款对账明细记录
            
        Returns:
            债务人信息字典或None
        """
        if not self.debtor_db:
            logger.warning("❌ 债务人数据库未初始化")
            return None
        
        try:
            # 1. 优先使用身份证号查询
            identity_no = record.get('身份证号', '').strip()
            if identity_no and len(identity_no) >= 10:
                logger.info(f"🔍 使用身份证号查询: {identity_no}")
                debtor_info = self.debtor_db.query_debtor_info(identity_no=identity_no)
                logger.info(f"   数据库查询结果: found={debtor_info.get('found')}, debtor_no={debtor_info.get('debtor_no')}")
                if debtor_info['found']:
                    logger.info(f"✅ 通过身份证号找到债务人: {debtor_info['debtor_no']}")
                    return {
                        'debtor_no': debtor_info['debtor_no'],
                        'collector_user_name': debtor_info.get('collecter_user_name', ''),
                        'total_principal': debtor_info.get('total_principal', 0.0),
                        'remaining_principal': debtor_info.get('remaining_principal', 0.0),
                        '匹配方式': '身份证号'
                    }
                else:
                    logger.info(f"   ❌ 身份证号查询无结果: {identity_no}")
            
            # 2. 使用合同号查询
            contract_no = record.get('合同号', '').strip()
            if contract_no:
                logger.info(f"🔍 使用合同号查询: {contract_no}")
                debtor_info = self.debtor_db.query_debtor_info(contract_no=contract_no)
                logger.info(f"   数据库查询结果: found={debtor_info.get('found')}, debtor_no={debtor_info.get('debtor_no')}")
                if debtor_info['found']:
                    logger.info(f"✅ 通过合同号找到债务人: {debtor_info['debtor_no']}")
                    return {
                        'debtor_no': debtor_info['debtor_no'],
                        'collector_user_name': debtor_info.get('collecter_user_name', ''),
                        'total_principal': debtor_info.get('total_principal', 0.0),
                        'remaining_principal': debtor_info.get('remaining_principal', 0.0),
                        '匹配方式': '合同号'
                    }
                else:
                    logger.info(f"   ❌ 合同号查询无结果: {contract_no}")
            
            # 3. 使用姓名+数字字母匹配身份证后4位
            debtor_name = record.get('债务人姓名', '').strip()
            if debtor_name:
                # logger.debug(f"🔍 使用姓名模糊匹配: {debtor_name}")
                
                # 提取姓名中的数字和字母
                import re
                numbers_letters = re.findall(r'[0-9A-Za-z]+', debtor_name)
                chinese_name = re.sub(r'[0-9A-Za-z]+', '', debtor_name).strip()
                
                if numbers_letters and chinese_name:
                    # 有数字字母，可能是重名情况
                    id_suffix = ''.join(numbers_letters)
                    # logger.debug(f"🔍 重名匹配 - 中文名: {chinese_name}, 身份证后缀: {id_suffix}")
                    
                    # 通过中文名称查询所有匹配的债务人
                    name_matches = self._query_debtors_by_name(chinese_name)
                    
                    # 在匹配结果中查找身份证后4位匹配的
                    for debtor in name_matches:
                        debtor_identity = debtor.get('identity_no', '')
                        if debtor_identity and len(debtor_identity) >= 4:
                            if debtor_identity[-4:] == id_suffix[-4:]:  # 后4位匹配
                                logger.info(f"✅ 通过姓名+身份证后4位找到债务人: {debtor['debtor_no']}")
                                return {
                                    'debtor_no': debtor['debtor_no'],
                                    'collector_user_name': debtor.get('collector_user_name', ''),
                                    'total_principal': debtor.get('total_principal', 0.0),
                                    'remaining_principal': debtor.get('remaining_principal', 0.0),
                                    '匹配方式': '姓名+身份证后4位'
                                }
                else:
                    # 没有数字字母，直接姓名匹配
                    name_matches = self._query_debtors_by_name(debtor_name)
                    if len(name_matches) == 1:
                        # 唯一匹配
                        debtor = name_matches[0]
                        logger.info(f"✅ 通过姓名唯一匹配找到债务人: {debtor['debtor_no']}")
                        return {
                            'debtor_no': debtor['debtor_no'],
                            'collector_user_name': debtor.get('collector_user_name', ''),
                            'total_principal': debtor.get('total_principal', 0.0),
                            'remaining_principal': debtor.get('remaining_principal', 0.0),
                            '匹配方式': '姓名唯一匹配'
                        }
                    elif len(name_matches) > 1:
                        logger.warning(f"⚠️ 姓名'{debtor_name}'匹配到多个债务人，无法确定唯一性")
            
            logger.warning(f"❌ 未找到匹配的债务人:")
            logger.warning(f"   债务人姓名: '{record.get('债务人姓名', '')}'")
            logger.warning(f"   合同号: '{record.get('合同号', '')}'")
            logger.warning(f"   身份证号: '{record.get('身份证号', '')}'")
            return None
            
        except Exception as e:
            logger.error(f"❌ 债务人匹配失败: {str(e)}")
            return None

    def _query_debtors_by_name(self, name: str) -> List[Dict]:
        """
        通过姓名查询债务人（支持模糊匹配）
        
        Args:
            name: 债务人姓名
            
        Returns:
            匹配的债务人列表
        """
        matches = []
        
        try:
            if not self.debtor_db or not self.debtor_db.data_loaded:
                return matches
            
            if self.debtor_db.use_duckdb and self.debtor_db.conn:
                # 使用DuckDB查询
                query = "SELECT * FROM debtors WHERE name LIKE ?"
                result = self.debtor_db.conn.execute(query, [f'%{name}%']).fetchall()
                columns = [desc[0] for desc in self.debtor_db.conn.description]
                
                for row in result:
                    matches.append(dict(zip(columns, row)))
            else:
                # 使用pandas查询
                if hasattr(self.debtor_db, 'df') and self.debtor_db.df is not None:
                    mask = self.debtor_db.df['name'].str.contains(name, na=False)
                    matched_rows = self.debtor_db.df[mask]
                    matches = matched_rows.to_dict('records')
                    
        except Exception as e:
            logger.error(f"❌ 姓名查询失败: {str(e)}")
        
        return matches

    def _count_debtor_images(self, folder_path: Path, debtor_name: str) -> int:
        """
        统计指定债务人在文件夹中的图片文件数量
        
        Args:
            folder_path: 文件夹路径
            debtor_name: 债务人姓名
            
        Returns:
            图片文件数量
        """
        count = 0
        
        try:
            # 提取中文姓名（去除数字字母）
            import re
            chinese_name = re.sub(r'[0-9A-Za-z]+', '', debtor_name).strip()
            
            # 扫描文件夹中的图片文件
            for image_file in folder_path.iterdir():
                if image_file.is_file() and self.is_image_file(image_file):
                    # 检查文件名是否包含债务人姓名
                    if chinese_name in image_file.name or debtor_name in image_file.name:
                        count += 1
                        
        except Exception as e:
            logger.warning(f"⚠️ 统计图片文件数量失败: {str(e)}")
        
        return count

    def _perform_reconciliation_analysis(self) -> Dict:
        """
        执行三重比对分析
        
        返回:
            比对分析结果字典
        """
        analysis_result = {
            '姓名匹配分析': [],
            '金额匹配分析': [],
            '结清验证分析': [],
            '总体统计': {}
        }
        
        try:
            logger.info("🔍 开始执行三重比对分析...")
            
            if not self.reconciliation_data:
                logger.warning("⚠️ 没有还款对账明细数据，跳过比对分析")
                return analysis_result
            
            if not self.results:
                logger.warning("⚠️ 没有处理结果明细数据，跳过比对分析")
                return analysis_result
            
            # 1. 准备数据：按debtor_no分组处理结果明细
            ocr_results_by_debtor = {}
            for result in self.results:
                debtor_no = result.get('debtor_no', '')
                if debtor_no:
                    if debtor_no not in ocr_results_by_debtor:
                        ocr_results_by_debtor[debtor_no] = []
                    ocr_results_by_debtor[debtor_no].append(result)
            
            # 准备还款对账明细数据（按debtor_no分组）
            reconciliation_by_debtor = {}
            for record in self.reconciliation_data:
                debtor_no = record.get('debtor_no', '')
                if debtor_no:
                    if debtor_no not in reconciliation_by_debtor:
                        reconciliation_by_debtor[debtor_no] = []
                    reconciliation_by_debtor[debtor_no].append(record)
            
            logger.info(f"📊 处理结果明细包含 {len(ocr_results_by_debtor)} 个不同债务人")
            logger.info(f"📊 还款对账明细包含 {len(reconciliation_by_debtor)} 个不同债务人")
            
            # 🔍 分析1：债务人姓名匹配分析
            logger.info("🔍 执行分析1：债务人姓名匹配分析")
            all_reconciliation_debtors = set(reconciliation_by_debtor.keys())
            all_ocr_debtors = set(ocr_results_by_debtor.keys())
            
            # 在还款对账明细中但不在处理结果明细中的
            missing_in_ocr = all_reconciliation_debtors - all_ocr_debtors
            # 在处理结果明细中但不在还款对账明细中的
            missing_in_reconciliation = all_ocr_debtors - all_reconciliation_debtors
            # 两边都有的
            common_debtors = all_reconciliation_debtors & all_ocr_debtors
            
            for debtor_no in missing_in_ocr:
                records = reconciliation_by_debtor[debtor_no]
                for record in records:
                    analysis_result['姓名匹配分析'].append({
                        'debtor_no': debtor_no,
                        '债务人姓名': record.get('债务人姓名', ''),
                        '文件夹名称': record.get('文件夹名称', ''),
                        '差异类型': '仅在还款对账明细中',
                        '说明': '还款对账明细中存在但处理结果明细中缺失',
                        '需要关注': '是'
                    })
            
            for debtor_no in missing_in_reconciliation:
                # 从OCR结果中找到债务人信息
                ocr_records = ocr_results_by_debtor[debtor_no]
                first_record = ocr_records[0]
                analysis_result['姓名匹配分析'].append({
                    'debtor_no': debtor_no,
                    '债务人姓名': first_record.get('还款人', ''),
                    '文件夹名称': first_record.get('文件夹名称', ''),
                    '差异类型': '仅在处理结果明细中',
                    '说明': '处理结果明细中存在但还款对账明细中缺失',
                    '需要关注': '是'
                })
            
            # 🔍 分析2：还款金额匹配分析
            logger.info("🔍 执行分析2：还款金额匹配分析")
            for debtor_no in common_debtors:
                reconciliation_records = reconciliation_by_debtor[debtor_no]
                ocr_records = ocr_results_by_debtor[debtor_no]
                
                # 计算还款对账明细中的总金额
                reconciliation_total = sum(float(r.get('还款金额', 0)) for r in reconciliation_records)
                
                # 计算处理结果明细中的总金额
                ocr_total = 0.0
                for ocr_record in ocr_records:
                    amount_str = ocr_record.get('图片OCR识别金额', '0')
                    try:
                        # 提取数字金额
                        import re
                        numbers = re.findall(r'\d+\.?\d*', str(amount_str))
                        if numbers:
                            ocr_total += float(numbers[0])
                    except (ValueError, TypeError):
                        continue
                
                # 比较金额差异
                amount_diff = abs(reconciliation_total - ocr_total)
                tolerance = 0.01  # 1分钱的容差
                
                if amount_diff > tolerance:
                    first_reconciliation = reconciliation_records[0]
                    analysis_result['金额匹配分析'].append({
                        'debtor_no': debtor_no,
                        '债务人姓名': first_reconciliation.get('债务人姓名', ''),
                        '文件夹名称': first_reconciliation.get('文件夹名称', ''),
                        '还款对账金额': reconciliation_total,
                        'OCR识别金额': ocr_total,
                        '金额差异': amount_diff,
                        '差异类型': '金额不一致',
                        '说明': f'还款对账明细总额 ¥{reconciliation_total:.2f} vs OCR识别总额 ¥{ocr_total:.2f}',
                        '需要关注': '是'
                    })
                else:
                    first_reconciliation = reconciliation_records[0]
                    analysis_result['金额匹配分析'].append({
                        'debtor_no': debtor_no,
                        '债务人姓名': first_reconciliation.get('债务人姓名', ''),
                        '文件夹名称': first_reconciliation.get('文件夹名称', ''),
                        '还款对账金额': reconciliation_total,
                        'OCR识别金额': ocr_total,
                        '金额差异': amount_diff,
                        '差异类型': '金额一致',
                        '说明': '金额匹配正确',
                        '需要关注': '否'
                    })
            
            # 🔍 分析3：结清验证分析
            logger.info("🔍 执行分析3：结清验证分析")
            for debtor_no in common_debtors:
                reconciliation_records = reconciliation_by_debtor[debtor_no]
                
                for record in reconciliation_records:
                    payment_type = record.get('类型', '').strip()
                    payment_amount = float(record.get('还款金额', 0))
                    remaining_principal = float(record.get('remaining_principal', 0))
                    
                    # 检查结清类型且金额小于剩余本金的情况
                    if '结清' in payment_type and payment_amount < remaining_principal:
                        analysis_result['结清验证分析'].append({
                            'debtor_no': debtor_no,
                            '债务人姓名': record.get('债务人姓名', ''),
                            '文件夹名称': record.get('文件夹名称', ''),
                            '还款类型': payment_type,
                            '还款金额': payment_amount,
                            '剩余本金': remaining_principal,
                            '差异金额': remaining_principal - payment_amount,
                            '风险等级': '高',
                            '说明': '标记为结清但还款金额小于剩余本金',
                            '建议': '需要先确认是否已做还款登记',
                            '需要关注': '是'
                        })
                    elif '结清' in payment_type:
                        analysis_result['结清验证分析'].append({
                            'debtor_no': debtor_no,
                            '债务人姓名': record.get('债务人姓名', ''),
                            '文件夹名称': record.get('文件夹名称', ''),
                            '还款类型': payment_type,
                            '还款金额': payment_amount,
                            '剩余本金': remaining_principal,
                            '差异金额': remaining_principal - payment_amount,
                            '风险等级': '低',
                            '说明': '结清金额合理',
                            '建议': '正常',
                            '需要关注': '否'
                        })
            
            # 统计信息
            analysis_result['总体统计'] = {
                '还款对账明细债务人数': len(all_reconciliation_debtors),
                '处理结果明细债务人数': len(all_ocr_debtors),
                '共同债务人数': len(common_debtors),
                '仅在还款对账明细中': len(missing_in_ocr),
                '仅在处理结果明细中': len(missing_in_reconciliation),
                '金额不一致债务人数': len([x for x in analysis_result['金额匹配分析'] if x['差异类型'] == '金额不一致']),
                '需要关注的结清案例': len([x for x in analysis_result['结清验证分析'] if x['需要关注'] == '是'])
            }
            
            logger.info("✅ 三重比对分析完成")
            logger.info(f"   📊 统计结果: {analysis_result['总体统计']}")
            
        except Exception as e:
            logger.error(f"❌ 比对分析失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        
        return analysis_result

    def _find_similar_image_file(self, target_path: Path) -> Optional[Path]:
        """
        在目录中查找相似的图片文件

        Args:
            target_path: 目标文件路径

        Returns:
            相似文件的Path对象或None
        """
        try:
            if not target_path.parent.exists():
                return None

            target_name = target_path.stem.lower()  # 不包含扩展名的文件名
            target_ext = target_path.suffix.lower()
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}

            best_match = None
            best_score = 0

            for item in target_path.parent.iterdir():
                if item.is_file() and item.suffix.lower() in image_extensions:
                    item_name = item.stem.lower()

                    # 计算文件名相似度
                    score = self._calculate_name_similarity(target_name, item_name)

                    # 如果扩展名相同，加分
                    if item.suffix.lower() == target_ext:
                        score += 0.1

                    if score > best_score and score > 0.3:  # 相似度阈值
                        best_score = score
                        best_match = item

            if best_match:
                # # logger.debug(f"找到相似文件: {target_path.name} → {best_match.name} (相似度: {best_score:.2f})")
                pass

            return best_match

        except Exception as e:
            # logger.debug(f"查找相似文件失败: {e}")
            return None

    def _format_payment_time(self, time_str: str) -> str:
        """
        格式化还款时间，只保留年月日
        
        Args:
            time_str: 原始时间字符串
            
        Returns:
            格式化后的时间字符串（YYYY-MM-DD）
        """
        if not time_str:
            return ''
        
        try:
            import re
            from datetime import datetime
            
            # 去除首尾空白
            time_str = time_str.strip()
            
            # 常见时间格式的正则表达式
            patterns = [
                # 2025-07-28 15:50:39 -> 2025-07-28
                r'(\d{4}-\d{2}-\d{2})\s+\d{2}:\d{2}:\d{2}',
                # 2025/07/28 15:50:39 -> 2025-07-28
                r'(\d{4})/(\d{2})/(\d{2})\s+\d{2}:\d{2}:\d{2}',
                # 2025-07-28 -> 2025-07-28 (已经是年月日格式)
                r'^(\d{4}-\d{2}-\d{2})$',
                # 2025/07/28 -> 2025-07-28
                r'^(\d{4})/(\d{2})/(\d{2})$',
                # 07-28 15:50 -> 2025-07-28 (假设当前年份)
                r'^(\d{2})-(\d{2})\s+\d{2}:\d{2}',
                # 07/28 15:50 -> 2025-07-28 (假设当前年份)
                r'^(\d{2})/(\d{2})\s+\d{2}:\d{2}',
            ]
            
            # 尝试匹配各种格式
            for pattern in patterns:
                match = re.search(pattern, time_str)
                if match:
                    if len(match.groups()) == 1:
                        # 已经是YYYY-MM-DD格式
                        return match.group(1)
                    elif len(match.groups()) == 3:
                        # YYYY/MM/DD格式，转换为YYYY-MM-DD
                        year, month, day = match.groups()
                        return f"{year}-{month}-{day}"
                    elif len(match.groups()) == 2:
                        # MM-DD或MM/DD格式，添加当前年份
                        month, day = match.groups()
                        current_year = datetime.now().year
                        return f"{current_year}-{month}-{day}"
            
            # 如果无法匹配，尝试直接解析
            try:
                # 尝试解析常见格式
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d']:
                    try:
                        dt = datetime.strptime(time_str, fmt)
                        return dt.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
            except:
                pass
            
            logger.warning(f"⚠️ 无法格式化时间: {time_str}，保持原样")
            return time_str
            
        except Exception as e:
            logger.warning(f"⚠️ 时间格式化失败: {time_str}, 错误: {e}")
            return time_str

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """
        计算两个文件名的相似度

        Args:
            name1: 文件名1
            name2: 文件名2

        Returns:
            相似度分数 (0-1)
        """
        try:
            if name1 == name2:
                return 1.0

            if name1 in name2 or name2 in name1:
                return 0.8

            # 提取数字部分进行比较（对于包含金额的文件名很有用）
            import re
            numbers1 = re.findall(r'\d+', name1)
            numbers2 = re.findall(r'\d+', name2)

            if numbers1 and numbers2:
                # 如果有相同的数字，增加相似度
                common_numbers = set(numbers1) & set(numbers2)
                if common_numbers:
                    return 0.6 + len(common_numbers) * 0.1

            # 基于公共字符的相似度
            common_chars = set(name1) & set(name2)
            if common_chars:
                similarity = len(common_chars) / max(len(set(name1)), len(set(name2)))
                return min(similarity, 0.5)  # 最大0.5分

            return 0.0

        except Exception:
            return 0.0

    def _init_ollama_ocr(self):
        """
        初始化Ollama OCR引擎，如果失败则使用备用OCR

        Returns:
            OCR引擎实例或None
        """
        logger.info("🚀 开始初始化Ollama OCR引擎...")
        
        # 首先尝试Ollama OCR
        try:
            # logger.debug("📦 正在导入ollama_ocr库...")
            from ollama_ocr import OCRProcessor

            logger.info("✅ ollama_ocr库导入成功")
            # logger.debug("🔧 正在创建OCRProcessor实例...")

            # 初始化Ollama OCR处理器
            ocr_processor = OCRProcessor(
                model_name='llama3.2-vision:11b',
                max_workers=1
            )

            logger.info("✅ Ollama OCR引擎初始化成功")
            logger.info(f"   模型: llama3.2-vision:11b")
            logger.info(f"   工作线程: 1")
            # logger.debug(f"   OCRProcessor类型: {type(ocr_processor)}")
            
            return ocr_processor

        except ImportError as e:
            logger.error(f"❌ Ollama OCR库导入失败: {str(e)}")
            logger.error("   请运行安装命令: pip install ollama-ocr")
            logger.info("🔄 尝试使用备用OCR引擎...")

        except Exception as e:
            logger.error(f"❌ Ollama OCR引擎初始化失败: {str(e)}")
            logger.error(f"   异常类型: {type(e).__name__}")
            logger.error("   可能的原因:")
            logger.error("   1. Ollama服务未运行 (请运行: ollama serve)")
            logger.error("   2. 模型未下载 (请运行: ollama pull llama3.2-vision:11b)")
            logger.error("   3. 网络连接问题")
            logger.info("🔄 尝试使用备用OCR引擎...")
            
            # 添加更详细的调试信息
            import traceback
            # logger.debug(f"📋 完整异常堆栈:\n{traceback.format_exc()}")

        # 备用方案：使用Tesseract OCR
        try:
            # logger.debug("📦 正在检查Tesseract OCR可用性...")
            import pytesseract
            logger.warning("⚠️ 使用Tesseract OCR作为备用引擎")
            logger.warning("   注意: Tesseract OCR识别精度可能不如Ollama OCR")
            return 'tesseract'

        except ImportError:
            logger.error("❌ Tesseract OCR也未安装")
            logger.error("🔧 请安装以下OCR引擎之一:")
            logger.error("   1. Ollama OCR (推荐): pip install ollama-ocr")
            logger.error("   2. Tesseract OCR: pip install pytesseract")
            logger.error("   并确保Ollama服务正在运行: ollama serve")
            logger.error("   模型已下载: ollama pull llama3.2-vision:11b")
            return None
    
    def _init_debtor_database(self) -> Optional[DebtorDatabase]:
        """
        初始化债务人数据库
        
        Returns:
            债务人数据库实例或None
        """
        try:
            debtor_file = "民生4期债务人明细.xlsx"
            
            if not os.path.exists(debtor_file):
                logger.warning(f"债务人明细文件不存在：{debtor_file}")
                return None
            
            # 创建数据库实例
            db = DebtorDatabase(debtor_file)
            
            # 加载数据
            if db.load_data():
                # 获取统计信息
                stats = db.get_statistics()
                if stats:
                    logger.info(f"债务人数据库初始化成功：")
                    logger.info(f"  数据库类型: {stats.get('database_type', 'unknown')}")
                    logger.info(f"  总记录数: {stats.get('total_records', 0)}")
                    logger.info(f"  有身份证号: {stats.get('with_identity_no', 0)}")
                    logger.info(f"  有合同号: {stats.get('with_case_no', 0)}")
                
                return db
            else:
                logger.error("债务人数据库数据加载失败")
                return None
                
        except Exception as e:
            logger.error(f"初始化债务人数据库失败：{str(e)}")
            return None
    
    def _select_ocr_engine(self, engine: str) -> str:
        """
        选择OCR引擎 - 更新版：豆包优先，Tesseract次之
        
        Args:
            engine: 用户指定的引擎类型
            
        Returns:
            选择的引擎类型
        """
        if engine.lower() == 'auto':
            # 自动选择：优先级 豆包Seed-1.6 > Tesseract > PaddleOCR > EasyOCR > Ollama
            logger.info("🤖 自动选择OCR引擎...")

            # 1. 优先尝试豆包Seed-1.6（AI视觉理解，最优中文识别）
            if DOUBAO_AVAILABLE:
                logger.info("✅ 检测到豆包Seed-1.6 API，优先使用（AI视觉理解，最优中文识别）")
                return 'doubao'
            else:
                logger.info("⚠️ 豆包Seed-1.6依赖未安装，跳过")

            # 2. 尝试Tesseract（成熟稳定，中文识别经过验证）
            try:
                import pytesseract
                logger.info("✅ 检测到Tesseract OCR，次优选择（成熟稳定的OCR引擎）")
                return 'tesseract'
            except ImportError:
                logger.info("⚠️ Tesseract OCR未安装，跳过")

            # 3. 尝试PaddleOCR（备用方案）
            try:
                from paddleocr import PaddleOCR
                logger.info("✅ 检测到PaddleOCR（备用方案）")
                return 'paddleocr'
            except ImportError:
                logger.info("⚠️ PaddleOCR未安装，跳过")

            # 4. 尝试EasyOCR
            try:
                import easyocr
                logger.info("✅ 检测到EasyOCR（备用方案）")
                return 'easyocr'
            except ImportError:
                logger.info("⚠️ EasyOCR未安装，跳过")

            # 5. 最后尝试Ollama OCR（性能较慢，作为最后选择）
            try:
                from ollama_ocr import OCRProcessor
                logger.info("✅ 检测到Ollama OCR（备用方案）")
                return 'ollama'
            except ImportError:
                logger.error("❌ 未找到任何可用的OCR引擎！")
                raise ImportError("请安装OCR引擎: pip install openai pytesseract")
        
        elif engine.lower() in ['doubao', 'doubao-seed-1.6']:
            return 'doubao'
        elif engine.lower() == 'tesseract':
            return 'tesseract'
        elif engine.lower() in ['paddleocr', 'paddle']:
            return 'paddleocr'
        elif engine.lower() == 'ollama':
            return 'ollama' 
        elif engine.lower() in ['easyocr', 'easy']:
            return 'easyocr'
        elif engine.lower() == 'none':
            return 'none'
        else:
            logger.warning(f"⚠️ 未知的OCR引擎: {engine}，使用自动选择")
            return self._select_ocr_engine('auto')

    def get_ocr_engine_status(self) -> Dict:
        """
        获取当前OCR引擎的详细状态信息
        
        Returns:
            OCR引擎状态字典
        """
        status = {
            'engine_type': getattr(self, 'ocr_engine_type', 'unknown'),
            'is_initialized': hasattr(self, 'ocr') and self.ocr is not None,
            'supports_chinese': False,
            'estimated_accuracy': 'unknown',
            'recommended_settings': {},
            'performance_metrics': {}
        }
        
        if status['engine_type'] == 'tesseract':
            status['supports_chinese'] = self._check_tesseract_chinese_support()
            status['estimated_accuracy'] = 'high' if status['supports_chinese'] else 'medium'
            status['recommended_settings'] = {
                'psm': '6 (uniform text block)',
                'oem': '3 (default)',
                'lang': 'chi_sim+eng' if status['supports_chinese'] else 'eng'
            }
        elif status['engine_type'] == 'paddleocr':
            status['supports_chinese'] = True
            status['estimated_accuracy'] = 'high'
            status['recommended_settings'] = {
                'lang': 'ch',
                'use_gpu': False,
                'precision': 'fp32'
            }
        elif status['engine_type'] == 'easyocr':
            status['supports_chinese'] = True
            status['estimated_accuracy'] = 'medium'
        elif status['engine_type'] == 'ollama':
            status['supports_chinese'] = True
            status['estimated_accuracy'] = 'high'
            status['recommended_settings'] = {
                'model': 'llama3.2-vision:11b',
                'max_workers': 1
            }
        
        return status

    def _check_tesseract_chinese_support(self) -> bool:
        """
        检查Tesseract是否支持中文识别
        
        Returns:
            是否支持中文
        """
        try:
            import pytesseract
            available_langs = pytesseract.get_languages()
            return 'chi_sim' in available_langs
        except Exception:
            return False

    def validate_ocr_vs_filename(self, ocr_result: Dict, filename_result: Dict, file_path: str) -> Dict:
        """
        验证OCR识别结果与文件名解析结果的一致性
        
        Args:
            ocr_result: OCR识别结果字典
            filename_result: 文件名解析结果字典  
            file_path: 文件路径
            
        Returns:
            验证结果字典
        """
        validation = {
            'status': '一致',  # 一致/不一致/部分一致/无法比较
            'confidence': 1.0,  # 匹配置信度 0-1
            'details': [],
            'needs_highlight': False,  # 是否需要在Excel中标红
            'accuracy_assessment': '优秀'  # 优秀/良好/一般/差
        }
        
        try:
            # 1. 姓名比较
            ocr_name = ocr_result.get('payer', '').strip()
            filename_name = filename_result.get('name', '').strip()
            
            name_match = self._compare_names(ocr_name, filename_name)
            
            # 2. 金额比较  
            ocr_amount = ocr_result.get('payment_amount', '').strip()
            filename_amount = filename_result.get('amount', '').strip()
            
            amount_match = self._compare_amounts(ocr_amount, filename_amount)
            
            # 3. 综合评估
            total_score = 0
            max_score = 0
            
            # 姓名权重：60%
            if ocr_name and filename_name:
                total_score += name_match['score'] * 0.6
                max_score += 0.6
                validation['details'].append(f"姓名匹配: OCR[{ocr_name}] vs 文件名[{filename_name}] = {name_match['score']:.2f}")
                
                if name_match['score'] < 0.5:
                    validation['details'].append(f"⚠️ 姓名不匹配: {name_match['reason']}")
            
            # 金额权重：40%
            if ocr_amount and filename_amount:
                total_score += amount_match['score'] * 0.4
                max_score += 0.4
                validation['details'].append(f"金额匹配: OCR[{ocr_amount}] vs 文件名[{filename_amount}] = {amount_match['score']:.2f}")
                
                if amount_match['score'] < 0.5:
                    validation['details'].append(f"⚠️ 金额不匹配: {amount_match['reason']}")
            
            # 计算最终置信度
            if max_score > 0:
                validation['confidence'] = total_score / max_score
            else:
                validation['confidence'] = 0.0
                validation['status'] = '无法比较'
                validation['details'].append("❌ 缺少可比较的数据")
            
            # 设置状态
            if validation['confidence'] >= 0.8:
                validation['status'] = '一致'
                validation['accuracy_assessment'] = '优秀'
            elif validation['confidence'] >= 0.6:
                validation['status'] = '部分一致'  
                validation['accuracy_assessment'] = '良好'
                validation['needs_highlight'] = True
            elif validation['confidence'] >= 0.3:
                validation['status'] = '不一致'
                validation['accuracy_assessment'] = '一般'
                validation['needs_highlight'] = True
            else:
                validation['status'] = '不一致'
                validation['accuracy_assessment'] = '差'
                validation['needs_highlight'] = True
            
            # 记录详细日志
            if validation['needs_highlight']:
                logger.warning(f"❌ OCR与文件名不匹配: {file_path}")
                logger.warning(f"   状态: {validation['status']}, 置信度: {validation['confidence']:.2f}")
                for detail in validation['details']:
                    logger.warning(f"   {detail}")
            else:
                pass
                # logger.debug(f"✅ OCR与文件名匹配: {file_path} (置信度: {validation['confidence']:.2f})")
                
        except Exception as e:
            validation['status'] = '验证失败'
            validation['confidence'] = 0.0
            validation['details'].append(f"验证过程出错: {str(e)}")
            validation['needs_highlight'] = True
            validation['accuracy_assessment'] = '错误'
            logger.error(f"OCR文件名验证失败: {file_path}, 错误: {str(e)}")
        
        return validation

    def _compare_names(self, ocr_name: str, filename_name: str) -> Dict:
        """
        比较OCR识别的姓名与文件名中的姓名
        
        Args:
            ocr_name: OCR识别的姓名
            filename_name: 文件名解析的姓名
            
        Returns:
            比较结果字典
        """
        result = {'score': 0.0, 'reason': ''}
        
        if not ocr_name or not filename_name:
            result['reason'] = '缺少姓名数据'
            return result
        
        # 去除特殊字符进行比较
        ocr_clean = re.sub(r'[*\s]', '', ocr_name)
        filename_clean = re.sub(r'[*\s]', '', filename_name)
        
        # 1. 完全匹配
        if ocr_clean == filename_clean:
            result['score'] = 1.0
            result['reason'] = '完全匹配'
            return result
        
        # 2. 处理星号遮挡情况
        if '*' in ocr_name or '*' in filename_name:
            # 将*替换为.进行正则匹配
            ocr_pattern = ocr_name.replace('*', '.')
            filename_pattern = filename_name.replace('*', '.')
            
            if re.match(ocr_pattern, filename_name) or re.match(filename_pattern, ocr_name):
                result['score'] = 0.8
                result['reason'] = '星号遮挡匹配'
                return result
        
        # 3. 包含关系
        if ocr_clean in filename_clean or filename_clean in ocr_clean:
            result['score'] = 0.7
            result['reason'] = '包含匹配'
            return result
        
        # 4. 字符相似度
        from difflib import SequenceMatcher
        similarity = SequenceMatcher(None, ocr_clean, filename_clean).ratio()
        
        if similarity >= 0.6:
            result['score'] = similarity * 0.6  # 相似度匹配最高0.6分
            result['reason'] = f'相似度匹配({similarity:.2f})'
        else:
            result['score'] = 0.0
            result['reason'] = f'不匹配(相似度仅{similarity:.2f})'
        
        return result

    def _compare_amounts(self, ocr_amount: str, filename_amount: str) -> Dict:
        """
        比较OCR识别的金额与文件名中的金额
        
        Args:
            ocr_amount: OCR识别的金额
            filename_amount: 文件名解析的金额
            
        Returns:
            比较结果字典
        """
        result = {'score': 0.0, 'reason': ''}
        
        if not ocr_amount or not filename_amount:
            result['reason'] = '缺少金额数据'
            return result
        
        try:
            # 清理金额字符串
            ocr_clean = re.sub(r'[,，\s]', '', ocr_amount)
            filename_clean = re.sub(r'[,，\s]', '', filename_amount)
            
            ocr_float = float(ocr_clean)
            filename_float = float(filename_clean)
            
            # 完全匹配
            if abs(ocr_float - filename_float) < 0.01:
                result['score'] = 1.0
                result['reason'] = '完全匹配'
            # 允许小幅度差异（可能是小数点识别问题）
            elif abs(ocr_float - filename_float) / max(ocr_float, filename_float) < 0.05:
                result['score'] = 0.9
                result['reason'] = '近似匹配'
            # 数量级匹配（可能是小数点位置问题）
            elif (abs(ocr_float * 10 - filename_float) < 0.01 or 
                  abs(ocr_float - filename_float * 10) < 0.01 or
                  abs(ocr_float * 100 - filename_float) < 0.01 or
                  abs(ocr_float - filename_float * 100) < 0.01):
                result['score'] = 0.7
                result['reason'] = '数量级匹配'
            else:
                difference_ratio = abs(ocr_float - filename_float) / max(ocr_float, filename_float)
                result['score'] = max(0.0, 1.0 - difference_ratio)
                result['reason'] = f'数值差异({difference_ratio:.2%})'
                
        except (ValueError, TypeError) as e:
            result['score'] = 0.0
            result['reason'] = f'金额格式错误: {str(e)}'
        
        return result

    def _init_ocr_engine(self, engine: str):
        """
        初始化OCR引擎
        
        Args:
            engine: OCR引擎类型
            
        Returns:
            OCR引擎实例或None
        """
        selected_engine = self._select_ocr_engine(engine)
        
        if selected_engine == 'doubao':
            doubao_instance = self._init_doubao_ocr()
            if doubao_instance:
                self.ocr_engine_type = 'doubao'
                logger.info("🎯 当前OCR引擎: 豆包Seed-1.6 AI视觉理解 (最优中文识别)")
                return doubao_instance
            else:
                logger.warning("⚠️ 豆包Seed-1.6初始化失败，尝试其他引擎...")
                # 回退到下一个可用引擎
                if engine.lower() == 'auto':
                    # 尝试Tesseract
                    try:
                        import pytesseract
                        if self._init_tesseract():
                            self.ocr_engine_type = 'tesseract'
                            logger.info("🎯 当前OCR引擎: Tesseract OCR (备用)")
                            return None
                    except ImportError:
                        pass
                    raise RuntimeError("所有OCR引擎初始化失败")
                else:
                    raise RuntimeError("豆包Seed-1.6初始化失败")
        
        elif selected_engine == 'tesseract':
            if self._init_tesseract():
                self.ocr_engine_type = 'tesseract'
                logger.info("🎯 当前OCR引擎: Tesseract OCR (成熟稳定，高识别率)")
                return None  # Tesseract使用函数调用，不需要实例
            else:
                logger.warning("⚠️ Tesseract OCR初始化失败，尝试其他引擎...")
                # 回退到下一个可用引擎
                if engine.lower() == 'auto':
                    # 尝试PaddleOCR
                    try:
                        from paddleocr import PaddleOCR
                        if self._init_paddleocr():
                            self.ocr_engine_type = 'paddleocr'
                            logger.info("🎯 当前OCR引擎: PaddleOCR (备用)")
                            return self.paddle_ocr
                    except ImportError:
                        pass

                    # 尝试EasyOCR
                    try:
                        import easyocr
                        if self._init_easyocr():
                            self.ocr_engine_type = 'easyocr'
                            logger.info("🎯 当前OCR引擎: EasyOCR (备用)")
                            return self.easyocr_reader
                    except ImportError:
                        pass

                    # 最后尝试Ollama OCR
                    try:
                        from ollama_ocr import OCRProcessor
                        ocr_instance = self._init_ollama_ocr()
                        if ocr_instance:
                            self.ocr_engine_type = 'ollama'
                            logger.info("🎯 当前OCR引擎: Ollama OCR (备用)")
                            return ocr_instance
                    except ImportError:
                        pass

                    raise RuntimeError("所有OCR引擎初始化失败")
                else:
                    raise RuntimeError("Tesseract OCR初始化失败")

        elif selected_engine == 'paddleocr':
            if self._init_paddleocr():
                self.ocr_engine_type = 'paddleocr'
                logger.info("🎯 当前OCR引擎: PaddleOCR (备用方案)")
                return self.paddle_ocr
            else:
                raise RuntimeError("PaddleOCR初始化失败")
        
        elif selected_engine == 'ollama':
            ocr_instance = self._init_ollama_ocr()
            if ocr_instance:
                self.ocr_engine_type = 'ollama'
                logger.info("🎯 当前OCR引擎: Ollama OCR")
                return ocr_instance
            else:
                raise RuntimeError("Ollama OCR初始化失败")
        
        elif selected_engine == 'easyocr':
            if self._init_easyocr():
                self.ocr_engine_type = 'easyocr'
                logger.info("🎯 当前OCR引擎: EasyOCR")
                return self.easyocr_reader
            else:
                raise RuntimeError("EasyOCR初始化失败")
        
        elif selected_engine == 'none':
            self.ocr_engine_type = 'none'
            logger.info("🎯 当前OCR引擎: 已禁用 (仅使用文件名解析)")
            return None
        
        else:
            raise ValueError(f"不支持的OCR引擎: {selected_engine}")

    def parse_filename(self, filename: str) -> Dict[str, str]:
        """
        解析文件名获取姓名、身份证后4位、金额
        
        格式1：姓名身份证号后4位-金额 (如：张三1234-5000.jpg)
        格式2：姓名金额 (如：李四3000.jpg)
        
        Args:
            filename: 文件名（不含扩展名）
            
        Returns:
            包含解析结果的字典
        """
        result = {
            'name': '',
            'id_last4': '',
            'amount': '',
            'parse_success': False
        }
        
        try:
            # 格式1：姓名身份证号后4位-金额
            if '-' in filename:
                parts = filename.split('-', 1)  # 只分割第一个"-"
                if len(parts) == 2:
                    name_id_part = parts[0].strip()
                    amount_part = parts[1].strip()
                    
                    # 提取金额（数字部分，包括小数）
                    amount_match = re.search(r'(\d+(?:\.\d+)?)', amount_part)
                    if amount_match:
                        result['amount'] = amount_match.group(1)
                    
                    # 从姓名身份证部分提取姓名和身份证后4位
                    # 先提取中文字符作为姓名
                    name_match = re.search(r'([\u4e00-\u9fff]+)', name_id_part)
                    if name_match:
                        result['name'] = name_match.group(1)
                    
                    # 提取身份证后4位（姓名之后的4位数字或包含X的后4位）
                    if result['name']:
                        # 移除姓名后，查找剩余的身份证信息
                        remaining_part = name_id_part.replace(result['name'], '')
                        # 查找4位数字或3位数字+X的组合
                        id_match = re.search(r'(\d{3}[0-9X]|\d{4})', remaining_part)
                        if id_match:
                            result['id_last4'] = id_match.group(1)
                    
                    result['parse_success'] = True
            else:
                # 格式2：姓名金额（如：李四3000.jpg）
                # 先提取中文姓名
                name_match = re.search(r'([\u4e00-\u9fff]+)', filename)
                if name_match:
                    result['name'] = name_match.group(1)
                
                # 提取金额（查找数字，优先选择较大的数字作为金额）
                amount_matches = re.findall(r'(\d+(?:\.\d+)?)', filename)
                if amount_matches:
                    # 如果有多个数字，选择最长的作为金额
                    result['amount'] = max(amount_matches, key=len)
                
                if result['name'] and result['amount']:
                    result['parse_success'] = True
                    
        except Exception as e:
            logger.error(f"解析文件名失败: {filename}, 错误: {str(e)}")
            
        return result

    def extract_archive(self, archive_path: Path) -> bool:
        """
        解压压缩文件
        
        Args:
            archive_path: 压缩文件路径
            
        Returns:
            是否解压成功
        """
        try:
            extract_dir = archive_path.parent / archive_path.stem
            
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            elif archive_path.suffix.lower() == '.rar':
                with rarfile.RarFile(archive_path, 'r') as rar_ref:
                    rar_ref.extractall(extract_dir)
            elif archive_path.suffix.lower() == '.7z':
                with py7zr.SevenZipFile(archive_path, mode='r') as z:
                    z.extractall(extract_dir)
            
            # 解压成功后删除原压缩文件
            archive_path.unlink()
            logger.info(f"成功解压并删除: {archive_path}")
            return True
            
        except Exception as e:
            logger.error(f"解压失败: {archive_path}, 错误: {str(e)}")
            return False

    def is_image_file(self, file_path: Path) -> bool:
        """判断是否为图片文件"""
        return file_path.suffix.lower() in self.image_extensions

    def is_archive_file(self, file_path: Path) -> bool:
        """判断是否为压缩文件"""
        return file_path.suffix.lower() in self.archive_extensions

    def ocr_extract_info(self, image_path: Path) -> Dict[str, str]:
        """
        从图片中提取付款方和账户信息 - 支持多OCR引擎，优化豆包AI

        Args:
            image_path: 图片路径

        Returns:
            提取的信息字典
        """
        logger.info(f"🔍 开始OCR识别图片: {image_path.name}")
        
        # 检查是否禁用了OCR
        if hasattr(self, 'ocr_engine_type') and self.ocr_engine_type == 'none':
            logger.info("⚠️ OCR引擎已禁用，跳过OCR识别")
            return {
                'payer': '未知',
                'account_last4': '0000',
                'payment_source': '',
                'ocr_success': False
            }
        
        # 根据当前OCR引擎类型调用相应的识别方法
        if hasattr(self, 'ocr_engine_type'):
            if self.ocr_engine_type == 'doubao':
                # 使用豆包Seed-1.6 AI视觉理解
                return self._perform_doubao_ocr(image_path)
            elif self.ocr_engine_type == 'paddleocr':
                ocr_text = self._perform_paddleocr(image_path)
            elif self.ocr_engine_type == 'tesseract':
                ocr_text = self._perform_tesseract(image_path)
            elif self.ocr_engine_type == 'ollama':
                ocr_text = self._perform_ollama_ocr(image_path)
            elif self.ocr_engine_type == 'easyocr':
                ocr_text = self._perform_easyocr(image_path)
            else:
                logger.warning(f"⚠️ 未知的OCR引擎类型: {self.ocr_engine_type}，尝试使用PaddleOCR")
                ocr_text = self._perform_paddleocr(image_path)
        else:
            logger.warning("⚠️ OCR引擎未初始化，尝试使用PaddleOCR")
            ocr_text = self._perform_paddleocr(image_path)

        if not ocr_text or len(ocr_text.strip()) < 5:
            logger.warning(f"❌ {getattr(self, 'ocr_engine_type', 'Unknown')} OCR未识别到任何文本: {image_path.name}")
            return {
                'payer': '未知',
                'account_last4': '0000',
                'payment_source': '',
                'ocr_success': False
            }

        logger.info(f"✅ {getattr(self, 'ocr_engine_type', 'Unknown')} OCR识别成功，文本长度: {len(ocr_text)} 字符")
        
        # 提取付款方信息
        logger.info("🔍 开始提取付款方信息...")
        payer_name = self._extract_payer_info(ocr_text)
        
        if payer_name and payer_name != '未知':
            logger.info(f"✅ 成功识别付款方: {payer_name}")
        else:
            logger.warning("❌ 未识别到有效的付款方信息")
            # 从文件名中提取姓名作为backup
            filename_without_ext = image_path.stem
            parsed_filename = self.parse_filename(filename_without_ext)
            if parsed_filename.get('name'):
                payer_name = parsed_filename['name']
                logger.info(f"🔄 使用文件名中的姓名: {payer_name}")
            else:
                payer_name = '未知'  # 最后fallback
        
        # 提取账户号码
        logger.info("🔍 开始提取账户号码...")
        account_last4 = self._extract_account_last4(ocr_text)
        
        if account_last4 and account_last4 != '0000':
            logger.info(f"✅ 成功识别账户后4位: {account_last4}")
        else:
            logger.warning("❌ 未找到有效账户信息，使用默认值: 0000")
            account_last4 = '0000'  # 按要求，未识别到时设置为"0000"

        # 识别代还关键词
        logger.info("🔍 开始识别代还关键词...")
        payment_source = self._extract_payment_source_from_remark(ocr_text)
        
        # 提取还款时间
        logger.info("🔍 开始提取还款时间...")
        payment_time = self._extract_payment_time(ocr_text)
        if payment_time:
            logger.info(f"⏰ 成功提取还款时间: {payment_time}")
        else:
            pass
            # logger.debug("⏰ 未能提取到还款时间")
        
        # 提取还款金额
        logger.info("🔍 开始提取还款金额...")
        payment_amount = self._extract_payment_amount(ocr_text)
        if payment_amount:
            logger.info(f"💰 成功提取还款金额: {payment_amount}")
        else:
            pass
            # logger.debug("💰 未能提取到还款金额")
        
        # 判断OCR是否成功
        ocr_success = bool(ocr_text and len(ocr_text.strip()) >= 5)
        
        result = {
            'payer': payer_name or '未知',
            'account_last4': account_last4 or '0000',
            'payment_source': payment_source or '',
            'payment_time': payment_time or '',
            'payment_amount': payment_amount or '',
            'ocr_success': ocr_success
        }
        
        logger.info(f"📋 OCR识别结果总结 - 付款方: {result['payer']}, 账户: ***{result['account_last4']}, 金额: {result['payment_amount']}, 代还: {result['payment_source']}, 时间: {result['payment_time']}, 成功: {ocr_success}")
        
        return result

    def _extract_account_last4(self, text: str) -> str:
        """
        从文本中提取付款账户号码的后4位（排除收款账户1284）- 增强版
        
        Args:
            text: OCR识别的文本
            
        Returns:
            付款账户号码后4位，未找到返回空字符串
        """
        # logger.debug("🔍 开始账户号码提取流程...")
        
        # 收集所有可能的账户号码
        all_candidates = []
        
        # 1. 基于关键词查找账户（高优先级）
        # logger.debug("📍 第1步: 基于关键词查找账户...")
        keyword_candidates = self._find_accounts_by_keywords(text)
        all_candidates.extend(keyword_candidates)
        # logger.debug(f"   找到 {len(keyword_candidates)} 个关键词匹配候选")
        
        # 2. 通用数字匹配（作为备选）
        # logger.debug("📍 第2步: 通用数字模式匹配...")
        generic_candidates = self._find_accounts_by_numbers(text)
        all_candidates.extend(generic_candidates)
        # logger.debug(f"   找到 {len(generic_candidates)} 个数字匹配候选")
        
        # 3. 过滤和排序候选账户
        # logger.debug("📍 第3步: 过滤和排序候选账户...")
        filtered_candidates = self._filter_and_rank_accounts(all_candidates)
        
        # 4. 返回最佳候选账户
        if filtered_candidates:
            best_account = filtered_candidates[0]
            logger.info(f"✅ 选择最优账户: ***{best_account['last4']} (来源: {best_account['source']}, 优先级: {best_account['priority']})")
            return best_account['last4']
        
        logger.warning("❌ 未找到任何有效的付款账户")
        return ""

    def _extract_payment_amount(self, text: str) -> str:
        """
        从OCR识别的文本中提取还款金额（增强版：解决错误识别问题）
        
        Args:
            text: OCR识别的完整文本
            
        Returns:
            还款金额字符串
        """
        try:
            # logger.debug(f"🔍 开始金额提取，文本长度: {len(text)}")
            
            # 金额关键词（按重要性排序）
            amount_keywords = [
                '转账成功', '转账金额', '交易金额', '还款金额', '付款金额', 
                '汇款金额', '金额', '转账', '支付金额', '成功金额', '实际到账',
                '到账金额', '收款金额', '入账金额'
            ]
            
            # 增强的金额格式模式（解决错误识别问题）
            amount_patterns = [
                # 最高优先级：转账成功相关的金额（增强版）
                (r'转账成功[^0-9]*?[-−]?\s*[¥￥]?\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 0.1),
                (r'转账成功[^0-9]*?(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 0.2),
                
                # 高优先级：带负号的金额（表示转出）- 增强验证
                (r'[-−]\s*[¥￥]?\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 0.5),
                (r'[-−]\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 0.6),
                
                # 高优先级：货币符号开头的金额
                (r'[¥￥]\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 1.0),
                
                # 高优先级：特定关键词后的金额（更严格的验证）
                (r'(?:转账金额|交易金额|还款金额|付款金额|汇款金额|收款金额|实际到账)[：:\s]*[-−]?\s*[¥￥]?\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 1.2),
                
                # 中优先级：数字后跟元/人民币
                (r'(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*(?:元|人民币)', 2.0),
                
                # 中优先级：交易明细中的金额
                (r'(?:交易明细|明细|详情)[^0-9]*?(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 2.5),
                
                # 较低优先级：金额关键词后的数字（更严格）
                (r'金额[：:\s]*[-−]?\s*[¥￥]?\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 3.0),
                
                # 较低优先级：余额变动金额
                (r'(?:余额|变动|支出|转出)[^0-9]*?[-−]?\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', 4.0),
            ]
            
            # 收集所有匹配的金额候选
            amount_candidates = []
            
            # 先检查是否有"转账成功"关键词
            if '转账成功' in text:
                # logger.debug("🎯 发现'转账成功'关键词，优先查找附近的金额")
                # 找到"转账成功"的位置
                success_pos = text.find('转账成功')
                # 提取"转账成功"前后100个字符的上下文
                start_pos = max(0, success_pos - 50)
                context = text[start_pos:success_pos + 200]
                
                # 在这个上下文中查找金额
                # 查找负号开头的金额
                neg_amount_match = re.search(r'[-−]\s*[¥￥]?\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', context)
                if neg_amount_match:
                    amount_str = neg_amount_match.group(1)
                    if self._is_valid_amount(amount_str):
                        logger.info(f"✅ 在'转账成功'附近找到负数金额: {amount_str}")
                        return amount_str
                
                # 查找其他格式的金额
                other_amount_match = re.search(r'[¥￥]?\s*(\d{2,10}(?:[,，]\d{3})*(?:\.\d{1,2})?)', context)
                if other_amount_match:
                    amount_str = other_amount_match.group(1)
                    if self._is_valid_amount(amount_str):
                        logger.info(f"✅ 在'转账成功'附近找到金额: {amount_str}")
                        return amount_str
            
            # 按优先级搜索金额模式
            for pattern, priority in amount_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    amount_str = match if isinstance(match, str) else match[0]
                    
                    # 使用增强的金额验证
                    if self._is_valid_amount(amount_str):
                        # 清理金额字符串
                        cleaned_amount = amount_str.replace(',', '').replace('，', '')
                        amount_float = float(cleaned_amount)
                        
                        amount_candidates.append({
                            'amount': cleaned_amount,
                            'priority': priority,
                            'value': amount_float,
                            'original': amount_str
                        })
                        # logger.debug(f"💰 找到金额候选: {cleaned_amount} (优先级: {priority})")
                    else:
                        pass
                        # logger.debug(f"❌ 跳过无效金额: {amount_str}")
            
            # 按优先级排序，如果优先级相同则按金额大小排序
            if amount_candidates:
                amount_candidates.sort(key=lambda x: (x['priority'], -x['value']))
                best_amount = amount_candidates[0]['amount']
                # logger.debug(f"选择最优金额: {best_amount}")
                return best_amount
            
            # logger.debug("未能从文本中提取到金额信息")
            return ''
            
        except Exception as e:
            logger.error(f"提取还款金额失败: {str(e)}")
            return ''

    def _is_valid_amount(self, amount_str: str) -> bool:
        """
        验证金额是否有效（解决错误识别问题）
        
        Args:
            amount_str: 金额字符串
            
        Returns:
            是否为有效金额
        """
        try:
            if not amount_str or not amount_str.strip():
                return False
            
            # 清理金额字符串
            cleaned = amount_str.replace(',', '').replace('，', '').strip()
            
            # 检查是否只包含数字和小数点
            if not re.match(r'^\d+(?:\.\d{1,2})?$', cleaned):
                # logger.debug(f"金额格式无效: {amount_str}")
                return False
            
            # 转换为浮点数
            amount_float = float(cleaned)
            
            # 基本范围检查（1元到1000万）
            if amount_float < 1.0 or amount_float > 10000000:
                # logger.debug(f"金额超出合理范围: {amount_float}")
                return False
            
            # 过滤明显错误的识别结果
            invalid_patterns = [
                # 单个数字或过短的数字
                r'^[0-9]$',           # 单个数字如 "1", "2"
                r'^0+$',              # 全零如 "00", "000"  
                r'^[0-9]{1,2}$',      # 1-2位数字（可能是误识别）
                
                # 特定的错误识别模式
                r'^621\d{3}$',        # 银行卡号片段如 "621768"
                r'^1234\d*$',         # 测试数字
                r'^999\d*$',          # 异常数字
                
                # 重复数字模式
                r'^(\d)\1{3,}$',      # 4个或以上重复数字如 "1111", "5555"
                
                # 可疑的小数
                r'^\d+\.0+$',         # 如 "123.00" 但值很小
            ]
            
            for pattern in invalid_patterns:
                if re.match(pattern, cleaned):
                    # logger.debug(f"金额匹配无效模式 '{pattern}': {amount_str}")
                    return False
            
            # 特殊验证：小金额需要额外确认
            if amount_float < 10:
                # 小于10元的金额需要更严格的验证
                # 必须有小数位或者在特定上下文中
                if '.' not in cleaned:
                    # logger.debug(f"小金额缺少小数位验证: {amount_float}")
                    return False
            
            # 验证通过
            # logger.debug(f"金额验证通过: {amount_str} -> {amount_float}")
            return True
            
        except (ValueError, TypeError) as e:
            # logger.debug(f"金额验证异常: {amount_str}, 错误: {e}")
            return False

    def _extract_payment_time(self, text: str) -> str:
        """
        从OCR识别的文本中提取还款时间（只保留日期部分）
        
        Args:
            text: OCR识别的完整文本
            
        Returns:
            还款时间字符串，格式：YYYY-MM-DD 或 空字符串
        """
        try:
            # 时间关键词
            time_keywords = [
                '交易时间', '转账时间', '还款时间', '付款时间', 
                '汇款时间', '时间', '日期', '交易日期'
            ]
            
            # 时间格式模式（按优先级排序）
            time_patterns = [
                # 带时分秒的完整格式
                r'(\d{4}[/\-年]\d{1,2}[/\-月]\d{1,2})[日]?\s*\d{1,2}:\d{2}:\d{2}',
                r'(\d{4}[/\-年]\d{1,2}[/\-月]\d{1,2})[日]?\s+\d{1,2}:\d{2}:\d{2}',
                r'(\d{4}年\d{1,2}月\d{1,2}日)\s*\d{1,2}:\d{2}',
                # 只有日期的格式
                r'(\d{4}[/\-年]\d{1,2}[/\-月]\d{1,2})[日]?',
                r'(\d{4}年\d{1,2}月\d{1,2}日)',
            ]
            
            # 先尝试根据关键词查找时间
            for keyword in time_keywords:
                # 查找关键词
                keyword_pos = text.find(keyword)
                if keyword_pos != -1:
                    # 提取关键词后面的文本（限制在100个字符内）
                    context = text[keyword_pos:keyword_pos + 100]
                    
                    # 尝试匹配时间格式
                    for pattern in time_patterns:
                        match = re.search(pattern, context)
                        if match:
                            date_str = match.group(1)
                            # 标准化日期格式为YYYY-MM-DD
                            date_str = date_str.replace('年', '-').replace('月', '-').replace('日', '').replace('/', '-')
                            # 确保格式为YYYY-MM-DD
                            parts = date_str.split('-')
                            if len(parts) == 3:
                                year = parts[0].zfill(4)
                                month = parts[1].zfill(2)
                                day = parts[2].zfill(2)
                                formatted_date = f"{year}-{month}-{day}"
                                # logger.debug(f"根据关键词'{keyword}'找到日期: {formatted_date}")
                                return formatted_date
            
            # 如果关键词方法失败，尝试全文搜索
            for pattern in time_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    # 返回第一个匹配的日期
                    date_str = matches[0]
                    date_str = date_str.replace('年', '-').replace('月', '-').replace('日', '').replace('/', '-')
                    # 确保格式为YYYY-MM-DD
                    parts = date_str.split('-')
                    if len(parts) == 3:
                        year = parts[0].zfill(4)
                        month = parts[1].zfill(2)
                        day = parts[2].zfill(2)
                        formatted_date = f"{year}-{month}-{day}"
                        # logger.debug(f"全文搜索找到日期: {formatted_date}")
                        return formatted_date
            
            # logger.debug("未能从文本中提取到日期信息")
            return ''
            
        except Exception as e:
            logger.error(f"提取还款时间失败: {str(e)}")
            return ''

    def _extract_payment_source_from_remark(self, text: str) -> str:
        """
        从OCR识别的文本中提取附言信息，判断是否为代还

        Args:
            text: OCR识别的完整文本

        Returns:
            还款来源：'他人代还' 或 '' (空字符串表示未识别到代还关键词)
        """
        try:
            # 代还关键词模式列表（按优先级排序）
            proxy_payment_patterns = [
                # 明确的代还表述
                r'代\s*[\u4e00-\u9fff]{1,4}\s*还款',  # 代XXX还款
                r'代\s*[\u4e00-\u9fff]{1,4}\s*还',    # 代XXX还
                r'替\s*[\u4e00-\u9fff]{1,4}\s*还款',  # 替XXX还款
                r'替\s*[\u4e00-\u9fff]{1,4}\s*还',    # 替XXX还
                r'代\s*[\u4e00-\u9fff]{1,4}\s*偿',    # 代XXX偿
                r'替\s*[\u4e00-\u9fff]{1,4}\s*偿',    # 替XXX偿

                # 其他代还表述
                r'代\s*[\u4e00-\u9fff]{1,4}\s*还债',  # 代XXX还债
                r'代\s*[\u4e00-\u9fff]{1,4}\s*还钱',  # 代XXX还钱
                r'替\s*[\u4e00-\u9fff]{1,4}\s*还债',  # 替XXX还债
                r'替\s*[\u4e00-\u9fff]{1,4}\s*还钱',  # 替XXX还钱
                r'代\s*[\u4e00-\u9fff]{1,4}\s*偿还',  # 代XXX偿还
                r'替\s*[\u4e00-\u9fff]{1,4}\s*偿还',  # 替XXX偿还

                # 简化表述
                r'代还[\u4e00-\u9fff]{1,4}',         # 代还XXX
                r'替还[\u4e00-\u9fff]{1,4}',         # 替还XXX
                r'代偿[\u4e00-\u9fff]{1,4}',         # 代偿XXX
                r'替偿[\u4e00-\u9fff]{1,4}',         # 替偿XXX

                # 附言常见表述
                r'附言[：:]\s*代\s*[\u4e00-\u9fff]{1,4}',  # 附言：代XXX
                r'备注[：:]\s*代\s*[\u4e00-\u9fff]{1,4}',  # 备注：代XXX
                r'用途[：:]\s*代\s*[\u4e00-\u9fff]{1,4}',  # 用途：代XXX
                r'说明[：:]\s*代\s*[\u4e00-\u9fff]{1,4}',  # 说明：代XXX
            ]

            # 遍历所有模式进行匹配
            for pattern in proxy_payment_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    matched_text = match.group()
                    # logger.debug(f"在附言中发现代还关键词: {matched_text}")
                    return '他人代还'

            # 如果没有匹配到任何代还关键词，返回空字符串
            return ''

        except Exception as e:
            # logger.debug(f"附言代还关键词识别失败: {str(e)}")
            return ''

    def _perform_ollama_ocr(self, image_path: Path) -> str:
        """
        使用Ollama OCR执行图片文字识别 - 中文路径优化版

        Args:
            image_path: 图片路径

        Returns:
            识别到的文本字符串
        """
        if self.ocr is None:
            logger.error("❌ Ollama OCR引擎未初始化")
            return ""

        import shutil
        import tempfile

        try:
            logger.info(f"🔍 使用Ollama OCR处理图片: {image_path.name}")
            # logger.debug(f"   原始图片路径: {image_path}")
            # logger.debug(f"   OCR引擎类型: {type(self.ocr)}")

            # 解决中文路径问题：复制到临时目录
            temp_image_path = None
            try:
                # 创建临时文件，使用英文文件名
                temp_dir = tempfile.mkdtemp()
                temp_filename = f"temp_ocr_image_{hash(str(image_path)) % 10000}.{image_path.suffix}"
                temp_image_path = Path(temp_dir) / temp_filename
                
                # 复制图片到临时目录
                shutil.copy2(str(image_path), str(temp_image_path))
                # logger.debug(f"   临时图片路径: {temp_image_path}")
                
                # 验证临时文件是否创建成功
                if not temp_image_path.exists():
                    logger.error(f"❌ 临时文件创建失败: {temp_image_path}")
                    return ""

                # 优化中文识别的提示词
                custom_prompt = """请识别这张银行转账凭证或支付回单中的所有文字信息。

重点提取以下信息：
1. 付款方姓名（关注：付款人、交易户名、付款方、汇款人、对方户名等字段）
2. 账号信息（关注：付款账号、交易卡号、付款卡号、账户、卡号等字段）
3. 金额和交易详情

请完整准确地识别所有可见的中文文字和数字，保持原始格式。"""

                # logger.debug(f"📝 使用中文优化提示词 (长度: {len(custom_prompt)} 字符)")

                # 尝试多种语言设置来提高中文识别效果
                language_options = ["Chinese", "zh", "zh-CN", "auto"]  # 优先中文
                
                for lang in language_options:
                    try:
                        # logger.debug(f"🌐 尝试语言设置: {lang}")
                        
                        # 调用Ollama OCR（使用临时文件路径）
                        result = self.ocr.process_image(
                            image_path=str(temp_image_path),  # 使用临时文件路径
                            format_type="text",
                            custom_prompt=custom_prompt,
                            language=lang
                        )

                        # 改进结果处理逻辑
                        extracted_text = ""
                        
                        if result:
                            # 处理不同的返回格式
                            if isinstance(result, str):
                                extracted_text = result.strip()
                            elif hasattr(result, 'text'):
                                extracted_text = result.text.strip()
                            elif hasattr(result, 'content'):
                                extracted_text = result.content.strip()
                            elif isinstance(result, dict):
                                # 处理字典格式返回
                                extracted_text = result.get('text', result.get('content', str(result))).strip()
                            else:
                                extracted_text = str(result).strip()
                        
                        # 验证识别结果
                        if extracted_text and len(extracted_text) > 5 and not extracted_text.startswith("Error"):
                            logger.info(f"✅ OCR识别成功 (语言: {lang})，文本长度: {len(extracted_text)} 字符")
                            
                            # 输出详细的识别结果用于调试
                            # logger.debug("=" * 50)
                            # logger.debug("📄 OCR识别完整文本:")
                            # logger.debug(extracted_text)
                            # logger.debug("=" * 50)
                            
                            # 输出关键部分预览
                            preview_length = min(300, len(extracted_text))
                            logger.info(f"📄 OCR文本预览 (前{preview_length}字符): {extracted_text[:preview_length]}")
                            
                            return extracted_text
                        else:
                            if extracted_text.startswith("Error"):
                                pass
                                # logger.debug(f"   ❌ 语言 {lang} 识别出错: {extracted_text[:100]}")
                            else:
                                pass
                                # logger.debug(f"   ⚠️ 语言 {lang} 识别结果为空或太短: {len(extracted_text)} 字符")
                            
                    except Exception as lang_error:
                        # logger.debug(f"   ❌ 语言 {lang} 识别失败: {str(lang_error)}")
                        continue
                
                # 如果所有语言都失败，再尝试不带language参数
                try:
                    # logger.debug("🔄 尝试不带language参数的识别...")
                    result = self.ocr.process_image(
                        image_path=str(temp_image_path),
                        format_type="text", 
                        custom_prompt=custom_prompt
                    )
                    
                    if result:
                        if isinstance(result, str):
                            extracted_text = result.strip()
                        elif hasattr(result, 'text'):
                            extracted_text = result.text.strip()
                        else:
                            extracted_text = str(result).strip()
                            
                        if extracted_text and len(extracted_text) > 5 and not extracted_text.startswith("Error"):
                            logger.info(f"✅ OCR识别成功 (自动语言检测)，文本长度: {len(extracted_text)} 字符")
                            logger.info(f"📄 OCR文本预览: {extracted_text[:200]}")
                            return extracted_text
                            
                except Exception as auto_error:
                    pass
                    # logger.debug(f"   ❌ 自动语言检测失败: {str(auto_error)}")

                logger.warning(f"❌ 所有OCR尝试均失败，未能识别到有效文本")
                return ""
                
            finally:
                # 清理临时文件
                if temp_image_path and temp_image_path.exists():
                    try:
                        temp_image_path.unlink()
                        temp_image_path.parent.rmdir()  # 删除临时目录
                        # logger.debug(f"✅ 临时文件已清理: {temp_image_path}")
                    except Exception as cleanup_error:
                        pass
                        # logger.debug(f"⚠️ 临时文件清理失败: {str(cleanup_error)}")

        except Exception as e:
            logger.error(f"❌ Ollama OCR识别异常: {str(e)}")
            # logger.debug(f"   异常类型: {type(e)}")
            import traceback
            # logger.debug(f"   异常堆栈: {traceback.format_exc()}")
            return ""

    def _extract_payer_info(self, text: str) -> str:
        """
        从OCR识别的文本中提取付款方信息 - 增强版

        Args:
            text: OCR识别的完整文本

        Returns:
            付款方姓名，未找到返回'未知'
        """
        try:
            # logger.debug("🔍 开始付款方信息提取流程...")
            
            # 注意：不在这里过滤"富安"，而是在候选姓名验证时过滤
            # 这样可以避免因为收款人是富安公司而误判

            # 付款方关键词模式列表（按优先级精确排序）
            payer_patterns = [
                # 【最高优先级】：附言/摘要中的姓名（真实付款人通常在这里）
                (r'附言[：:\s]*([*\u4e00-\u9fff]{2,4})', 1, '附言姓名'),           # 附言：刘丹0040
                (r'摘要[：:\s]*([*\u4e00-\u9fff]{2,4})', 1, '摘要姓名'),           # 摘要：姜春红十4425
                (r'备注[：:\s]*([*\u4e00-\u9fff]{2,4})', 1, '备注姓名'),           # 备注：王五
                
                # 【高优先级】：交易相关关键词（用户重点要求）
                (r'交易户名[：:\s]*([*\u4e00-\u9fff]{1,8})', 2, '交易户名'),         # 交易户名：朱颖
                (r'交易对方[：:\s]*([*\u4e00-\u9fff]{1,8})', 2, '交易对方'),         
                (r'交易方[：:\s]*([*\u4e00-\u9fff]{1,8})', 2, '交易方'),
                
                # 【中优先级】：付款相关关键词
                (r'付款方[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '付款方'),           # 付款方：张三
                (r'付款人[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '付款人'),           # 付款人：李四
                (r'付款方名称[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '付款方名称'),       # 付款方名称：王五
                (r'付款人户名[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '付款人户名'),       # 付款人户名：赵六
                (r'付款方户名[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '付款方户名'),       # 付款方户名：小明
                (r'付款户名[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '付款户名'),         # 付款户名：小红

                # 【中优先级】：汇款相关关键词
                (r'汇款人[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '汇款人'),           # 汇款人：张三
                (r'汇款人姓名[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '汇款人姓名'),       # 汇款人姓名：李四
                (r'汇出人[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '汇出人'),           # 汇出人：王五

                # 【中优先级】：对方相关关键词
                (r'对方户名[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '对方户名'),         # 对方户名：小明
                (r'对方姓名[：:\s]*([*\u4e00-\u9fff]{1,8})', 3, '对方姓名'),         # 对方姓名：小红

                # 【较低优先级】：客户相关关键词
                (r'客户姓名[：:\s]*([*\u4e00-\u9fff]{1,8})', 4, '客户姓名'),         # 客户姓名：张三
                (r'账户姓名[：:\s]*([*\u4e00-\u9fff]{1,8})', 4, '账户姓名'),         # 账户姓名：李四
                (r'户\s*名[：:\s]*([*\u4e00-\u9fff]{1,8})', 4, '户名'),          # 户名：王五

                # 【较低优先级】：转账相关关键词
                (r'转出方[：:\s]*([*\u4e00-\u9fff]{1,8})', 4, '转出方'),           # 转出方：赵六
                (r'转账人[：:\s]*([*\u4e00-\u9fff]{1,8})', 4, '转账人'),           # 转账人：小明

                # 【低优先级】：无冒号分隔的模式
                (r'付款方\s+([*\u4e00-\u9fff]{1,8})', 5, '付款方(无冒号)'),               # 付款方 张三
                (r'付款人\s+([*\u4e00-\u9fff]{1,8})', 5, '付款人(无冒号)'),               # 付款人 李四
                (r'汇款人\s+([*\u4e00-\u9fff]{1,8})', 5, '汇款人(无冒号)'),               # 汇款人 王五
                (r'户名\s+([*\u4e00-\u9fff]{1,8})', 5, '户名(无冒号)'),                 # 户名 赵六

                # 【最低优先级】：上下文相关模式（需要严格验证）
                (r'([*\u4e00-\u9fff]{2,6})\s*转账', 6, '姓名+转账'),                 # 张三转账
                (r'([*\u4e00-\u9fff]{2,6})\s*付款', 6, '姓名+付款'),                 # 李四付款
                (r'([*\u4e00-\u9fff]{2,6})\s*汇款', 6, '姓名+汇款'),                 # 王五汇款
            ]

            # logger.debug(f"📋 开始姓名模式匹配，共有 {len(payer_patterns)} 个模式")
            
            # 按优先级遍历所有模式进行匹配
            found_candidates = []
            
            for pattern, priority, pattern_name in payer_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    # logger.debug(f"   ✅ 模式 '{pattern_name}' 找到 {len(matches)} 个匹配: {matches}")
                    for match in matches:
                        payer_name = match.strip()
                        # 验证提取的姓名是否有效
                        if self._is_valid_payer_name(payer_name):
                            found_candidates.append({
                                'name': payer_name,
                                'priority': priority,
                                'pattern': pattern_name,
                                'source': f'{pattern_name}模式匹配'
                            })
                            # logger.debug(f"      有效姓名: 【{payer_name}】(优先级: {priority}, 模式: {pattern_name})")
                        else:
                            pass
                            # logger.debug(f"      无效姓名: 【{payer_name}】(未通过验证)")

            # 如果找到候选姓名，选择优先级最高的
            if found_candidates:
                # 按优先级排序（数字越小优先级越高）
                found_candidates.sort(key=lambda x: x['priority'])
                best_candidate = found_candidates[0]
                
                logger.info(f"✅ 选择最优付款方: 【{best_candidate['name']}】")
                logger.info(f"   匹配模式: {best_candidate['pattern']} (优先级: {best_candidate['priority']})")
                
                if len(found_candidates) > 1:
                    pass
                    # logger.debug(f"   其他候选: {[c['name'] for c in found_candidates[1:]]}")
                
                return best_candidate['name']

            # 如果没有匹配到任何模式，返回未知
            logger.warning("❌ 未找到任何有效的付款方信息")
            # logger.debug(f"   搜索的文本片段: {text[:300]}...")
            return '未知'

        except Exception as e:
            logger.error(f"❌ 付款方信息提取失败: {str(e)}")
            return '未知'

    def _is_valid_payer_name(self, name: str) -> bool:
        """
        验证提取的付款方姓名是否有效（增强版：解决*爽等识别问题）
        支持完整中文姓名、部分遮挡姓名和复姓

        Args:
            name: 提取的姓名

        Returns:
            是否为有效姓名
        """
        if not name or len(name.strip()) == 0:
            return False

        name = name.strip()

        # 长度检查：1-8个字符（支持复姓如"欧阳修"、"司马光"等）
        if len(name) < 1 or len(name) > 8:
            # logger.debug(f"姓名长度不符合要求: {name} (长度: {len(name)})")
            return False

        # 检查是否包含无效字符（只允许中文字符和星号）
        if not re.match(r'^[*\u4e00-\u9fff]+$', name):
            # logger.debug(f"姓名包含无效字符: {name}")
            return False

        # 检查是否为明显的系统词汇或无效内容
        invalid_names = [
            # 银行相关
            '银行', '支付', '转账', '付款', '收款', '汇款', '存款', '取款',
            '账户', '卡号', '余额', '开户', '销户', '冻结', '解冻',

            # 金额时间相关
            '金额', '时间', '日期', '年月', '小时', '分钟', '秒钟',
            '元', '角', '分', '万元', '千元', '百元',

            # 系统词汇
            '备注', '附言', '用途', '说明', '摘要', '详情', '信息',
            '成功', '失败', '处理', '完成', '确认', '取消',

            # 特殊标识（必须过滤）
            '富安', '富安公司', '富安集团',

            # 关键词本身（不应该被识别为姓名）
            '付款方', '付款人', '付款方名称', '付款人户名', '付款方户名', '付款户名',
            '汇款人', '汇款人姓名', '汇出人', '交易户名', '对方户名', '对方姓名',
            '客户姓名', '账户姓名', '户名', '转出方', '转账人',

            # 其他无效内容
            '系统', '客服', '管理', '操作', '服务', '业务',
            '网点', '柜台', '自助', '手机', '电脑', '终端'
        ]

        # 检查是否包含无效词汇
        for invalid in invalid_names:
            if invalid in name:
                # logger.debug(f"姓名包含无效词汇 '{invalid}': {name}")
                return False

        # 检查是否全是星号（无意义）
        if name.replace('*', '') == '':
            # logger.debug(f"姓名全为星号: {name}")
            return False

        # 优化星号验证逻辑 - 支持*爽等格式
        star_count = name.count('*')
        chinese_char_count = len(name) - star_count
        
        # 至少需要1个中文字符
        if chinese_char_count < 1:
            # logger.debug(f"姓名缺少中文字符: {name}")
            return False
        
        # 星号数量不应超过中文字符数量（允许*爽、*春红等格式）
        if star_count > chinese_char_count:
            # logger.debug(f"星号过多: {name} (星号:{star_count}, 中文:{chinese_char_count})")
            return False
        
        # 特别支持*爽、*红、*明等格式（星号+1个中文字符）
        if len(name) == 2 and name.startswith('*') and chinese_char_count == 1:
            # logger.debug(f"识别到*X格式姓名: {name}")
            return True
        
        # 支持*春红、*建华等格式（星号+2个中文字符）
        if len(name) == 3 and name.startswith('*') and chinese_char_count == 2:
            # logger.debug(f"识别到*XX格式姓名: {name}")
            return True

        # 常见复姓列表（用于验证复姓的有效性）
        common_compound_surnames = [
            '欧阳', '太史', '端木', '上官', '司马', '东方', '独孤', '南宫', '万俟', '闻人',
            '夏侯', '诸葛', '尉迟', '公羊', '赫连', '澹台', '皇甫', '宗政', '濮阳', '公冶',
            '太叔', '申屠', '公孙', '慕容', '仲孙', '钟离', '长孙', '宇文', '司徒', '鲜于'
        ]

        # 如果是复姓，进行特殊验证
        for compound in common_compound_surnames:
            if name.startswith(compound) or name.startswith('*' + compound[1:]) or name.startswith(compound[0] + '*'):
                # logger.debug(f"识别到复姓: {name}")
                return True

        # 基本姓名格式验证通过
        # logger.debug(f"姓名验证通过: {name}")
        return True

    def _find_accounts_by_keywords(self, text: str) -> List[Dict]:
        """
        基于关键词查找账户号码 - 增强版（按用户要求的优先级排序）
        
        Args:
            text: OCR识别的文本
            
        Returns:
            账户候选列表，每个元素包含last4, source, priority等信息
        """
        candidates = []
        
        # 付款方关键词（按用户要求的优先级排序）
        payer_keywords = [
            # 【最高优先级】：交易相关关键词 
            ('交易卡号', 1), ('交易账户', 1), ('交易账号', 1),
            
            # 【高优先级】：付款方关键词
            ('付款卡号', 2), ('付款账户', 2), ('付款账号', 2), 
            ('付款人账号', 2), ('付款方账号', 2),
            
            # 【中优先级】：汇款相关
            ('汇款人卡号', 3), ('汇款人账号', 3), ('汇款账户', 3),
            ('汇款卡号', 3), ('汇款账号', 3),
            
            # 【中等优先级】：其他付款相关
            ('扣款账户', 3), ('扣款账号', 3), ('扣款卡号', 3),
            ('转出账户', 3), ('转出账号', 3), ('转出卡号', 3),
            
            # 【较低优先级】：通用关键词
            ('卡号', 4), ('账号', 4), ('账户', 4),
            ('银行卡号', 4), ('银行账户', 4), ('银行账号', 4),
        ]
        
        # logger.debug(f"🔍 开始基于 {len(payer_keywords)} 个关键词搜索账户...")
        
        for keyword, priority in payer_keywords:
            # logger.debug(f"   搜索关键词: 【{keyword}】 (优先级: {priority})")
            
            # 构建多种正则表达式模式来匹配不同格式
            patterns = [
                # 模式1: 关键词:号码 (最常见)
                rf'{keyword}[：:\s]*([*\d\s]{{8,25}})',
                
                # 模式2: 关键词 号码 (空格分隔)  
                rf'{keyword}\s+([*\d\s]{{8,25}})',
                
                # 模式3: 关键词后面跟随的数字块
                rf'{keyword}[：:\s]*(\d{{4}}\s*\d{{4}}\s*\d{{4}}\s*\d{{4}})',
                
                # 模式4: 带星号遮挡的卡号
                rf'{keyword}[：:\s]*(\*+\d{{4}})',
                
                # 模式5: 完整的银行卡号格式 (16-19位)
                rf'{keyword}[：:\s]*(\d{{4}}\s?\d{{4}}\s?\d{{4}}\s?\d{{4,7}})',
                
                # 模式6: 简化的数字匹配
                rf'{keyword}[：:\s]*([*\d\s-]{{10,30}})',
            ]
            
            for i, pattern in enumerate(patterns, 1):
                try:
                    matches = re.findall(pattern, text, re.IGNORECASE)
                    
                    if matches:
                        # logger.debug(f"      模式{i} 找到 {len(matches)} 个匹配: {matches}")
                        
                        for match in matches:
                            account_info = self._extract_account_from_match(match, keyword, priority)
                            if account_info:
                                candidates.append(account_info)
                                # logger.debug(f"         ✅ 有效账户: ***{account_info['last4']} (完整匹配: {match})")
                            else:
                                pass
                                # logger.debug(f"         ❌ 无效账户: {match}")
                    else:
                        pass
                        # logger.debug(f"      模式{i} 未找到匹配")
                        
                except Exception as e:
                    pass
                    # logger.debug(f"      模式{i} 匹配异常: {str(e)}")
        
        # logger.debug(f"🎯 关键词搜索完成，共找到 {len(candidates)} 个候选账户")
        return candidates

    def _find_accounts_by_numbers(self, text: str) -> List[Dict]:
        """
        通过数字模式查找账户（作为备选方案）
        
        Args:
            text: OCR识别的文本
            
        Returns:
            账户候选列表
        """
        candidates = []
        
        # 查找所有可能的银行卡号（12-20位数字）
        long_numbers = re.findall(r'\d{12,20}', text)
        for number in long_numbers:
            last4 = number[-4:]
            # 排除收款账户
            if last4 != '1284':
                candidates.append({
                    'last4': last4,
                    'source': f'通用数字匹配({number[:4]}***{last4})',
                    'priority': 10,  # 最低优先级
                    'full_number': number
                })
        
        # 查找星号遮盖格式的账户（如：6217 ****** 5240）
        star_patterns = [
            r'(\d{4})\s*\*{3,}\s*(\d{4})',      # 6217 ****** 5240
            r'(\d{4})\*{3,}(\d{4})',            # 6217******5240
            r'(\d{6})\s*\*{3,}\s*(\d{4})',      # 621799 ****** 5240
        ]
        
        for pattern in star_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if len(match) == 2:
                    last4 = match[1]
                    # 排除收款账户
                    if last4 != '1284':
                        candidates.append({
                            'last4': last4,
                            'source': f'星号格式匹配({match[0]}***{last4})',
                            'priority': 8,
                            'first_part': match[0],
                            'last_part': match[1]
                        })
        
        return candidates

    def _extract_account_from_match(self, match, keyword: str, priority: int) -> Optional[Dict]:
        """
        从正则匹配结果中提取账户信息 - 增强版
        
        Args:
            match: 正则匹配的结果（可能是字符串或元组）
            keyword: 匹配的关键词
            priority: 优先级
            
        Returns:
            账户信息字典或None
        """
        try:
            # 处理正则匹配结果
            if isinstance(match, tuple):
                # 如果是元组，取第一个非空元素
                account_text = next((item for item in match if item.strip()), "")
            else:
                account_text = str(match)
            
            if not account_text:
                # logger.debug(f"   ❌ 空匹配结果: {match}")
                return None
            
            # logger.debug(f"   🔍 处理账户匹配: 【{account_text}】")
            
            # 清理账户文本：删除所有空格、破折号、点等分隔符
            cleaned_account = re.sub(r'[\s\-\.]+', '', account_text.strip())
            # logger.debug(f"      清理后: 【{cleaned_account}】")
            
            # 处理不同格式的账户
            last4_digits = ""
            
            # 格式1: 包含星号的部分遮挡格式 (如: 6217***5240, ****1234)
            star_pattern = r'\*+(\d{4})$'  # 以4位数字结尾
            star_match = re.search(star_pattern, cleaned_account)
            if star_match:
                last4_digits = star_match.group(1)
                # logger.debug(f"      ✅ 星号格式，后4位: {last4_digits}")
            
            # 格式2: 完整数字格式 (如: ****************)
            elif cleaned_account.isdigit():
                if len(cleaned_account) >= 4:
                    last4_digits = cleaned_account[-4:]
                    # logger.debug(f"      ✅ 完整数字格式，后4位: {last4_digits}")
                else:
                    # logger.debug(f"      ❌ 数字太短，长度: {len(cleaned_account)}")
                    return None
            
            # 格式3: 混合格式 (如: 6217****5240中提取5240)
            else:
                # 提取最后连续的4位数字
                trailing_digits = re.search(r'(\d{4})$', cleaned_account)
                if trailing_digits:
                    last4_digits = trailing_digits.group(1)
                    # logger.debug(f"      ✅ 混合格式，后4位: {last4_digits}")
                else:
                    # 如果末尾没有4位数字，提取所有数字的最后4位
                    all_digits = re.findall(r'\d', cleaned_account)
                    if len(all_digits) >= 4:
                        last4_digits = ''.join(all_digits[-4:])
                        # logger.debug(f"      ✅ 提取数字，后4位: {last4_digits}")
                    else:
                        # logger.debug(f"      ❌ 数字不足4位: {''.join(all_digits)}")
                        return None
            
            # 验证后4位数字
            if not last4_digits or len(last4_digits) != 4 or not last4_digits.isdigit():
                # logger.debug(f"      ❌ 无效后4位: 【{last4_digits}】")
                return None
            
            # 排除明显的无效账户
            invalid_patterns = ['0000', '1111', '2222', '3333', '4444', '5555', 
                              '6666', '7777', '8888', '9999', '1234', '4321']
            if last4_digits in invalid_patterns:
                # logger.debug(f"      ❌ 明显无效的后4位: {last4_digits}")
                return None
            
            # 排除收款账户 1284
            if last4_digits == '1284':
                # logger.debug(f"      ❌ 排除收款账户: {last4_digits}")
                return None
            
            # 构建结果
            result = {
                'last4': last4_digits,
                'source': f'{keyword}关键词',
                'priority': priority,
                'original': account_text,
                'cleaned': cleaned_account
            }
            
            # logger.debug(f"      ✅ 成功提取账户: ***{last4_digits} (来源: {keyword})")
            return result
            
        except Exception as e:
            # logger.debug(f"      ❌ 账户提取异常: {str(e)}")
            return None

    def _filter_and_rank_accounts(self, candidates: List[Dict]) -> List[Dict]:
        """
        过滤和排序候选账户 - 增强版（严格的过滤和排序逻辑）
        
        Args:
            candidates: 候选账户列表
            
        Returns:
            排序后的有效账户列表
        """
        if not candidates:
            # logger.debug("❌ 没有候选账户需要过滤")
            return []
        
        # logger.debug(f"📋 开始过滤和排序 {len(candidates)} 个候选账户...")
        
        # 第一步：基本有效性过滤
        valid_candidates = []
        for candidate in candidates:
            last4 = candidate.get('last4', '')
            
            # 检查后4位是否有效
            if not last4 or len(last4) != 4 or not last4.isdigit():
                # logger.debug(f"   ❌ 无效账户格式: {candidate}")
                continue
                
            # 排除收款账户1284
            if last4 == '1284':
                # logger.debug(f"   ❌ 排除收款账户: ***{last4}")
                continue
                
            # 排除明显无效的账户（如全0、全1等）
            if last4 in ['0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999']:
                # logger.debug(f"   ❌ 排除明显无效账户: ***{last4}")
                continue
                
            valid_candidates.append(candidate)
            # logger.debug(f"   ✅ 有效账户: ***{last4} (来源: {candidate.get('source', 'unknown')}, 优先级: {candidate.get('priority', 99)})")
        
        if not valid_candidates:
            logger.warning("❌ 没有通过基本有效性检查的账户")
            return []
            
        # logger.debug(f"📋 基本过滤完成，剩余 {len(valid_candidates)} 个有效候选")
        
        # 第二步：按last4去重（相同last4的只保留优先级最高的）
        unique_accounts = {}
        for candidate in valid_candidates:
            last4 = candidate['last4']
            priority = candidate.get('priority', 99)
            keyword = candidate.get('keyword', '')
            
            if last4 not in unique_accounts:
                unique_accounts[last4] = candidate
                # logger.debug(f"   📝 新增账户: ***{last4} (优先级: {priority}, 关键词: {keyword})")
            else:
                existing_priority = unique_accounts[last4].get('priority', 99)
                if priority < existing_priority:  # 数字越小优先级越高
                    old_keyword = unique_accounts[last4].get('keyword', '')
                    unique_accounts[last4] = candidate
                    # logger.debug(f"   🔄 更新账户: ***{last4} 优先级 {existing_priority}({old_keyword}) → {priority}({keyword})")
                else:
                    pass
                    # logger.debug(f"   ⚪ 保持账户: ***{last4} 优先级 {existing_priority} > {priority}")
        
        # logger.debug(f"📋 去重完成，剩余 {len(unique_accounts)} 个唯一账户")
        
        # 第三步：按优先级和关键词重要性排序
        sorted_accounts = sorted(
            unique_accounts.values(),
            key=lambda x: (
                x.get('priority', 99),  # 首要排序：优先级（数字越小越优先）
                x.get('keyword', '').lower(),  # 次要排序：关键词字母顺序
                x['last4']  # 最终排序：账户号码
            )
        )
        
        # 记录排序结果
        if sorted_accounts:
            logger.info(f"📊 账户排序完成，找到 {len(sorted_accounts)} 个候选账户:")
            for i, account in enumerate(sorted_accounts, 1):
                priority = account.get('priority', 99)
                keyword = account.get('keyword', 'unknown')
                source = account.get('source', 'unknown')
                logger.info(f"   {i}. ***{account['last4']} (优先级: {priority}, 关键词: {keyword}, 来源: {source})")
                
                if i == 1:
                    logger.info(f"   🏆 最优选择: ***{account['last4']} (将被选用)")
        
        return sorted_accounts



    

    def _tesseract_recognize_image(self, img) -> List[str]:
        """Tesseract识别PIL图片对象"""
        texts = []
        try:
            from PIL import ImageEnhance, ImageFilter
            
            # 复制图片以避免修改原图
            img_copy = img.copy()
            
            # 增强对比度和锐度
            enhancer = ImageEnhance.Contrast(img_copy)
            img_copy = enhancer.enhance(2.0)
            
            enhancer = ImageEnhance.Sharpness(img_copy)
            img_copy = enhancer.enhance(2.0)
            
            # 尝试多种Tesseract配置
            configs = [
                '--psm 6 -l chi_sim',  # 统一文本块
                '--psm 8 -l chi_sim',  # 单个词
                '--psm 13 -l chi_sim',  # 原始行
            ]
            
            for config in configs:
                try:
                    text = pytesseract.image_to_string(img_copy, config=config)
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    if lines:
                        texts.extend(lines)
                        break
                except:
                    continue
                    
        except Exception as e:
            pass
            # logger.debug(f"Tesseract识别失败: {e}")
        return texts

    def process_image(self, image_path: Path, folder_name: str, is_deposit: bool = False,
                     deposit_image_path: str = '') -> Dict[str, str]:
        """
        处理单个图片文件

        Args:
            image_path: 图片路径
            folder_name: 所在文件夹名称
            is_deposit: 是否为代存
            deposit_image_path: 代存图片路径

        Returns:
            处理结果字典
        """
        result = {col: '' for col in self.excel_columns}
        result['文件夹名称'] = folder_name
        result['完整文件路径'] = str(image_path)
        result['是否代存'] = '是' if is_deposit else '否'
        result['代存图片路径'] = deposit_image_path

        try:
            # 增强的文件存在性检查
            if not image_path.exists():
                # 尝试查找相似文件
                logger.warning(f"文件不存在，尝试查找相似文件: {image_path}")

                # 在父目录中查找相似文件
                similar_file = self._find_similar_image_file(image_path)
                if similar_file:
                    logger.info(f"✅ 找到相似文件: {image_path} → {similar_file}")
                    image_path = similar_file  # 使用找到的相似文件
                else:
                    result['失败原因'] = f'文件不存在且无相似文件: {image_path.name}'
                    logger.error(f"文件不存在且无相似文件: {image_path}")

                    # 列出父目录中的文件以便调试
                    if image_path.parent.exists():
                        try:
                            image_files = [f.name for f in image_path.parent.iterdir()
                                         if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']]
                            if image_files:
                                logger.info(f"父目录中的图片文件: {image_files[:5]}...")  # 只显示前5个
                        except Exception as e:
                            pass
                            # logger.debug(f"列出父目录文件失败: {e}")

                    return result
            
            # 检查文件大小
            if image_path.stat().st_size == 0:
                result['失败原因'] = '文件大小为0'
                logger.error(f"文件大小为0: {image_path}")
                return result

            # 解析文件名
            filename_without_ext = image_path.stem
            parsed_filename = self.parse_filename(filename_without_ext)

            result['图片文件名姓名'] = parsed_filename['name']
            result['图片身份证后4位'] = parsed_filename['id_last4']
            result['图片文件名金额'] = parsed_filename['amount']

            # 如果文件名包含"代存"，跳过OCR识别
            if '代存' in filename_without_ext:
                result['失败原因'] = '代存文件，跳过OCR识别'
                return result

            # 验证图片文件完整性
            try:
                from PIL import Image
                with Image.open(image_path) as img:
                    img.verify()  # 验证图片文件完整性
            except Exception as e:
                result['失败原因'] = f'图片文件损坏: {str(e)}'
                logger.error(f"图片文件损坏: {image_path}, 错误: {str(e)}")
                return result

            # 注释掉文件名解析赋值部分，强制使用OCR结果
            # if self.ocr_engine_type == 'none':
            #     # OCR引擎被禁用时，使用文件名解析结果
            #     result['还款人'] = parsed_filename['name'] if parsed_filename['name'] else '未识别'
            #     logger.info(f"👤 OCR引擎已禁用，使用文件名解析结果: 【{result['还款人']}】")
            # else:
            #     # OCR引擎启用时，设置为未识别，等待OCR结果
            #     result['还款人'] = '未识别'  # 改为未识别，强制依赖OCR结果
            # result['图片OCR识别金额'] = parsed_filename['amount']  # 直接使用文件名中的金额
            
            # 初始化为空，等待OCR结果填充
            result['还款人'] = ''  
            result['图片OCR识别金额'] = ''
            result['还款时间'] = ''
            result['还款账号'] = ''
            result['还款来源'] = ''
            
            logger.info(f"📌 强制使用OCR识别模式，不使用文件名解析结果")
            
            # 查询债务人信息并填入debtor_no（增强版：优先从还款对账明细查询）
            debtor_query_result = {'found': False, 'debtor_no': '', 'match_method': '', 'is_missing': False}
            
            if parsed_filename['name']:
                # 🆕 优先从还款对账明细中查询debtor_no
                debtor_query_result = self._query_debtor_no_from_reconciliation(
                    name=parsed_filename['name'],
                    id_last4=parsed_filename.get('id_last4')
                )
                
                if debtor_query_result['found']:
                    result['debtor_no'] = debtor_query_result['debtor_no']
                    logger.info(f"✅ 通过还款对账明细找到debtor_no: {parsed_filename['name']} → {debtor_query_result['debtor_no']} ({debtor_query_result['match_method']})")
                else:
                    # 备用方法：从Excel中获取身份证号和合同号查询债务人数据库
                    logger.info(f"📋 还款对账明细未找到，尝试数据库查询: {parsed_filename['name']}")
                    excel_info = self._extract_info_from_excel(folder_name, parsed_filename['name'])
                    if excel_info:
                        # 查询债务人信息
                        debtor_info = self._query_debtor_info(
                            identity_no=excel_info.get('identity_no'),
                            contract_no=excel_info.get('contract_no')
                        )
                        if debtor_info['found']:
                            result['debtor_no'] = debtor_info['debtor_no']
                            logger.info(f"✅ 通过数据库找到debtor_no: {parsed_filename['name']} → {debtor_info['debtor_no']}")
                        else:
                            result['debtor_no'] = ''
                            logger.warning(f"⚠️ 数据库也未找到debtor_no: {parsed_filename['name']}")
                    else:
                        result['debtor_no'] = ''
                        logger.warning(f"⚠️ Excel信息提取失败: {parsed_filename['name']}")
                
                # 🟡 检查是否需要黄色标识（文件夹中有人名但还款对账明细中没有记录）
                if debtor_query_result['is_missing']:
                    result['_需要黄色标识'] = True
                    result['_黄色标识原因'] = f"文件夹中有姓名[{parsed_filename['name']}]但还款对账明细中未找到记录"
                    logger.warning(f"🟡 标记黄色：{parsed_filename['name']} - 文件夹中存在但还款对账明细中缺失")
            else:
                result['debtor_no'] = ''
            
            # 尝试OCR识别（现在是唯一的信息来源）
            logger.info(f"📸 开始处理图片: {image_path.name}")
            
            if self.ocr_engine_type != 'none':
                try:
                    logger.info(f"🚀 启动OCR识别流程...")
                    ocr_result = self.ocr_extract_info(image_path)
                    
                    if ocr_result['ocr_success']:
                        logger.info(f"✅ OCR识别成功，开始应用识别结果...")
                        
                        # 1. 处理付款方信息（过滤收款方）
                        if ocr_result['payer']:
                            payer_name = ocr_result['payer']
                            # 过滤收款方信息（如辽宁富安金融资产等）- 不跳过文件，只是不记录还款人
                            if '辽宁富安金融资产' in payer_name or '金融资产' in payer_name:
                                logger.warning(f"🚫 识别到收款方信息，还款人字段置空: 【{payer_name}】")
                                result['还款人'] = ''  # 收款方信息不记录为还款人
                                result['失败原因'] = f'识别到收款方信息: {payer_name}'
                            else:
                                # 🆕 检查是否为带星号的付款方名称，需要特殊处理
                                if '*' in payer_name:
                                    logger.info(f"⭐ 检测到带星号的付款方: 【{payer_name}】，进行星号验证...")
                                    starred_validation = self._validate_starred_payer_name(
                                        ocr_payer=payer_name,
                                        filename_name=parsed_filename.get('name', ''),
                                        ocr_payment_time=ocr_result.get('payment_time', ''),
                                        folder_name=folder_name
                                    )
                                    
                                    if starred_validation['should_replace']:
                                        result['还款人'] = starred_validation['replacement_name']
                                        logger.info(f"✅ 星号付款方替换成功: 【{payer_name}】 → 【{starred_validation['replacement_name']}】")
                                        logger.info(f"   替换原因: {starred_validation['reason']}")
                                    else:
                                        result['还款人'] = payer_name
                                        logger.info(f"⭐ 保持星号付款方: 【{payer_name}】")
                                        logger.info(f"   不替换原因: {starred_validation['reason']}")
                                else:
                                    result['还款人'] = payer_name
                                    logger.info(f"👤 付款方识别成功: 【{payer_name}】")
                        else:
                            logger.warning(f"👤 OCR未识别到付款方，保持为空")

                        # 2. 处理账户号码后4位
                        if ocr_result['account_last4']:
                            result['还款账号'] = ocr_result['account_last4']
                            logger.info(f"💳 账户号码识别成功: ***{ocr_result['account_last4']}")
                        else:
                            logger.warning(f"💳 OCR未识别到账户号码，保持为空")

                        # 3. 处理还款金额
                        if ocr_result.get('payment_amount'):
                            result['图片OCR识别金额'] = ocr_result['payment_amount']
                            logger.info(f"💰 还款金额识别成功: {ocr_result['payment_amount']}")
                        else:
                            logger.warning(f"💰 OCR未识别到还款金额，保持为空")

                        # 4. 处理还款时间（格式化为年月日）
                        if ocr_result['payment_time']:
                            raw_payment_time = ocr_result['payment_time']
                            result['还款时间'] = self._format_payment_time(raw_payment_time)
                            logger.info(f"⏰ 还款时间识别成功: {raw_payment_time} → {result['还款时间']}")
                        else:
                            logger.warning(f"⏰ OCR未识别到还款时间，保持为空")

                        # 5. 处理还款来源（优化：基于姓名比较和代还关键词综合判断）
                        ocr_payer = ocr_result.get('payer', '').strip()
                        filename_name = parsed_filename.get('name', '').strip()
                        
                        # 首先基于姓名比较判断是否他人代还
                        name_comparison = self._compare_names(ocr_payer, filename_name)
                        is_different_payer = name_comparison['score'] < 0.5  # 姓名不匹配
                        
                        # 综合判断还款来源
                        if is_different_payer and ocr_payer and filename_name:
                            result['还款来源'] = '他人代还'
                            logger.info(f"🔄 还款来源: 他人代还 (付款人[{ocr_payer}]与文件名[{filename_name}]不匹配)")
                        elif ocr_result['payment_source'] == '他人代还':
                            result['还款来源'] = '他人代还'
                            logger.info(f"🔄 还款来源: 他人代还 (OCR识别到代还关键词)")
                        else:
                            result['还款来源'] = '本人还款'
                            logger.info(f"🔄 还款来源: 本人还款 (付款人姓名匹配且无代还关键词)")
                        
                        # 🔍 步骤6: OCR结果与文件名匹配验证（新增功能）
                        logger.info(f"🔍 开始OCR与文件名匹配验证...")
                        validation_result = self.validate_ocr_vs_filename(
                            ocr_result, parsed_filename, str(image_path)
                        )
                        
                        # 设置验证结果到Excel列
                        result['OCR文件名匹配状态'] = validation_result['status']
                        result['OCR准确度评估'] = validation_result['accuracy_assessment']
                        
                        # 如果需要标红，设置标记
                        if validation_result['needs_highlight']:
                            result['_需要标红'] = True
                            result['_验证详情'] = '; '.join(validation_result['details'])
                        
                        # 记录最终OCR应用结果
                        logger.info(f"📊 OCR结果应用完成:")
                        logger.info(f"   👤 还款人: 【{result['还款人']}】")
                        logger.info(f"   💳 还款账号: ***{result['还款账号']}")
                        logger.info(f"   💰 还款金额: {result['图片OCR识别金额']}")
                        logger.info(f"   ⏰ 还款时间: {result['还款时间']}")
                        logger.info(f"   🔄 还款来源: 【{result['还款来源']}】")
                        logger.info(f"   ✅ 匹配验证: {validation_result['status']} (置信度: {validation_result['confidence']:.2f})")
                        
                    else:
                        logger.error(f"❌ OCR识别失败，所有字段保持为空")
                        result['还款来源'] = ''  # OCR失败时也不设置默认值
                        result['OCR文件名匹配状态'] = '无法验证'
                        result['OCR准确度评估'] = 'OCR失败'
                        result['_需要黄色标识'] = True  # OCR失败需要黄色标识
                        result['_黄色标识原因'] = 'OCR识别失败'
                        
                except Exception as e:
                    logger.error(f"❌ OCR识别过程异常: {str(e)}")
                    result['还款来源'] = ''  # 异常时也不设置默认值
            else:
                logger.error(f"⚠️  OCR引擎未初始化，无法进行识别")

        except Exception as e:
            result['失败原因'] = f'处理异常: {str(e)}'
            logger.error(f"❌ 处理图片失败: {image_path}, 错误: {str(e)}")

        # 记录最终处理结果
        logger.info(f"🎯 图片处理完成: {image_path.name}")
        logger.info(f"   📋 最终结果 - 付款方: 【{result['还款人']}】, 账户: ***{result['还款账号']}, 来源: 【{result['还款来源']}】")
        
        return result

    def process_directory(self, directory: Path, parent_folder_name: str = '') -> None:
        """
        处理目录中的所有文件

        Args:
            directory: 目录路径
            parent_folder_name: 父文件夹名称
        """
        try:
            folder_name = parent_folder_name or directory.name
            logger.info(f"处理目录: {directory}")

            # 记录处理前的结果数量
            results_before = len(self.results)

            # 首先处理压缩文件
            archive_files = [f for f in directory.iterdir()
                           if f.is_file() and self.is_archive_file(f)]

            extracted_dirs = []  # 记录解压出的目录，避免重复处理
            for archive_file in archive_files:
                logger.info(f"发现压缩文件: {archive_file}")
                if self.extract_archive(archive_file):
                    # 解压成功后，记录解压出的文件夹
                    extracted_dir = directory / archive_file.stem
                    if extracted_dir.exists():
                        extracted_dirs.append(extracted_dir)
                        logger.info(f"压缩文件已解压到: {extracted_dir}")

            # 🆕 扫描还款对账明细Excel文件
            reconciliation_records = self._scan_reconciliation_excel(directory)
            if reconciliation_records:
                self.reconciliation_data.extend(reconciliation_records)
                logger.info(f"📊 已收集 {len(reconciliation_records)} 条还款对账明细记录")

            # 处理图片文件（排除已解压的压缩文件）
            image_files = [f for f in directory.iterdir()
                          if f.is_file() and self.is_image_file(f)]

            # 检查是否有子文件夹（代存情况）
            subdirs = [d for d in directory.iterdir() if d.is_dir()]
            has_subdirs = len(subdirs) > 0

            # 查找代存图片
            deposit_image_path = ''
            deposit_subdir = None
            if has_subdirs:
                for subdir in subdirs:
                    # 检查子文件夹是否包含"代存"关键词
                    if '代存' in subdir.name:
                        deposit_subdir = subdir
                        # 查找代存子文件夹中的图片
                        deposit_files = [f for f in subdir.iterdir()
                                       if f.is_file() and self.is_image_file(f)]
                        if deposit_files:
                            deposit_image_path = str(deposit_files[0])
                            logger.info(f"找到代存图片: {deposit_image_path}")
                            break
                    else:
                        # 在普通子文件夹中查找包含"代存"的文件
                        deposit_files = [f for f in subdir.iterdir()
                                       if f.is_file() and self.is_image_file(f) and '代存' in f.stem]
                        if deposit_files:
                            deposit_image_path = str(deposit_files[0])
                            logger.info(f"找到代存图片: {deposit_image_path}")
                            break

            # 处理当前目录的图片文件
            for image_file in image_files:
                # 判断是否为代存
                is_deposit = '代存' in image_file.stem or (deposit_subdir and image_file.parent == deposit_subdir)
                result = self.process_image(image_file, folder_name, is_deposit, deposit_image_path)
                # 检查是否为被忽略的文件（如收款方信息），不添加到结果中
                if result.get('status') != 'ignored':
                    self.results.append(result)

            # 递归处理子文件夹（包括解压出的文件夹）
            for subdir in subdirs:
                # 🆕 为每个子文件夹扫描还款对账明细Excel
                subdir_reconciliation = self._scan_reconciliation_excel(subdir)
                if subdir_reconciliation:
                    self.reconciliation_data.extend(subdir_reconciliation)
                    logger.info(f"📊 子文件夹 {subdir.name} 收集 {len(subdir_reconciliation)} 条还款对账明细记录")
                
                # 如果是代存文件夹，单独处理其中的图片
                if '代存' in subdir.name:
                    for deposit_file in subdir.iterdir():
                        if deposit_file.is_file() and self.is_image_file(deposit_file):
                            result = self.process_image(deposit_file, folder_name, True, str(deposit_file))
                            # 检查是否为被忽略的文件（如收款方信息），不添加到结果中
                            if result.get('status') != 'ignored':
                                self.results.append(result)
                else:
                    # 检查子文件夹是否符合解析规则（包含姓名和金额）
                    subdir_parsed = self.parse_filename(subdir.name)
                    if subdir_parsed['parse_success']:
                        # 子文件夹名称符合解析规则，按文件夹处理
                        logger.info(f"处理子文件夹（按文件夹名解析）: {subdir.name}")
                        
                        # 查找子文件夹中的主要图片和代存图片
                        main_image = None
                        deposit_image_path = ''
                        
                        for file in subdir.iterdir():
                            if file.is_file() and self.is_image_file(file):
                                if '代存' in file.stem or '代还' in file.stem:
                                    # 找到代存图片
                                    deposit_image_path = str(file)
                                    logger.info(f"找到代存图片: {deposit_image_path}")
                                elif not main_image:
                                    # 找到主要图片（通常是第一个非代存图片）
                                    main_image = file
                        
                        # 创建一条记录，但不使用文件夹名称解析的结果
                        if main_image:
                            result = {col: '' for col in self.excel_columns}
                            result['文件夹名称'] = folder_name
                            result['完整文件路径'] = str(main_image)
                            # 注释掉文件名解析赋值
                            # result['还款人'] = subdir_parsed['name']
                            # result['图片OCR识别金额'] = subdir_parsed['amount']
                            result['图片文件名姓名'] = subdir_parsed['name']
                            result['图片身份证后4位'] = subdir_parsed['id_last4']
                            result['图片文件名金额'] = subdir_parsed['amount']
                            # result['还款来源'] = '本人还款'  # 默认值，后续可能被OCR结果覆盖
                            result['是否代存'] = '是' if deposit_image_path else '否'
                            result['代存图片路径'] = deposit_image_path
                            # result['还款账号'] = '0000'  # 默认值
                            
                            # 初始化为空，等待OCR结果
                            result['还款人'] = ''  
                            result['图片OCR识别金额'] = ''
                            result['还款时间'] = ''
                            result['还款账号'] = ''
                            result['还款来源'] = ''

                            # 查询债务人信息并填入debtor_no（增强版：优先从还款对账明细查询）
                            debtor_query_result = {'found': False, 'debtor_no': '', 'match_method': '', 'is_missing': False}
                            
                            if subdir_parsed['name']:
                                # 🆕 优先从还款对账明细中查询debtor_no
                                debtor_query_result = self._query_debtor_no_from_reconciliation(
                                    name=subdir_parsed['name'],
                                    id_last4=subdir_parsed.get('id_last4')
                                )
                                
                                if debtor_query_result['found']:
                                    result['debtor_no'] = debtor_query_result['debtor_no']
                                    logger.info(f"✅ 子目录通过还款对账明细找到debtor_no: {subdir_parsed['name']} → {debtor_query_result['debtor_no']} ({debtor_query_result['match_method']})")
                                else:
                                    # 备用方法：从Excel中获取身份证号和合同号查询债务人数据库
                                    logger.info(f"📋 子目录还款对账明细未找到，尝试数据库查询: {subdir_parsed['name']}")
                                    excel_info = self._extract_info_from_excel(folder_name, subdir_parsed['name'])
                                    if excel_info:
                                        debtor_info = self._query_debtor_info(
                                            identity_no=excel_info.get('identity_no'),
                                            contract_no=excel_info.get('contract_no')
                                        )
                                        if debtor_info['found']:
                                            result['debtor_no'] = debtor_info['debtor_no']
                                            logger.info(f"✅ 子目录通过数据库找到debtor_no: {subdir_parsed['name']} → {debtor_info['debtor_no']}")
                                        else:
                                            result['debtor_no'] = ''
                                            logger.warning(f"⚠️ 子目录数据库也未找到debtor_no: {subdir_parsed['name']}")
                                    else:
                                        result['debtor_no'] = ''
                                        logger.warning(f"⚠️ 子目录Excel信息提取失败: {subdir_parsed['name']}")
                                
                                # 🟡 检查是否需要黄色标识（文件夹中有人名但还款对账明细中没有记录）
                                if debtor_query_result['is_missing']:
                                    result['_需要黄色标识'] = True
                                    result['_黄色标识原因'] = f"子目录中有姓名[{subdir_parsed['name']}]但还款对账明细中未找到记录"
                                    logger.warning(f"🟡 子目录标记黄色：{subdir_parsed['name']} - 子目录中存在但还款对账明细中缺失")
                            else:
                                result['debtor_no'] = ''

                            # 标志：是否应该添加记录到结果中
                            should_add_record = True
                            
                            # 尝试OCR识别（现在是唯一的信息来源）
                            if self.ocr_engine_type != 'none':
                                try:
                                    logger.info(f"🚀 子目录OCR识别流程: {main_image.name}")
                                    ocr_result = self.ocr_extract_info(main_image)
                                    if ocr_result['ocr_success']:
                                        logger.info(f"✅ 子目录OCR识别成功，应用识别结果...")
                                        
                                        # 1. 处理付款方信息（过滤收款方）
                                        if ocr_result['payer']:
                                            payer_name = ocr_result['payer']
                                            # 过滤收款方信息（如辽宁富安金融资产等）- 不跳过文件，只是不记录还款人
                                            if '辽宁富安金融资产' in payer_name or '金融资产' in payer_name:
                                                logger.warning(f"🚫 子目录识别到收款方信息，还款人字段置空: 【{payer_name}】")
                                                result['还款人'] = ''  # 收款方信息不记录为还款人
                                                result['失败原因'] = f'识别到收款方信息: {payer_name}'
                                            else:
                                                result['还款人'] = payer_name
                                                logger.info(f"👤 子目录付款方识别成功: 【{payer_name}】")
                                        else:
                                            logger.warning(f"👤 子目录OCR未识别到付款方，保持为空")

                                        # 2. 处理账户号码
                                        if ocr_result['account_last4']:
                                            result['还款账号'] = ocr_result['account_last4']
                                            logger.info(f"💳 子目录账户识别成功: ***{ocr_result['account_last4']}")
                                        else:
                                            logger.warning(f"💳 子目录OCR未识别到账户号码，保持为空")

                                        # 3. 处理还款金额
                                        if ocr_result.get('payment_amount'):
                                            result['图片OCR识别金额'] = ocr_result['payment_amount']
                                            logger.info(f"💰 子目录还款金额识别成功: {ocr_result['payment_amount']}")
                                        else:
                                            logger.warning(f"💰 子目录OCR未识别到还款金额，保持为空")

                                        # 4. 处理还款时间（格式化为年月日）
                                        if ocr_result['payment_time']:
                                            raw_payment_time = ocr_result['payment_time']
                                            result['还款时间'] = self._format_payment_time(raw_payment_time)
                                            logger.info(f"⏰ 子目录还款时间识别成功: {raw_payment_time} → {result['还款时间']}")
                                        else:
                                            logger.warning(f"⏰ 子目录OCR未识别到还款时间，保持为空")

                                        # 5. 处理还款来源（优化：基于姓名比较和代还关键词综合判断）
                                        ocr_payer = ocr_result.get('payer', '').strip()
                                        filename_name = subdir_parsed.get('name', '').strip()
                                        
                                        # 首先基于姓名比较判断是否他人代还
                                        name_comparison = self._compare_names(ocr_payer, filename_name)
                                        is_different_payer = name_comparison['score'] < 0.5  # 姓名不匹配
                                        
                                        # 综合判断还款来源
                                        if is_different_payer and ocr_payer and filename_name:
                                            result['还款来源'] = '他人代还'
                                            logger.info(f"🔄 子目录还款来源: 他人代还 (付款人[{ocr_payer}]与文件名[{filename_name}]不匹配)")
                                        elif ocr_result['payment_source'] == '他人代还':
                                            result['还款来源'] = '他人代还'
                                            logger.info(f"🔄 子目录还款来源: 他人代还 (OCR识别到代还关键词)")
                                        else:
                                            result['还款来源'] = '本人还款'
                                            logger.info(f"🔄 子目录还款来源: 本人还款 (付款人姓名匹配且无代还关键词)")
                                        
                                        logger.info(f"📊 子目录OCR结果应用完成:")
                                        logger.info(f"   👤 还款人: 【{result['还款人']}】")
                                        logger.info(f"   💳 还款账号: ***{result['还款账号']}")
                                        logger.info(f"   💰 还款金额: {result['图片OCR识别金额']}")
                                        logger.info(f"   ⏰ 还款时间: {result['还款时间']}")
                                        logger.info(f"   🔄 还款来源: 【{result['还款来源']}】")
                                    else:
                                        logger.error(f"❌ 子目录OCR识别失败，所有字段保持为空")
                                        result['还款来源'] = ''  # OCR失败时也不设置默认值
                                        result['OCR文件名匹配状态'] = '无法验证'
                                        result['OCR准确度评估'] = 'OCR失败'
                                        result['_需要黄色标识'] = True  # OCR失败需要黄色标识
                                        result['_黄色标识原因'] = 'OCR识别失败'
                                        
                                except Exception as e:
                                    logger.error(f"❌ 子目录OCR识别异常: {str(e)}")
                                    result['还款来源'] = ''  # 异常时也不设置默认值
                            else:
                                logger.warning(f"⚠️  OCR引擎未初始化，跳过子目录OCR识别")
                            
                            # 只有在未检测到收款方信息时才添加记录
                            if should_add_record:
                                self.results.append(result)
                                logger.info(f"基于文件夹名称创建记录: {subdir.name} -> {subdir_parsed['name']} {subdir_parsed['amount']}元")
                            else:
                                logger.info(f"跳过收款方记录，不添加到结果中: {subdir.name}")
                    else:
                        # 文件夹名称不符合解析规则，按普通文件夹递归处理
                        self.process_directory(subdir, folder_name)

            # 📊 处理完成后立即验证该文件夹的数据一致性
            results_after = len(self.results)
            folder_results_count = results_after - results_before
            
            if folder_results_count > 0:
                logger.info(f"📊 开始验证文件夹 '{folder_name}' 的数据一致性...")
                validation_result = self._validate_folder_consistency(folder_name, results_before, results_after)
                
                if validation_result['is_consistent']:
                    logger.info(f"✅ 文件夹 '{folder_name}' 数据一致性验证通过")
                    logger.info(f"   📄 图片文件数: {validation_result['image_count']}")
                    logger.info(f"   👥 统计人数: {validation_result['person_count']}")
                    logger.info(f"   💰 总金额: {validation_result['total_amount']:.2f} 元")
                else:
                    logger.error(f"❌ 文件夹 '{folder_name}' 数据一致性验证失败!")
                    for error in validation_result['errors']:
                        logger.error(f"   {error}")
                
                # 🔍 验证OCR识别结果与Excel对比
                logger.info(f"🔍 开始验证OCR识别结果与Excel对比...")
                folder_ocr_results = self.results[results_before:results_after]
                excel_validation = self._validate_with_payment_excel(folder_name, folder_ocr_results)
                
                if excel_validation['is_valid']:
                    logger.info(f"✅ Excel验证通过 - 所有还款人都在Excel中找到")
                else:
                    logger.warning(f"⚠️  Excel验证发现问题:")
                    for error in excel_validation['errors']:
                        logger.warning(f"   {error}")
                    
                    # 记录未匹配的记录
                    if excel_validation['unmatched_records']:
                        for record in excel_validation['unmatched_records']:
                            # 在results中标记这些记录
                            for i in range(results_before, results_after):
                                if self.results[i].get('完整文件路径') == record['file']:
                                    self.results[i]['_需要标红'] = True
                                    break
            else:
                logger.warning(f"⚠️  文件夹 '{folder_name}' 未处理任何图片文件")

        except Exception as e:
            logger.error(f"处理目录失败: {directory}, 错误: {str(e)}")
    
    def _validate_folder_consistency(self, folder_name: str, start_index: int, end_index: int) -> Dict:
        """
        验证单个文件夹的数据一致性
        
        Args:
            folder_name: 文件夹名称
            start_index: 该文件夹在results中的开始索引
            end_index: 该文件夹在results中的结束索引
            
        Returns:
            验证结果字典
        """
        validation_result = {
            'is_consistent': True,
            'errors': [],
            'image_count': 0,
            'person_count': 0,
            'total_amount': 0.0,
            'person_stats': {}
        }
        
        try:
            # 获取该文件夹的所有处理结果
            folder_results = self.results[start_index:end_index]
            validation_result['image_count'] = len(folder_results)
            
            # 按人名统计该文件夹的数据
            person_stats = {}
            total_folder_amount = 0.0
            
            for result in folder_results:
                person_name = result.get('还款人', '').strip()
                amount_str = result.get('图片OCR识别金额', '') or result.get('图片文件名金额', '')
                
                # 如果没有姓名，尝试从文件路径中提取
                if not person_name:
                    file_path = result.get('完整文件路径', '')
                    if file_path:
                        file_name = os.path.basename(file_path)
                        parsed = self.parse_filename(os.path.splitext(file_name)[0])
                        if parsed['name']:
                            person_name = parsed['name']
                
                # 如果还是没有姓名，使用"未识别"
                if not person_name:
                    person_name = "未识别姓名"
                
                # 解析金额
                try:
                    amount = float(amount_str) if amount_str else 0.0
                except (ValueError, TypeError):
                    amount = 0.0
                
                total_folder_amount += amount
                
                # 统计每个人的信息
                if person_name not in person_stats:
                    person_stats[person_name] = {
                        'count': 0,
                        'total_amount': 0.0,
                        'files': []
                    }
                
                person_stats[person_name]['count'] += 1
                person_stats[person_name]['total_amount'] += amount
                person_stats[person_name]['files'].append({
                    'path': result.get('完整文件路径', ''),
                    'amount': amount
                })
            
            validation_result['person_count'] = len(person_stats)
            validation_result['total_amount'] = total_folder_amount
            validation_result['person_stats'] = person_stats
            
            # 验证每个人的金额统计
            for person_name, stats in person_stats.items():
                expected_amount = sum(file_info['amount'] for file_info in stats['files'])
                actual_amount = stats['total_amount']
                
                if abs(expected_amount - actual_amount) > 0.01:  # 允许小数点误差
                    validation_result['is_consistent'] = False
                    validation_result['errors'].append(
                        f"人员 '{person_name}' 金额不一致: 预期 {expected_amount:.2f}, 实际 {actual_amount:.2f}"
                    )
                
                # 验证文件数量
                if len(stats['files']) != stats['count']:
                    validation_result['is_consistent'] = False
                    validation_result['errors'].append(
                        f"人员 '{person_name}' 文件数量不一致: 预期 {len(stats['files'])}, 实际 {stats['count']}"
                    )
            
            # 从Excel中获取该文件夹的信息进行交叉验证
            excel_validation = self._validate_with_excel(folder_name, person_stats)
            if not excel_validation['is_consistent']:
                validation_result['is_consistent'] = False
                validation_result['errors'].extend(excel_validation['errors'])
            
            # 打印详细的验证信息
            if validation_result['is_consistent']:
                # logger.debug(f"文件夹 '{folder_name}' 详细统计:")
                for person_name, stats in person_stats.items():
                    pass
                    # logger.debug(f"  👤 {person_name}: {stats['count']} 个文件, 总金额 {stats['total_amount']:.2f} 元")
            
        except Exception as e:
            validation_result['is_consistent'] = False
            validation_result['errors'].append(f"验证过程出错: {str(e)}")
            logger.error(f"验证文件夹 '{folder_name}' 一致性时出错: {str(e)}")
        
        return validation_result
    
    def _validate_with_excel(self, folder_name: str, person_stats: Dict) -> Dict:
        """
        与Excel文件中的数据进行交叉验证
        
        Args:
            folder_name: 文件夹名称
            person_stats: 从图片处理结果统计的人员数据
            
        Returns:
            Excel验证结果
        """
        excel_validation = {
            'is_consistent': True,
            'errors': []
        }
        
        try:
            # 查找对应的Excel文件
            folder_path = self.base_folder / folder_name
            excel_files = list(folder_path.glob("*.xlsx")) + list(folder_path.glob("*.xls"))
            
            if len(excel_files) != 1:
                excel_validation['errors'].append(f"Excel文件数量异常: {len(excel_files)}")
                excel_validation['is_consistent'] = False
                return excel_validation
            
            excel_file = excel_files[0]
            df = pd.read_excel(excel_file)
            
            # 检查Excel中的人员是否都在图片处理结果中
            name_columns = ['姓名', '债务人姓名', '还款人姓名', '客户姓名', 'name']
            excel_names = set()
            
            for col in name_columns:
                if col in df.columns:
                    names = df[col].dropna().astype(str).str.strip()
                    excel_names.update(names)
                    break
            
            processed_names = set(person_stats.keys())
            
            # 检查Excel中的人员是否都被处理了
            missing_in_processed = excel_names - processed_names
            if missing_in_processed:
                excel_validation['errors'].append(
                    f"Excel中的人员未在图片中找到: {', '.join(missing_in_processed)}"
                )
                excel_validation['is_consistent'] = False
            
            # 检查处理结果中是否有Excel中没有的人员
            extra_in_processed = processed_names - excel_names
            if extra_in_processed:
                # 排除"未识别姓名"这种情况
                actual_extra = {name for name in extra_in_processed if not name.startswith("未识别")}
                if actual_extra:
                    excel_validation['errors'].append(
                        f"图片中的人员在Excel中未找到: {', '.join(actual_extra)}"
                    )
                    # 这种情况可能是正常的（文件名包含姓名但Excel中没有），不标记为不一致
                    # excel_validation['is_consistent'] = False
            
        except Exception as e:
            excel_validation['errors'].append(f"Excel验证过程出错: {str(e)}")
            excel_validation['is_consistent'] = False
        
        return excel_validation
    
    def _generate_overall_validation_report(self) -> Dict:
        """
        生成整体数据一致性验证报告
        
        Returns:
            整体验证报告字典
        """
        report = {
            'total_folders': 0,
            'validated_folders': 0,
            'consistent_folders': 0,
            'inconsistent_folders': 0,
            'total_images': len(self.results),
            'total_persons': 0,
            'total_amount': 0.0,
            'folder_details': {},
            'overall_errors': []
        }
        
        try:
            # 按文件夹重新统计所有数据
            folder_stats = {}
            
            for result in self.results:
                folder_name = result.get('文件夹名称', '').strip()
                if not folder_name:
                    continue
                
                if folder_name not in folder_stats:
                    folder_stats[folder_name] = {
                        'images': [],
                        'persons': set(),
                        'total_amount': 0.0
                    }
                
                # 收集图片信息
                folder_stats[folder_name]['images'].append(result)
                
                # 收集人员信息
                person_name = result.get('还款人', '').strip()
                if not person_name:
                    # 尝试从文件路径提取
                    file_path = result.get('完整文件路径', '')
                    if file_path:
                        file_name = os.path.basename(file_path)
                        parsed = self.parse_filename(os.path.splitext(file_name)[0])
                        if parsed['name']:
                            person_name = parsed['name']
                
                if person_name:
                    folder_stats[folder_name]['persons'].add(person_name)
                
                # 累计金额
                amount_str = result.get('图片OCR识别金额', '') or result.get('图片文件名金额', '')
                try:
                    amount = float(amount_str) if amount_str else 0.0
                    folder_stats[folder_name]['total_amount'] += amount
                except (ValueError, TypeError):
                    pass
            
            report['total_folders'] = len(folder_stats)
            report['validated_folders'] = len(folder_stats)
            
            # 详细验证每个文件夹
            for folder_name, stats in folder_stats.items():
                folder_validation = {
                    'image_count': len(stats['images']),
                    'person_count': len(stats['persons']),
                    'total_amount': stats['total_amount'],
                    'is_consistent': True,
                    'errors': []
                }
                
                # 检查是否有Excel文件
                try:
                    folder_path = self.base_folder / folder_name
                    excel_files = list(folder_path.glob("*.xlsx")) + list(folder_path.glob("*.xls"))
                    
                    if len(excel_files) != 1:
                        folder_validation['errors'].append(f"Excel文件数量异常: {len(excel_files)}")
                        folder_validation['is_consistent'] = False
                    else:
                        # 简化的Excel验证
                        try:
                            df = pd.read_excel(excel_files[0])
                            expected_persons = set()
                            
                            name_columns = ['姓名', '债务人姓名', '还款人姓名', '客户姓名', 'name']
                            for col in name_columns:
                                if col in df.columns:
                                    names = df[col].dropna().astype(str).str.strip()
                                    expected_persons.update(names)
                                    break
                            
                            # 检查人员匹配度
                            actual_persons = stats['persons']
                            missing_persons = expected_persons - actual_persons
                            if missing_persons and len(missing_persons) > len(expected_persons) * 0.5:  # 如果超过50%的人员缺失
                                folder_validation['errors'].append(f"大量人员未在图片中找到: {len(missing_persons)}/{len(expected_persons)}")
                                folder_validation['is_consistent'] = False
                                
                        except Exception as e:
                            folder_validation['errors'].append(f"Excel验证失败: {str(e)}")
                            folder_validation['is_consistent'] = False
                
                except Exception as e:
                    folder_validation['errors'].append(f"文件夹验证失败: {str(e)}")
                    folder_validation['is_consistent'] = False
                
                report['folder_details'][folder_name] = folder_validation
                
                if folder_validation['is_consistent']:
                    report['consistent_folders'] += 1
                else:
                    report['inconsistent_folders'] += 1
                    report['overall_errors'].extend([f"{folder_name}: {error}" for error in folder_validation['errors']])
                
                report['total_persons'] += folder_validation['person_count']
                report['total_amount'] += folder_validation['total_amount']
            
        except Exception as e:
            report['overall_errors'].append(f"生成验证报告时出错: {str(e)}")
        
        return report
    
    def _print_validation_summary(self, validation_report: Dict):
        """
        打印数据一致性验证总结
        
        Args:
            validation_report: 验证报告字典
        """
        try:
            print("\n" + "="*80)
            print("📊 数据一致性验证总结")
            print("="*80)
            
            # 基本统计
            print(f"📂 文件夹统计:")
            print(f"   总文件夹数: {validation_report['total_folders']}")
            print(f"   验证通过: {validation_report['consistent_folders']} 个")
            print(f"   验证失败: {validation_report['inconsistent_folders']} 个")
            
            # 数据统计
            print(f"\n📊 数据统计:")
            print(f"   总图片数: {validation_report['total_images']}")
            print(f"   总人员数: {validation_report['total_persons']}")
            print(f"   总金额: {validation_report['total_amount']:.2f} 元")
            
            # 一致性状态
            if validation_report['inconsistent_folders'] == 0:
                print(f"\n✅ 数据一致性验证: 全部通过")
                print(f"   所有 {validation_report['consistent_folders']} 个文件夹的数据都一致")
            else:
                print(f"\n⚠️  数据一致性验证: 发现问题")
                print(f"   {validation_report['inconsistent_folders']} 个文件夹存在数据不一致")
                
                # 显示详细错误
                if validation_report['overall_errors']:
                    print(f"\n❌ 详细问题列表:")
                    for i, error in enumerate(validation_report['overall_errors'][:10], 1):  # 只显示前10个
                        print(f"   {i:2d}. {error}")
                    
                    if len(validation_report['overall_errors']) > 10:
                        print(f"   ... 还有 {len(validation_report['overall_errors']) - 10} 个其他问题")
            
            # 文件夹详情（只显示有问题的）
            if validation_report['inconsistent_folders'] > 0:
                print(f"\n📋 问题文件夹详情:")
                problem_count = 0
                for folder_name, details in validation_report['folder_details'].items():
                    if not details['is_consistent'] and problem_count < 5:  # 只显示前5个问题文件夹
                        problem_count += 1
                        print(f"   📁 {folder_name}:")
                        print(f"      图片数: {details['image_count']}, 人员数: {details['person_count']}, 金额: {details['total_amount']:.2f}")
                        for error in details['errors']:
                            print(f"      ❌ {error}")
                        print()
                
                if validation_report['inconsistent_folders'] > 5:
                    print(f"   ... 还有 {validation_report['inconsistent_folders'] - 5} 个其他问题文件夹")
            
            print("="*80)
            
            # 记录到日志
            if validation_report['inconsistent_folders'] == 0:
                logger.info("✅ 整体数据一致性验证通过")
            else:
                logger.warning(f"⚠️  发现 {validation_report['inconsistent_folders']} 个文件夹存在数据不一致")
                for error in validation_report['overall_errors']:
                    logger.warning(f"数据不一致: {error}")
            
        except Exception as e:
            print(f"❌ 打印验证总结时出错: {str(e)}")
            logger.error(f"打印验证总结失败: {str(e)}")

    def save_to_excel(self, output_path: str = None) -> str:
        """
        将结果保存到Excel文件

        Args:
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"还款凭证解析_{timestamp}.xlsx"

        try:
            df = pd.DataFrame(self.results, columns=self.excel_columns)
            df.to_excel(output_path, index=False, engine='openpyxl')
            logger.info(f"结果已保存到: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"保存Excel失败: {str(e)}")
            raise

    def run(self) -> str:
        """
        运行处理流程（增强版）- 每个子文件夹生成单独的Excel文件

        Returns:
            生成的Excel文件列表字符串
        """
        if not self.base_folder.exists():
            raise FileNotFoundError(f"指定的文件夹不存在: {self.base_folder}")

        logger.info(f"开始处理文件夹: {self.base_folder}")
        
        # 用于记录所有生成的Excel文件
        generated_files = []
        overall_directory_errors = []

        # 处理所有二级目录 - 每个目录单独生成Excel
        for item in self.base_folder.iterdir():
            if item.is_dir():
                directory_name = item.name
                logger.info(f"=" * 80)
                logger.info(f"🗂️  开始处理子文件夹: {directory_name}")
                
                # 为每个子文件夹单独处理
                excel_file = self._process_single_directory(item)
                if excel_file:
                    generated_files.append(excel_file)
                    logger.info(f"✅ 子文件夹 {directory_name} 处理完成，生成文件: {excel_file}")
                else:
                    logger.warning(f"⚠️ 子文件夹 {directory_name} 处理失败")
                    
        logger.info(f"=" * 80)
        logger.info(f"🎯 全部处理完成！")
        logger.info(f"📋 共处理 {len([d for d in self.base_folder.iterdir() if d.is_dir()])} 个子文件夹")
        logger.info(f"📄 成功生成 {len(generated_files)} 个Excel文件")
        
        if generated_files:
            logger.info(f"📄 生成的文件列表:")
            for file in generated_files:
                logger.info(f"   - {file}")
            return f"成功生成 {len(generated_files)} 个Excel文件: " + ", ".join(generated_files)
        else:
            return "未生成任何Excel文件"

    def _process_single_directory(self, directory: Path) -> str:
        """
        处理单个子文件夹并生成独立的Excel文件
        
        Args:
            directory: 子文件夹路径
            
        Returns:
            生成的Excel文件路径，失败时返回None
        """
        directory_name = directory.name
        
        # 重置数据结构，为单个目录处理做准备
        self.results = []
        self.reconciliation_data = []
        self.directory_stats = {}
        self.missing_debtor_names = set()
        
        directory_errors = []
        
        try:
            # 1. 验证Excel文件唯一性
            excel_validation = self._validate_directory_excel_files(directory)
            
            if excel_validation.startswith("❌"):
                # Excel文件验证失败，记录错误但继续处理图片
                directory_errors.append({
                    'directory_name': directory_name,
                    'error_type': 'Excel文件验证失败',
                    'error_message': excel_validation,
                    'status': '部分失败'
                })
                logger.warning(f"目录 {directory_name} Excel验证失败: {excel_validation}")
                logger.info(f"📸 继续处理目录 {directory_name} 中的图片文件...")
                
                # 即使Excel验证失败也记录目录信息
                self.directory_stats[directory_name] = {
                    'excel_file': '验证失败',
                    'processed_images': 0,
                    'status': '部分失败',
                    'error_message': excel_validation
                }
            else:
                # Excel文件验证成功，记录信息和扫描还款对账明细
                self.directory_stats[directory_name] = {
                    'excel_file': excel_validation,
                    'processed_images': 0,
                    'status': '成功',
                    'error_message': ''
                }
                logger.info(f"目录 {directory_name} Excel验证成功")
                
                # 扫描还款对账明细
                try:
                    reconciliation_records = self._scan_reconciliation_excel(directory)
                    self.reconciliation_data.extend(reconciliation_records)
                    logger.info(f"🗂️ 扫描还款对账明细: {len(reconciliation_records)} 条记录")
                except Exception as e:
                    logger.error(f"❌ 扫描还款对账明细失败: {e}")
            
            # 2. 处理目录中的图片文件（无论Excel验证是否成功）
            images_before = len(self.results)
            self.process_directory(directory, directory_name)
            images_after = len(self.results)
            
            # 更新统计信息
            processed_images = images_after - images_before
            self.directory_stats[directory_name]['processed_images'] = processed_images
            
            if processed_images > 0:
                logger.info(f"目录 {directory_name} 处理完成，共处理 {processed_images} 个图片")
                # 如果之前是部分失败，但图片处理成功，更新状态
                if self.directory_stats[directory_name]['status'] == '部分失败':
                    self.directory_stats[directory_name]['status'] = '图片处理成功'
            else:
                logger.warning(f"目录 {directory_name} 中没有找到可处理的图片文件")
            
            # 3. 为该目录生成独立的Excel文件
            if self.results or self.reconciliation_data:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"{directory_name}_还款凭证解析_{timestamp}.xlsx"
                
                try:
                    excel_file = self.save_to_excel_enhanced(directory_errors, output_filename)
                    logger.info(f"✅ 成功生成Excel文件: {excel_file}")
                    return excel_file
                except Exception as e:
                    logger.error(f"❌ 保存Excel文件失败: {e}")
                    return None
            else:
                logger.warning(f"⚠️ 目录 {directory_name} 没有处理结果，跳过Excel生成")
                return None
                
        except Exception as e:
            # 记录处理异常
            error_msg = f"处理异常: {str(e)}"
            directory_errors.append({
                'directory_name': directory_name,
                'error_type': '处理异常',
                'error_message': error_msg,
                'status': '失败'
            })
            logger.error(f"目录 {directory_name} 处理异常: {error_msg}")
            return None

    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if hasattr(self, 'debtor_db') and self.debtor_db:
                self.debtor_db.close()
        except Exception:
            # 忽略析构时的异常
            pass

    def _validate_with_payment_excel(self, folder_name: str, ocr_results: List[Dict]) -> Dict:
        """
        与还款对账明细Excel文件对比验证识别准确性
        
        Args:
            folder_name: 文件夹名称
            ocr_results: OCR识别结果列表
            
        Returns:
            验证结果字典
        """
        validation_result = {
            'is_valid': True,
            'matched_count': 0,
            'unmatched_count': 0,
            'errors': [],
            'unmatched_records': []
        }
        
        try:
            # 查找还款对账明细Excel文件
            folder_path = self.base_folder / folder_name
            excel_files = []
            
            # 查找包含"还款"和"明细"关键词的Excel文件
            for file in folder_path.glob("*.xlsx"):
                if "还款" in file.name and "明细" in file.name:
                    excel_files.append(file)
            for file in folder_path.glob("*.xls"):
                if "还款" in file.name and "明细" in file.name:
                    excel_files.append(file)
            
            if not excel_files:
                # 如果没有找到特定名称的文件，使用任何Excel文件
                excel_files = list(folder_path.glob("*.xlsx")) + list(folder_path.glob("*.xls"))
            
            if not excel_files:
                validation_result['errors'].append("未找到还款对账明细Excel文件")
                validation_result['is_valid'] = False
                return validation_result
            
            # 读取Excel文件
            excel_file = excel_files[0]
            df = pd.read_excel(excel_file)
            logger.info(f"📋 读取还款对账明细文件: {excel_file.name}")
            
            # 从Excel中提取还款人信息
            excel_payers = set()
            name_columns = ['姓名', '债务人姓名', '还款人姓名', '还款人', '客户姓名', 'name']
            
            for col in name_columns:
                if col in df.columns:
                    names = df[col].dropna().astype(str).str.strip()
                    excel_payers.update(names)
                    break
            
            # 验证OCR识别的还款人
            for result in ocr_results:
                ocr_payer = result.get('还款人', '').strip()
                if not ocr_payer:
                    continue
                
                # 检查是否在Excel中
                if ocr_payer in excel_payers:
                    validation_result['matched_count'] += 1
                    # logger.debug(f"✅ 还款人匹配成功: {ocr_payer}")
                else:
                    # 尝试模糊匹配（处理部分遮挡的姓名）
                    matched = False
                    for excel_payer in excel_payers:
                        # 如果OCR识别的姓名包含*，进行模糊匹配
                        if '*' in ocr_payer:
                            # 将*替换为.进行正则匹配
                            pattern = ocr_payer.replace('*', '.')
                            if re.match(pattern, excel_payer):
                                matched = True
                                validation_result['matched_count'] += 1
                                # logger.debug(f"✅ 还款人模糊匹配成功: {ocr_payer} -> {excel_payer}")
                                break
                        # 检查是否为子串
                        elif ocr_payer in excel_payer or excel_payer in ocr_payer:
                            matched = True
                            validation_result['matched_count'] += 1
                            # logger.debug(f"✅ 还款人部分匹配成功: {ocr_payer} -> {excel_payer}")
                            break
                    
                    if not matched:
                        validation_result['unmatched_count'] += 1
                        validation_result['unmatched_records'].append({
                            'file': result.get('完整文件路径', ''),
                            'payer': ocr_payer,
                            'amount': result.get('图片OCR识别金额', ''),
                            'time': result.get('还款时间', '')
                        })
                        logger.warning(f"❌ 还款人未在Excel中找到: {ocr_payer}")
            
            # 设置验证结果
            if validation_result['unmatched_count'] > 0:
                validation_result['is_valid'] = False
                validation_result['errors'].append(
                    f"有 {validation_result['unmatched_count']} 个还款人未在Excel中找到"
                )
            
            logger.info(f"📊 Excel验证完成 - 匹配: {validation_result['matched_count']}, 未匹配: {validation_result['unmatched_count']}")
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"Excel验证过程出错: {str(e)}")
            logger.error(f"Excel验证失败: {str(e)}")
        
        return validation_result
    
    def save_to_excel_enhanced(self, directory_errors: List[Dict], output_filename: str = None) -> str:
        """
        将结果保存到Excel文件，包含多个sheet，并对未找到债务人信息的记录标红
        
        Args:
            directory_errors: 目录处理错误信息列表
            output_filename: 输出文件名（可选），如果不提供则使用默认格式
            
        Returns:
            保存的文件路径
        """
        # 🔧 修正：允许没有图片处理结果时也能保存Excel（至少包含还款对账明细）
        if not self.results and not self.reconciliation_data:
            raise ValueError("没有处理结果和还款对账明细可以保存")

        # 生成输出文件名
        if output_filename:
            output_path = output_filename
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"还款凭证解析_增强版_{timestamp}.xlsx"

        try:
            # 创建Excel写入器
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                
                # 1. 主要处理结果sheet（在生成前先设置结清验证状态）
                if self.results:
                    # 先执行结清验证分析以获取验证状态
                    settlement_analysis = self._get_settlement_analysis_for_results()
                    
                    # 为每条记录设置结清验证状态
                    self._set_settlement_validation_status(settlement_analysis)
                    
                    main_df = pd.DataFrame(self.results, columns=self.excel_columns)
                    main_df.to_excel(writer, sheet_name='处理结果明细', index=False)
                    logger.info(f"写入主要处理结果: {len(main_df)} 条记录")
                else:
                    # 创建空的处理结果sheet
                    empty_df = pd.DataFrame(columns=self.excel_columns)
                    empty_df.to_excel(writer, sheet_name='处理结果明细', index=False)
                    logger.info("写入空的处理结果明细（没有图片处理结果）")
                
                # 🆕 2. 还款对账明细sheet
                if self.reconciliation_data:
                    reconciliation_columns = [
                        '文件夹名称', '还款时间', '债务人姓名', '合同号', '还款金额', '类型', '身份证号','地区',
                        'debtor_no', 'collector_user_name', 'total_principal', 'remaining_principal', 
                        '图片文件数量', '匹配方式', 'Excel文件', '行号'
                    ]
                    
                    reconciliation_df = pd.DataFrame(self.reconciliation_data, columns=reconciliation_columns)
                    reconciliation_df.to_excel(writer, sheet_name='还款对账明细', index=False)
                    logger.info(f"写入还款对账明细: {len(reconciliation_df)} 条记录")
                    
                    # 🆕 3. 执行三重比对分析并生成比对结果sheet
                    if self.results:  # 只有在有OCR结果时才执行比对分析
                        analysis_result = self._perform_reconciliation_analysis()
                    else:
                        # 没有OCR结果时，生成简化的分析结果
                        analysis_result = {
                            '姓名匹配分析': [],
                            '金额匹配分析': [],
                            '结清验证分析': [],
                            '总体统计': {
                                '还款对账明细债务人数': len(set(r.get('debtor_no', '') for r in self.reconciliation_data if r.get('debtor_no', ''))),
                                '处理结果明细债务人数': 0,
                                '共同债务人数': 0,
                                '说明': '没有图片处理结果，无法进行完整比对分析'
                            }
                        }
                        logger.info("📊 没有OCR结果，生成简化的比对分析")
                    
                    if analysis_result:
                        # 3.1 姓名匹配分析sheet
                        if analysis_result['姓名匹配分析']:
                            name_analysis_df = pd.DataFrame(analysis_result['姓名匹配分析'])
                            name_analysis_df.to_excel(writer, sheet_name='姓名匹配分析', index=False)
                            logger.info(f"写入姓名匹配分析: {len(name_analysis_df)} 条记录")
                        
                        # 3.2 金额匹配分析sheet
                        if analysis_result['金额匹配分析']:
                            amount_analysis_df = pd.DataFrame(analysis_result['金额匹配分析'])
                            amount_analysis_df.to_excel(writer, sheet_name='金额匹配分析', index=False)
                            logger.info(f"写入金额匹配分析: {len(amount_analysis_df)} 条记录")
                        
                        # 3.3 结清验证分析sheet
                        if analysis_result['结清验证分析']:
                            settlement_analysis_df = pd.DataFrame(analysis_result['结清验证分析'])
                            settlement_analysis_df.to_excel(writer, sheet_name='结清验证分析', index=False)
                            logger.info(f"写入结清验证分析: {len(settlement_analysis_df)} 条记录")
                        
                        # 3.4 比对分析摘要sheet
                        if analysis_result['总体统计']:
                            summary_data = []
                            for key, value in analysis_result['总体统计'].items():
                                summary_data.append({'统计项目': key, '数值': value})
                            
                            summary_df = pd.DataFrame(summary_data)
                            summary_df.to_excel(writer, sheet_name='比对分析摘要', index=False)
                            logger.info(f"写入比对分析摘要: {len(summary_df)} 条记录")
                
                # 4. 生成人名统计信息
                person_stats = self._generate_person_statistics()
                not_found_records = []  # 记录未找到债务人信息的记录
                
                if person_stats:
                    person_columns = [
                        '二级目录名称', '债务人姓名', '还款时间', '合同号', '身份证号', 
                        '还款金额总计', 'debtor_no', 'collecter_user_name', '凭证数量'
                    ]
                    
                    person_data = []
                    for stats in person_stats:
                        # 检查是否找到债务人信息
                        if not stats['debtor_no']:
                            not_found_records.append({
                                'name': stats['name'],
                                'folder': stats['folder_name'],
                                'contract_no': stats['contract_no'],
                                'id_no': stats['full_id_no']
                            })
                        
                        person_data.append({
                            '二级目录名称': stats['folder_name'],
                            '债务人姓名': stats['name'],
                            '还款时间': stats['payment_time'],
                            '合同号': stats['contract_no'],
                            '身份证号': stats['full_id_no'],  # 强制文本格式
                            '还款金额总计': round(stats['total_amount'], 2),
                            'debtor_no': stats['debtor_no'],
                            'collecter_user_name': stats['collecter_user_name'],
                            '凭证数量': stats['voucher_count']
                        })
                    
                    person_df = pd.DataFrame(person_data, columns=person_columns)
                    person_df.to_excel(writer, sheet_name='按人名统计', index=False)
                    logger.info(f"写入人名统计信息: {len(person_df)} 条记录")
                    
                    # 5. 应用格式设置（身份证号文本格式 + 标红未找到的记录 + 比对分析颜色标记）
                    self._apply_excel_formatting(writer, person_df, not_found_records)
                
                # 🆕 6. 对还款对账明细sheet进行格式化（图片文件数量为0的行标红）
                if self.reconciliation_data:
                    self._format_reconciliation_sheet(writer, reconciliation_df)
                
                # 3. 二级目录处理统计
                directory_data = []
                for dir_name, stats in self.directory_stats.items():
                    directory_data.append({
                        '目录名称': dir_name,
                        'Excel文件路径': stats['excel_file'],
                        '处理图片数量': stats['processed_images'],
                        '处理状态': stats['status'],
                        '错误信息': stats.get('error_message', '')
                    })
                
                # 添加失败的目录
                for error in directory_errors:
                    directory_data.append({
                        '目录名称': error['directory_name'],
                        'Excel文件路径': '',
                        '处理图片数量': 0,
                        '处理状态': error['status'],
                        '错误信息': error['error_message']
                    })
                
                if directory_data:
                    dir_columns = ['目录名称', 'Excel文件路径', '处理图片数量', '处理状态', '错误信息']
                    dir_df = pd.DataFrame(directory_data, columns=dir_columns)
                    dir_df.to_excel(writer, sheet_name='二级目录统计', index=False)
                    logger.info(f"写入目录统计信息: {len(dir_df)} 条记录")
                
                # 4. 处理统计摘要
                summary_data = {
                    '统计项目': [
                        '总二级目录数',
                        '成功处理目录数',
                        '失败处理目录数',
                        '总图片文件数',
                        '成功解析文件数',
                        '账户识别成功数',
                        '不同还款人数',
                        '未找到债务人信息数'
                    ],
                    '数量': [
                        len([d for d in self.base_folder.iterdir() if d.is_dir()]),
                        len(self.directory_stats),
                        len(directory_errors),
                        len(self.results),
                        len([r for r in self.results if r.get('还款人')]),
                        len([r for r in self.results if r.get('还款账号') and r.get('还款账号') != '0000']),
                        len(person_stats) if person_stats else 0,
                        len(not_found_records)
                    ]
                }
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='处理摘要', index=False)
                logger.info("写入处理摘要信息")
                
                # 5. 输出未找到债务人信息的记录摘要
                if not_found_records:
                    logger.info(f"⚠️  共有 {len(not_found_records)} 个记录未找到债务人信息")
                    self._log_not_found_records(not_found_records)

            logger.info(f"增强版Excel文件保存成功: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"保存增强版Excel文件失败: {str(e)}")
            raise
    
    def _apply_excel_formatting(self, writer, person_df, not_found_records):
        """
        应用Excel格式设置：文件路径超链接 + 身份证号文本格式 + 标红不匹配记录 (增强版)
        
        Args:
            writer: Excel写入器
            person_df: 人名统计DataFrame
            not_found_records: 未找到债务人信息的记录列表
        """
        try:
            from openpyxl.styles import PatternFill, Font
            from openpyxl.utils.dataframe import dataframe_to_rows
            from openpyxl import Workbook
            import os
            
            # 定义样式
            red_fill = PatternFill(start_color='FFCCCB', end_color='FFCCCB', fill_type='solid')  # 浅红色背景
            red_font = Font(color='CC0000', bold=True)  # 红色字体
            orange_fill = PatternFill(start_color='FFE4B5', end_color='FFE4B5', fill_type='solid')  # 橙色背景（部分一致）
            yellow_fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')  # 黄色背景（OCR失败）
            yellow_font = Font(color='996600', bold=True)  # 黄色字体
            blue_font = Font(color='0000FF', underline='single')  # 蓝色超链接字体
            
            # 1. 处理"处理结果明细"sheet - 增强版
            if '处理结果明细' in writer.sheets:
                ws_detail = writer.sheets['处理结果明细']
                
                # 找到关键列的索引
                file_path_col_idx = None
                ocr_match_status_col_idx = None
                ocr_accuracy_col_idx = None
                payer_col_idx = None
                settlement_status_col_idx = None
                
                for col_idx, col_name in enumerate(self.excel_columns, 1):
                    if col_name == '完整文件路径':
                        file_path_col_idx = col_idx
                    elif col_name == 'OCR文件名匹配状态':
                        ocr_match_status_col_idx = col_idx
                    elif col_name == 'OCR准确度评估':
                        ocr_accuracy_col_idx = col_idx
                    elif col_name == '还款人':
                        payer_col_idx = col_idx
                    elif col_name == '结清验证状态':
                        settlement_status_col_idx = col_idx
                
                logger.info("🎨 开始应用Excel格式设置...")
                logger.info(f"   文件路径列索引: {file_path_col_idx}")
                logger.info(f"   总行数: {ws_detail.max_row}")
                
                # 🔗 第一步：先为所有行设置文件路径超链接
                if file_path_col_idx:
                    logger.info("🔗 正在设置文件路径超链接...")
                    hyperlink_count = 0
                    
                    for row_idx in range(2, ws_detail.max_row + 1):  # 从第2行开始（跳过标题）
                        file_path_cell = ws_detail.cell(row=row_idx, column=file_path_col_idx)
                        file_path = file_path_cell.value
                        
                        if file_path:
                            # 清理文件路径，处理可能的编码问题
                            file_path = str(file_path).strip()
                            
                            if os.path.exists(file_path):
                                try:
                                    # 转换为可点击的超链接
                                    # 处理Windows路径和特殊字符
                                    normalized_path = os.path.normpath(file_path).replace('\\', '/')
                                    file_path_cell.hyperlink = f"file:///{normalized_path}"
                                    file_path_cell.font = blue_font
                                    hyperlink_count += 1
                                    # logger.debug(f"   设置超链接: 行{row_idx}, 路径: {file_path}")
                                except Exception as e:
                                    logger.warning(f"   设置超链接失败 行{row_idx}: {e}")
                            else:
                                pass
                                # logger.debug(f"   文件不存在: 行{row_idx}, 路径: {file_path}")
                        else:
                            pass
                            # logger.debug(f"   文件路径为空: 行{row_idx}")
                    
                    logger.info(f"✅ 文件路径超链接设置完成，共设置 {hyperlink_count} 个超链接")
                
                # 🎨 第二步：设置行颜色标记（保持超链接不变）
                logger.info("🎨 正在设置行颜色标记...")
                
                for row_idx in range(2, ws_detail.max_row + 1):  # 从第2行开始（跳过标题）
                    match_status = None
                    
                    # 获取OCR匹配状态
                    if ocr_match_status_col_idx:
                        match_status_cell = ws_detail.cell(row=row_idx, column=ocr_match_status_col_idx)
                        match_status = match_status_cell.value
                    
                    # 🟡 步骤2.1: 优先检查黄色标识（文件夹中有人名但还款对账明细中没有记录）
                    needs_yellow_highlight = False
                    if file_path_col_idx:
                        file_path = ws_detail.cell(row=row_idx, column=file_path_col_idx).value
                        # 检查是否需要黄色标识
                        for result in self.results:
                            if (result.get('_需要黄色标识', False) and 
                                result.get('完整文件路径') == file_path):
                                needs_yellow_highlight = True
                                logger.info(f"🟡 应用黄色标识: {result.get('_黄色标识原因', '未知原因')}")
                                break
                    
                    # 根据优先级设置背景色（黄色标识优先级最高）
                    if needs_yellow_highlight:
                        # 🟡 标黄整行，表示文件夹中有人名但还款对账明细中没有记录
                        for col in range(1, len(self.excel_columns) + 1):
                            cell = ws_detail.cell(row=row_idx, column=col)
                            cell.fill = yellow_fill
                            
                            # 对关键列使用黄色字体，但文件路径列保持超链接样式
                            if col in [payer_col_idx, ocr_match_status_col_idx, ocr_accuracy_col_idx]:
                                cell.font = yellow_font
                            elif col == file_path_col_idx and cell.hyperlink:
                                # 保持超链接样式，但添加黄色背景
                                cell.font = blue_font  # 保持蓝色超链接字体
                                
                    elif match_status == 'OCR失败':
                        # 标黄整行，表示OCR识别失败
                        for col in range(1, len(self.excel_columns) + 1):
                            cell = ws_detail.cell(row=row_idx, column=col)
                            cell.fill = yellow_fill
                            
                            # 对关键列使用黄色字体，但文件路径列保持超链接样式
                            if col in [payer_col_idx, ocr_match_status_col_idx, ocr_accuracy_col_idx]:
                                cell.font = yellow_font
                            elif col == file_path_col_idx and cell.hyperlink:
                                # 保持超链接样式，但添加黄色背景
                                cell.font = blue_font  # 保持蓝色超链接字体
                                
                    elif match_status == '不一致':
                        # 标红整行，但保持文件路径列的超链接
                        for col in range(1, len(self.excel_columns) + 1):
                            cell = ws_detail.cell(row=row_idx, column=col)
                            cell.fill = red_fill
                            
                            # 对关键列使用红色字体，但文件路径列保持超链接样式
                            if col in [payer_col_idx, ocr_match_status_col_idx, ocr_accuracy_col_idx]:
                                cell.font = red_font
                            elif col == file_path_col_idx and cell.hyperlink:
                                # 保持超链接样式，但添加红色背景
                                cell.font = blue_font  # 保持蓝色超链接字体
                                
                    elif match_status == '部分一致':
                        # 标橙整行，但保持文件路径列的超链接
                        for col in range(1, len(self.excel_columns) + 1):
                            cell = ws_detail.cell(row=row_idx, column=col)
                            cell.fill = orange_fill
                            
                            # 文件路径列保持超链接样式
                            if col == file_path_col_idx and cell.hyperlink:
                                cell.font = blue_font  # 保持蓝色超链接字体
                    
                    # 🔍 步骤3: 检查是否有其他标红需求（兼容原有功能）
                    if file_path_col_idx and not needs_yellow_highlight and match_status not in ['OCR失败', '不一致', '部分一致']:
                        file_path = ws_detail.cell(row=row_idx, column=file_path_col_idx).value
                        
                        # 检查是否需要标红（未找到债务人信息等）
                        for result in self.results:
                            if (result.get('_需要标红', False) and 
                                result.get('完整文件路径') == file_path):
                                
                                # 应用原有的标红逻辑，但保持超链接
                                for col in range(1, len(self.excel_columns) + 1):
                                    cell = ws_detail.cell(row=row_idx, column=col)
                                    cell.fill = red_fill
                                    
                                    if col == payer_col_idx:
                                        cell.font = red_font
                                    elif col == file_path_col_idx and cell.hyperlink:
                                        cell.font = blue_font  # 保持超链接样式
                                break
                
                logger.info(f"✅ '处理结果明细'sheet格式设置完成")
            
            # 2. 处理"按人名统计"sheet
            ws = writer.sheets['按人名统计']
            
            # 1. 设置身份证号列为文本格式
            id_col_index = None
            debtor_no_col_index = None
            name_col_index = None
            
            # 找到相关列的索引
            for idx, col_name in enumerate(person_df.columns, 1):
                if col_name == '身份证号':
                    id_col_index = idx
                elif col_name == 'debtor_no':
                    debtor_no_col_index = idx
                elif col_name == '债务人姓名':
                    name_col_index = idx
            
            # 2. 强制身份证号列为文本格式
            if id_col_index:
                for row in range(2, len(person_df) + 2):  # 从第2行开始（跳过标题）
                    cell = ws.cell(row=row, column=id_col_index)
                    if cell.value:
                        # 强制设置为文本格式
                        cell.number_format = '@'  # 文本格式
                        # 如果是数字，转换为字符串
                        if isinstance(cell.value, (int, float)):
                            cell.value = str(int(cell.value))
            
            # 3. 标红未找到债务人信息的行
            if not_found_records and debtor_no_col_index:
                
                # 创建未找到记录的姓名集合
                not_found_names = {record['name'] for record in not_found_records}
                
                for row in range(2, len(person_df) + 2):  # 从第2行开始
                    debtor_no_cell = ws.cell(row=row, column=debtor_no_col_index)
                    name_cell = ws.cell(row=row, column=name_col_index) if name_col_index else None
                    
                    # 如果debtor_no为空或者姓名在未找到列表中
                    if (not debtor_no_cell.value or debtor_no_cell.value == '') and \
                       name_cell and name_cell.value in not_found_names:
                        # 标红整行
                        for col in range(1, len(person_df.columns) + 1):
                            cell = ws.cell(row=row, column=col)
                            cell.fill = red_fill
                            if col in [name_col_index, debtor_no_col_index]:
                                cell.font = red_font
            
            # 4. 单独处理结清验证状态列的格式化
            if settlement_status_col_idx:
                logger.info("🔴 正在设置结清验证状态列格式...")
                settlement_formatted_count = 0
                
                for row_idx in range(2, ws_detail.max_row + 1):  # 从第2行开始（跳过标题）
                    # 检查是否有结清验证问题
                    has_settlement_issue = False
                    if file_path_col_idx:
                        file_path = ws_detail.cell(row=row_idx, column=file_path_col_idx).value
                        # 查找对应的结果记录
                        for result in self.results:
                            if (result.get('完整文件路径') == file_path and 
                                result.get('_结清验证问题', False)):
                                has_settlement_issue = True
                                break
                    
                    # 如果有结清验证问题，为该列单元格设置红色
                    if has_settlement_issue:
                        settlement_cell = ws_detail.cell(row=row_idx, column=settlement_status_col_idx)
                        if settlement_cell.value:  # 只有当单元格有内容时才设置格式
                            settlement_cell.fill = red_fill
                            settlement_cell.font = red_font
                            settlement_formatted_count += 1
                
                logger.info(f"✅ 结清验证状态列格式化完成：{settlement_formatted_count} 个单元格标红")
            
            logger.info("✅ Excel格式设置完成：文件路径超链接 + 身份证号文本格式 + OCR匹配状态标色 + 未找到记录标红 + 结清验证状态标红")
            
        except Exception as e:
            logger.warning(f"Excel格式设置失败（不影响数据）: {str(e)}")
    
    def _format_reconciliation_sheet(self, writer, reconciliation_df):
        """
        格式化还款对账明细sheet：图片文件数量为0的行标红，结清验证问题行标橙
        
        Args:
            writer: Excel写入器
            reconciliation_df: 还款对账明细DataFrame
        """
        try:
            from openpyxl.styles import PatternFill, Font
            
            # 定义填充样式
            red_fill = PatternFill(start_color='FFCCCB', end_color='FFCCCB', fill_type='solid')  # 浅红色背景（图片文件数量为0）
            red_font = Font(color='CC0000', bold=True)  # 红色字体
            orange_fill = PatternFill(start_color='FFE4B5', end_color='FFE4B5', fill_type='solid')  # 橙色背景（结清验证问题）
            orange_font = Font(color='FF6600', bold=True)  # 橙色字体
            
            # 获取还款对账明细sheet
            if '还款对账明细' not in writer.sheets:
                logger.warning("未找到'还款对账明细'sheet，跳过格式化")
                return
            
            ws = writer.sheets['还款对账明细']
            
            # 找到关键列的索引
            image_count_col_idx = None
            debtor_no_col_idx = None
            payment_type_col_idx = None
            payment_amount_col_idx = None
            debtor_name_col_idx = None
            header_row = 1  # 标题行
            
            for col_idx, cell in enumerate(ws[header_row], 1):
                if cell.value:
                    col_value = str(cell.value)
                    if '图片文件数量' in col_value:
                        image_count_col_idx = col_idx
                    elif 'debtor_no' in col_value:
                        debtor_no_col_idx = col_idx
                    elif '类型' in col_value:
                        payment_type_col_idx = col_idx
                    elif '还款金额' in col_value:
                        payment_amount_col_idx = col_idx
                    elif '债务人姓名' in col_value:
                        debtor_name_col_idx = col_idx
            
            if image_count_col_idx is None:
                logger.warning("未找到'图片文件数量'列，跳过图片文件格式化")
            
            logger.info(f"📍 找到关键列 - 图片文件数量: {image_count_col_idx}, debtor_no: {debtor_no_col_idx}, 类型: {payment_type_col_idx}")
            
            # 获取结清验证分析结果
            settlement_issues = {}  # {debtor_no: 问题描述}
            if hasattr(self, 'reconciliation_analysis') and self.reconciliation_analysis:
                for issue in self.reconciliation_analysis.get('结清验证分析', []):
                    if issue.get('需要关注') == '是':
                        debtor_no = issue.get('debtor_no')
                        if debtor_no:
                            settlement_issues[debtor_no] = issue.get('说明', '结清验证异常')
            
            # 记录需要标识的行数
            red_highlighted_rows = 0  # 图片文件数量为0
            orange_highlighted_rows = 0  # 结清验证问题
            
            # 遍历数据行（从第2行开始，第1行是标题）
            for row_idx in range(2, len(reconciliation_df) + 2):  # +2因为Excel从1开始且有标题行
                # 获取当前行的关键信息
                image_count = None
                debtor_no = None
                debtor_name = ''
                
                if image_count_col_idx:
                    image_count_cell = ws.cell(row=row_idx, column=image_count_col_idx)
                    image_count = image_count_cell.value
                
                if debtor_no_col_idx:
                    debtor_no_cell = ws.cell(row=row_idx, column=debtor_no_col_idx)
                    debtor_no = debtor_no_cell.value
                
                if debtor_name_col_idx:
                    debtor_name_cell = ws.cell(row=row_idx, column=debtor_name_col_idx)
                    debtor_name = debtor_name_cell.value or ''
                
                # 确定行的标识颜色（优先级：红色 > 橙色）
                needs_red = image_count_col_idx and (image_count == 0 or image_count == '0')
                needs_orange = debtor_no and debtor_no in settlement_issues
                
                if needs_red:
                    # 图片文件数量为0，标红
                    for col_idx in range(1, len(reconciliation_df.columns) + 1):
                        cell = ws.cell(row=row_idx, column=col_idx)
                        cell.fill = red_fill
                        cell.font = red_font
                    
                    red_highlighted_rows += 1
                    logger.info(f"   🔴 标红第{row_idx}行: {debtor_name} (图片文件数量为0)")
                    
                elif needs_orange:
                    # 结清验证有问题，标橙
                    for col_idx in range(1, len(reconciliation_df.columns) + 1):
                        cell = ws.cell(row=row_idx, column=col_idx)
                        cell.fill = orange_fill
                        cell.font = orange_font
                    
                    orange_highlighted_rows += 1
                    issue_desc = settlement_issues[debtor_no]
                    logger.info(f"   🟠 标橙第{row_idx}行: {debtor_name} ({issue_desc})")
            
            logger.info(f"✅ 还款对账明细格式化完成")
            logger.info(f"   🔴 图片文件缺失标红: {red_highlighted_rows} 行")
            logger.info(f"   🟠 结清验证问题标橙: {orange_highlighted_rows} 行")
            
            if red_highlighted_rows > 0:
                logger.warning(f"⚠️ 发现 {red_highlighted_rows} 条记录没有找到对应的图片文件，已在Excel中标红显示")
                logger.warning("   💡 这些记录可能需要检查：Excel中有还款记录但缺少凭证图片")
            
            if orange_highlighted_rows > 0:
                logger.warning(f"⚠️ 发现 {orange_highlighted_rows} 条记录存在结清验证问题，已在Excel中标橙显示")
                logger.warning("   💡 这些记录可能需要检查：标记为结清但还款金额小于剩余本金")
            
        except Exception as e:
            logger.warning(f"还款对账明细格式化失败（不影响数据）: {str(e)}")
    
    def _log_not_found_records(self, not_found_records):
        """
        记录未找到债务人信息的详细信息
        
        Args:
            not_found_records: 未找到债务人信息的记录列表
        """
        logger.error("=" * 60)
        logger.error("❌ 以下记录未找到债务人信息（已在Excel中标红）:")
        logger.error("=" * 60)
        
        for i, record in enumerate(not_found_records, 1):
            logger.error(f"{i:3d}. 【{record['name']}】")
            logger.error(f"     文件夹: {record['folder']}")
            if record['id_no']:
                logger.error(f"     身份证号: {record['id_no']}")
            if record['contract_no']:
                logger.error(f"     合同号: {record['contract_no']}")
            logger.error("")
        
        logger.error("=" * 60)
        logger.error(f"💡 建议检查以上 {len(not_found_records)} 个记录的身份证号和合同号是否正确")
        logger.error("💡 或确认这些债务人是否在债务人明细Excel文件中")
        logger.error("=" * 60)

    def _validate_directory_excel_files(self, directory_path: Path) -> str:
        """
        验证二级目录中Excel文件的唯一性
        
        Args:
            directory_path: 二级目录路径
            
        Returns:
            Excel文件路径，如果有问题返回错误信息
        """
        try:
            # 🔧 扫描Excel文件并过滤临时文件
            all_excel_files = list(directory_path.glob("*.xlsx")) + list(directory_path.glob("*.xls"))
            excel_files = [f for f in all_excel_files if not f.name.startswith('~$')]
            
            if len(excel_files) == 0:
                if len(all_excel_files) > 0:
                    # 有Excel文件但都是临时文件
                    temp_files = [f.name for f in all_excel_files if f.name.startswith('~$')]
                    logger.warning(f"⚠️ 目录 {directory_path.name} 中只有Excel临时文件: {temp_files}")
                    error_msg = f"❌ 目录 {directory_path.name} 中没有有效的Excel文件（只有临时文件）"
                else:
                    error_msg = f"❌ 目录 {directory_path.name} 中没有Excel文件"
                logger.error(error_msg)
                return error_msg
            elif len(excel_files) > 1:
                excel_names = [f.name for f in excel_files]
                error_msg = f"❌ 目录 {directory_path.name} 中有多个Excel文件: {excel_names}"
                logger.error(error_msg)
                return error_msg
            else:
                logger.info(f"✅ 目录 {directory_path.name} 中Excel文件验证通过: {excel_files[0].name}")
                return str(excel_files[0])
                
        except Exception as e:
            error_msg = f"❌ 验证目录 {directory_path.name} Excel文件时出错: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def _query_debtor_no_from_reconciliation(self, name: str, id_last4: str = None) -> Dict:
        """
        从还款对账明细中查询debtor_no
        
        Args:
            name: 债务人姓名
            id_last4: 身份证后4位（可选，用于重名处理）
            
        Returns:
            查询结果字典：{'found': bool, 'debtor_no': str, 'match_method': str, 'is_missing': bool}
        """
        if not self.reconciliation_data:
            return {'found': False, 'debtor_no': '', 'match_method': '无还款对账明细数据', 'is_missing': False}
        
        try:
            # 按姓名查找匹配记录
            name_matches = []
            for record in self.reconciliation_data:
                record_name = record.get('债务人姓名', '').strip()
                if record_name == name:
                    name_matches.append(record)
            
            if not name_matches:
                # 姓名未找到，标记为缺失
                self.missing_debtor_names.add(name)
                logger.warning(f"⚠️ 还款对账明细中未找到姓名: {name}")
                return {'found': False, 'debtor_no': '', 'match_method': f'还款对账明细中未找到姓名: {name}', 'is_missing': True}
            
            # 如果只有一个匹配，直接返回
            if len(name_matches) == 1:
                debtor_no = name_matches[0].get('debtor_no', '')
                logger.info(f"✅ 通过姓名找到唯一匹配: {name} → debtor_no: {debtor_no}")
                return {'found': True, 'debtor_no': debtor_no, 'match_method': '姓名唯一匹配', 'is_missing': False}
            
            # 有多个同名记录，尝试通过身份证后4位精确匹配
            if id_last4:
                exact_matches = []
                for record in name_matches:
                    record_id = record.get('身份证号', '')
                    if record_id and str(record_id).endswith(id_last4):
                        exact_matches.append(record)
                
                if len(exact_matches) == 1:
                    debtor_no = exact_matches[0].get('debtor_no', '')
                    logger.info(f"✅ 通过姓名+身份证后4位精确匹配: {name}({id_last4}) → debtor_no: {debtor_no}")
                    return {'found': True, 'debtor_no': debtor_no, 'match_method': f'姓名+身份证后4位精确匹配({id_last4})', 'is_missing': False}
                elif len(exact_matches) > 1:
                    # 仍有重复，取第一个
                    debtor_no = exact_matches[0].get('debtor_no', '')
                    logger.warning(f"⚠️ 姓名+身份证后4位仍有多个匹配，取第一个: {name}({id_last4}) → debtor_no: {debtor_no}")
                    return {'found': True, 'debtor_no': debtor_no, 'match_method': f'姓名+身份证后4位多个匹配取第一个({id_last4})', 'is_missing': False}
                else:
                    # 身份证后4位不匹配，使用姓名匹配的第一个
                    debtor_no = name_matches[0].get('debtor_no', '')
                    logger.warning(f"⚠️ 身份证后4位不匹配，使用姓名匹配的第一个: {name} → debtor_no: {debtor_no}")
                    return {'found': True, 'debtor_no': debtor_no, 'match_method': f'身份证后4位不匹配，姓名匹配第一个', 'is_missing': False}
            else:
                # 没有身份证后4位，多个同名记录取第一个
                debtor_no = name_matches[0].get('debtor_no', '')
                logger.warning(f"⚠️ 多个同名记录且无身份证后4位，取第一个: {name} → debtor_no: {debtor_no}")
                return {'found': True, 'debtor_no': debtor_no, 'match_method': f'多个同名记录取第一个', 'is_missing': False}
        
        except Exception as e:
            logger.error(f"❌ 查询还款对账明细debtor_no失败: {name}, 错误: {e}")
            return {'found': False, 'debtor_no': '', 'match_method': f'查询失败: {e}', 'is_missing': False}

    def _validate_starred_payer_name(self, ocr_payer: str, filename_name: str, 
                                   ocr_payment_time: str, folder_name: str) -> Dict:
        """
        验证带星号的还款人是否应该替换为债务人名称
        
        Args:
            ocr_payer: OCR识别的付款方名称（可能包含*号）
            filename_name: 文件名中的债务人名称
            ocr_payment_time: OCR识别的还款时间
            folder_name: 文件夹名称
            
        Returns:
            验证结果字典：{
                'should_replace': bool,  # 是否应该替换
                'replacement_name': str,  # 替换的名称
                'reason': str  # 替换或不替换的原因
            }
        """
        result = {
            'should_replace': False,
            'replacement_name': '',
            'reason': ''
        }
        
        try:
            # 1. 检查是否包含星号
            if '*' not in ocr_payer:
                result['reason'] = '付款方名称不包含星号'
                return result
            
            # 2. 检查是否有文件名中的债务人名称
            if not filename_name:
                result['reason'] = '文件名中没有识别到债务人名称'
                return result
            
            # 3. 检查该债务人是否在还款对账明细中有debtor_no
            debtor_query_result = self._query_debtor_no_from_reconciliation(filename_name)
            if not debtor_query_result['found'] or not debtor_query_result['debtor_no']:
                result['reason'] = f'债务人[{filename_name}]在还款对账明细中未找到debtor_no'
                return result
            
            # 4. 检查OCR识别的还款时间是否与Excel中的还款时间一致
            if not ocr_payment_time:
                result['reason'] = 'OCR未识别到还款时间'
                return result
            
            # 在还款对账明细中查找该债务人的还款时间记录
            excel_payment_time = None
            for record in self.reconciliation_data:
                if (record.get('债务人姓名', '').strip() == filename_name and 
                    record.get('debtor_no', '') == debtor_query_result['debtor_no']):
                    excel_payment_time = record.get('还款时间', '')
                    break
            
            if not excel_payment_time:
                result['reason'] = f'在还款对账明细中未找到债务人[{filename_name}]的还款时间记录'
                return result
            
            # 5. 比较时间是否一致（转换为标准格式进行比较）
            try:
                # 格式化OCR时间为标准格式
                formatted_ocr_time = self._format_payment_time(ocr_payment_time)
                # 格式化Excel时间为标准格式
                formatted_excel_time = self._format_payment_time(str(excel_payment_time))
                
                if formatted_ocr_time == formatted_excel_time:
                    # 所有条件都满足，可以替换
                    result['should_replace'] = True
                    result['replacement_name'] = filename_name
                    result['reason'] = f'满足所有条件：含*号[{ocr_payer}]，文件名债务人[{filename_name}]，Excel有debtor_no[{debtor_query_result["debtor_no"]}]，时间一致[{formatted_ocr_time}]'
                    logger.info(f"✅ 星号还款人验证通过，将替换: {ocr_payer} → {filename_name}")
                else:
                    result['reason'] = f'还款时间不一致：OCR[{formatted_ocr_time}] vs Excel[{formatted_excel_time}]'
                    
            except Exception as e:
                result['reason'] = f'时间格式比较失败: {str(e)}'
                
        except Exception as e:
            result['reason'] = f'验证过程异常: {str(e)}'
            logger.error(f"❌ 星号还款人验证异常: {str(e)}")
        
        return result

    def _get_settlement_analysis_for_results(self) -> Dict:
        """
        为处理结果获取结清验证分析信息
        
        Returns:
            结清验证分析字典 {debtor_no: 验证状态}
        """
        settlement_issues = {}
        
        if not self.reconciliation_data:
            return settlement_issues
        
        try:
            # 按debtor_no分组还款对账明细数据
            reconciliation_by_debtor = {}
            for record in self.reconciliation_data:
                debtor_no = record.get('debtor_no', '')
                if debtor_no:
                    if debtor_no not in reconciliation_by_debtor:
                        reconciliation_by_debtor[debtor_no] = []
                    reconciliation_by_debtor[debtor_no].append(record)
            
            # 检查每个债务人的结清验证状态
            for debtor_no, records in reconciliation_by_debtor.items():
                for record in records:
                    payment_type = record.get('类型', '').strip()
                    
                    # 只处理结清类型的记录
                    if '结清' in payment_type:
                        try:
                            payment_amount = float(record.get('还款金额', 0))
                            remaining_principal = float(record.get('remaining_principal', 0))
                            
                            # 检查是否还款金额小于剩余本金
                            if payment_amount < remaining_principal:
                                settlement_issues[debtor_no] = {
                                    'status': '结清但还款金额小于剩余本金，需要关注',
                                    'payment_amount': payment_amount,
                                    'remaining_principal': remaining_principal,
                                    'difference': remaining_principal - payment_amount
                                }
                                logger.info(f"🟠 发现结清验证问题: {debtor_no} - 还款{payment_amount}，剩余{remaining_principal}")
                            else:
                                # 结清金额合理的情况，暂不设置状态
                                pass
                        except (ValueError, TypeError) as e:
                            logger.warning(f"⚠️ 解析金额失败，跳过结清验证: {debtor_no}, 错误: {e}")
                            
        except Exception as e:
            logger.error(f"❌ 获取结清验证分析失败: {e}")
        
        return settlement_issues

    def _set_settlement_validation_status(self, settlement_analysis: Dict):
        """
        为每条处理结果设置结清验证状态
        
        Args:
            settlement_analysis: 结清验证分析结果
        """
        try:
            for result in self.results:
                debtor_no = result.get('debtor_no', '')
                
                # 默认状态为空
                result['结清验证状态'] = ''
                
                # 检查是否存在结清验证问题
                if debtor_no and debtor_no in settlement_analysis:
                    issue = settlement_analysis[debtor_no]
                    result['结清验证状态'] = issue['status']
                    result['_结清验证问题'] = True  # 标记用于Excel格式化
                    logger.info(f"✅ 设置结清验证状态: {debtor_no} - {issue['status']}")
                else:
                    result['_结清验证问题'] = False
                    
        except Exception as e:
            logger.error(f"❌ 设置结清验证状态失败: {e}")

    def _query_debtor_info(self, identity_no: str = None, contract_no: str = None) -> Dict:
        """
        通过身份证号或合同号查询债务人信息 - 使用DuckDB增强版
        
        Args:
            identity_no: 身份证号
            contract_no: 合同号
            
        Returns:
            债务人信息字典
        """
        result = {
            'debtor_no': '',
            'collecter_user_name': '',
            'found': False,
            'search_method': ''
        }
        
        if not self.debtor_db:
            # logger.debug("债务人数据库未初始化")
            return result
            
        try:
            # 使用数据库查询
            return self.debtor_db.query_debtor_info(identity_no, contract_no)
            
        except Exception as e:
            logger.error(f"查询债务人信息时出错：{str(e)}")
            return result

    def _generate_person_statistics(self) -> List[Dict]:
        """
        生成按人名的统计信息 - 修复版
        
        Returns:
            按人名统计的结果列表
        """
        person_stats = {}
        processed_directories = set()
        
        logger.info(f"开始生成人名统计，共有 {len(self.results)} 条处理结果")
        
        # 统计每个人的信息
        for i, result in enumerate(self.results):
            folder_name = result.get('文件夹名称', '').strip()
            name = result.get('还款人', '').strip()
            file_path = result.get('完整文件路径', '')
            
            # logger.debug(f"处理记录 {i+1}: 文件夹={folder_name}, 姓名={name}, 路径={file_path}")
            
            # 记录处理过的目录
            if folder_name:
                processed_directories.add(folder_name)
            
            # 如果没有姓名，尝试从文件路径中提取
            if not name and file_path:
                # 尝试从文件名中解析姓名
                file_name = os.path.basename(file_path)
                parsed = self.parse_filename(os.path.splitext(file_name)[0])
                if parsed['name']:
                    name = parsed['name']
                    # logger.debug(f"从文件名解析到姓名: {name}")
            
            # 如果还是没有姓名，尝试从文件夹名称中提取
            if not name and folder_name:
                parsed = self.parse_filename(folder_name)
                if parsed['name']:
                    name = parsed['name']
                    # logger.debug(f"从文件夹名称解析到姓名: {name}")
            
            # 如果仍然没有姓名，使用文件夹名称作为标识
            if not name:
                if folder_name:
                    name = f"未识别姓名_{folder_name}"
                    logger.warning(f"无法识别姓名，使用文件夹名称: {name}")
                else:
                    name = f"未识别姓名_{i+1}"
                    logger.warning(f"无法识别姓名和文件夹，使用序号: {name}")
            
            # 获取其他信息
            payment_time = result.get('还款时间', '')
            amount_str = result.get('图片OCR识别金额', '') or result.get('图片文件名金额', '')
            id_last4 = result.get('图片身份证后4位', '')
            
            # 尝试解析金额
            try:
                amount = float(amount_str) if amount_str else 0.0
            except (ValueError, TypeError):
                amount = 0.0
            
            # 创建唯一标识（文件夹+姓名）
            unique_key = f"{folder_name}_{name}"
            
            # 初始化人员统计
            if unique_key not in person_stats:
                person_stats[unique_key] = {
                    'name': name,
                    'folder_name': folder_name,
                    'payment_time': payment_time,
                    'contract_no': '',  # 将从Excel中提取
                    'id_last4': id_last4,
                    'full_id_no': '',   # 将从Excel中提取
                    'debtor_no': '',
                    'collecter_user_name': '',
                    'voucher_count': 0,
                    'total_amount': 0.0,
                    'voucher_list': []
                }
                # logger.debug(f"创建新的人员统计记录: {unique_key}")
            
            # 累加统计信息
            person_stats[unique_key]['voucher_count'] += 1
            person_stats[unique_key]['total_amount'] += amount
            person_stats[unique_key]['voucher_list'].append({
                'file_path': file_path,
                'amount': amount
            })
            
            # 更新身份证信息（优先使用有值的）
            if id_last4 and not person_stats[unique_key]['id_last4']:
                person_stats[unique_key]['id_last4'] = id_last4
            
            # 更新还款时间（优先使用有值的）
            if payment_time and not person_stats[unique_key]['payment_time']:
                person_stats[unique_key]['payment_time'] = payment_time
        
        logger.info(f"处理完成，共处理 {len(processed_directories)} 个目录，生成 {len(person_stats)} 个人员统计")
        logger.info(f"处理的目录列表: {sorted(processed_directories)}")
        
        # 从Excel文件中查询债务人信息
        for unique_key, stats in person_stats.items():
            # logger.debug(f"为 {unique_key} 查询Excel信息")
            
            # 读取对应目录的Excel文件获取身份证号和合同号
            excel_info = self._extract_info_from_excel(stats['folder_name'], stats['name'])
            
            if excel_info:
                stats['full_id_no'] = excel_info.get('identity_no', '')
                stats['contract_no'] = excel_info.get('contract_no', '')
                if excel_info.get('payment_time') and not stats['payment_time']:
                    stats['payment_time'] = excel_info.get('payment_time')
                # logger.debug(f"从Excel获取信息: 身份证={stats['full_id_no']}, 合同号={stats['contract_no']}")
            
            # 从Sheet1结果中获取debtor_no（避免重复查询）
            debtor_no_from_results = ''
            collecter_user_name_from_results = ''
            
            # 查找该人员在results中的debtor_no
            for voucher in stats['voucher_list']:
                for result in self.results:
                    if result.get('完整文件路径') == voucher['file_path']:
                        if result.get('debtor_no'):
                            debtor_no_from_results = result['debtor_no']
                            # 如果有debtor_no，查询对应的collecter_user_name
                            if debtor_no_from_results:
                                debtor_info = self._query_debtor_info(
                                    identity_no=stats['full_id_no'],
                                    contract_no=stats['contract_no']
                                )
                                if debtor_info['found']:
                                    collecter_user_name_from_results = debtor_info['collecter_user_name']
                            break
                if debtor_no_from_results:
                    break
            
            if debtor_no_from_results:
                stats['debtor_no'] = debtor_no_from_results
                stats['collecter_user_name'] = collecter_user_name_from_results
                # logger.debug(f"从Sheet1获取债务人信息: {stats['name']} -> {debtor_no_from_results}")
            else:
                stats['debtor_no'] = ''
                stats['collecter_user_name'] = ''
                logger.warning(f"未找到债务人信息: {stats['name']}")
        
        # 转换为列表格式并按文件夹名称排序
        result_list = sorted(person_stats.values(), key=lambda x: (x['folder_name'], x['name']))
        
        logger.info(f"最终生成 {len(result_list)} 个人员统计记录")
        
        return result_list
    
    def _extract_info_from_excel(self, folder_name: str, person_name: str) -> Dict:
        """
        从Excel文件中提取人员信息
        
        Args:
            folder_name: 文件夹名称
            person_name: 人员姓名
            
        Returns:
            人员信息字典
        """
        try:
            # 查找对应的Excel文件
            folder_path = self.base_folder / folder_name
            excel_files = list(folder_path.glob("*.xlsx")) + list(folder_path.glob("*.xls"))
            
            if len(excel_files) != 1:
                logger.warning(f"文件夹 {folder_name} 中Excel文件数量不正确: {len(excel_files)}")
                return {}
            
            excel_file = excel_files[0]
            df = pd.read_excel(excel_file)
            
            # 查找对应人员的信息
            # 尝试不同的姓名列
            name_columns = ['姓名', '债务人姓名', '还款人姓名', '客户姓名', 'name']
            target_row = None
            
            for col in name_columns:
                if col in df.columns:
                    mask = df[col].astype(str).str.contains(person_name, na=False)
                    matches = df[mask]
                    if not matches.empty:
                        target_row = matches.iloc[0]
                        break
            
            if target_row is None:
                # logger.debug(f"在Excel中未找到人员 {person_name} 的信息")
                return {}
            
            # 提取信息
            result = {}
            
            # 查找身份证号 - 强制文本格式，避免科学计数法
            id_columns = ['身份证号码', '身份证号', '身份证', 'identity_no', '证件号码']
            for col in id_columns:
                if col in target_row and pd.notna(target_row[col]):
                    id_value = target_row[col]
                    # 如果是浮点数，转换为整数再转字符串
                    if isinstance(id_value, float):
                        id_value = str(int(id_value))
                    else:
                        id_value = str(id_value)
                    result['identity_no'] = id_value.strip()
                    break
            
            # 查找合同号
            contract_columns = ['合同号码', '合同号', '合同编号', 'contract_no', '案件号', 'case_no']
            for col in contract_columns:
                if col in target_row and pd.notna(target_row[col]):
                    contract_value = str(target_row[col]).strip()
                    # 处理浮点数格式的合同号（如 19679101.0 -> 19679101）
                    try:
                        if '.' in contract_value and contract_value.replace('.', '').isdigit():
                            contract_value = str(int(float(contract_value)))
                    except (ValueError, TypeError):
                        pass  # 保持原值
                    result['contract_no'] = contract_value
                    break
            
            # 查找还款时间
            time_columns = ['还款时间', '还款日期', '交易时间', '交易日期', 'payment_time']
            for col in time_columns:
                if col in target_row and pd.notna(target_row[col]):
                    result['payment_time'] = str(target_row[col]).strip()
                    break
            
            # logger.debug(f"从Excel提取信息 {person_name}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"从Excel提取信息失败 {folder_name}/{person_name}: {str(e)}")
            return {}

    def _init_paddleocr(self):
        """
        初始化PaddleOCR引擎 - 使用Context7推荐的最新配置优化中文识别
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("🚀 开始初始化PaddleOCR引擎 (Context7优化版)...")
            
            # 导入PaddleOCR
            from paddleocr import PaddleOCR
            
            logger.info("✅ PaddleOCR库导入成功")
            
            # 使用Context7文档推荐的最新最佳配置
            # 针对中文银行凭证识别的优化参数
            self.paddle_ocr = PaddleOCR(
                # === 语言和版本 ===
                lang='ch',                          # 中文模型
                ocr_version='PP-OCRv5',             # 使用最新的PP-OCRv5版本
                
                # === 文档方向和预处理 ===
                use_doc_orientation_classify=True,   # 启用文档方向分类（银行凭证可能有旋转）
                use_doc_unwarping=True,              # 启用文档矫正（处理拍照倾斜）
                use_textline_orientation=True,       # 启用文本行方向分类
                
                # === 文本检测优化（关键参数）===
                text_det_limit_side_len=1280,        # 增大图像边长限制（更高分辨率）
                text_det_limit_type='max',           # 最大边长限制
                text_det_thresh=0.2,                 # 降低像素阈值（检测更多文本）
                text_det_box_thresh=0.5,             # 降低框阈值（保留更多检测框）
                text_det_unclip_ratio=2.5,           # 增大文本区域扩展系数
                
                # === 文本识别优化 ===
                text_rec_score_thresh=0.1,           # 降低识别阈值（保留更多识别结果）
                
                # === 性能优化 ===
                enable_mkldnn=True,                  # 启用MKL-DNN加速
                cpu_threads=8,                       # CPU线程数
                precision='fp32',                    # 使用fp32精度（更准确）
                
                # === 高级优化参数 ===
                enable_hpi=False,                    # 禁用高性能推理（保证准确性）
                use_tensorrt=False,                  # 禁用TensorRT（避免兼容性问题）
                
                # === 模型配置 ===
                text_detection_model_name="PP-OCRv5_mobile_det",   # 使用v5检测模型
                text_recognition_model_name="PP-OCRv5_mobile_rec", # 使用v5识别模型
                
                # === 其他设置 ===
                # show_log 参数在当前版本中不支持，已移除
            )
            
            logger.info("✅ PaddleOCR引擎初始化成功 (Context7优化配置)")
            logger.info(f"   🎯 语言: 中文 (ch)")
            logger.info(f"   📱 版本: PP-OCRv5 (最新版本)")
            logger.info(f"   🔄 文档方向分类: 启用")
            logger.info(f"   📐 文档矫正: 启用") 
            logger.info(f"   📏 检测边长限制: 1280px")
            logger.info(f"   🎚️ 检测阈值: 0.2 (更敏感)")
            logger.info(f"   📦 框阈值: 0.5 (保留更多)")
            logger.info(f"   🔍 识别阈值: 0.1 (保留更多)")
            logger.info(f"   ⚡ MKL-DNN加速: 启用")
            
            # 测试OCR引擎
            test_success = self._test_paddleocr()
            if test_success:
                logger.info("🎉 PaddleOCR引擎测试通过，配置优化成功")
                return True
            else:
                logger.warning("⚠️ PaddleOCR引擎测试失败，尝试备用配置...")
                return self._init_paddleocr_fallback()
                
        except ImportError as e:
            logger.error(f"❌ PaddleOCR库导入失败: {str(e)}")
            logger.info("💡 解决方案: pip install paddlepaddle paddleocr")
            return False
        except Exception as e:
            logger.error(f"❌ PaddleOCR初始化失败: {str(e)}")
            logger.info("🔄 尝试备用配置...")
            return self._init_paddleocr_fallback()

    def _init_paddleocr_fallback(self):
        """
        PaddleOCR备用初始化配置（简化参数）
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("🔄 尝试PaddleOCR备用配置...")
            from paddleocr import PaddleOCR
            
            # 简化配置，提高兼容性
            self.paddle_ocr = PaddleOCR(
                lang='ch',                           # 中文模型
                use_doc_orientation_classify=False,  # 禁用文档方向分类
                use_doc_unwarping=False,             # 禁用文档矫正
                use_textline_orientation=True,       # 保留文本行方向分类
                text_det_thresh=0.3,                 # 默认检测阈值
                text_det_box_thresh=0.6,             # 默认框阈值
                text_rec_score_thresh=0.0            # 默认识别阈值
            )
            
            logger.info("✅ PaddleOCR备用配置初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ PaddleOCR备用配置也失败: {str(e)}")
            return False

    def _test_paddleocr(self) -> bool:
        """
        测试PaddleOCR引擎是否正常工作
        
        Returns:
            bool: 测试是否通过
        """
        try:
            # 创建一个简单的测试图像（纯白背景黑字）
            import numpy as np
            from PIL import Image, ImageDraw, ImageFont
            import tempfile
            
            # 创建测试图片
            img = Image.new('RGB', (200, 100), color='white')
            draw = ImageDraw.Draw(img)
            
            # 尝试使用系统字体，如果失败则使用默认字体
            try:
                # Windows常见中文字体
                font = ImageFont.truetype("msyh.ttc", 24)  # 微软雅黑
            except:
                try:
                    font = ImageFont.truetype("simsun.ttc", 24)  # 宋体
                except:
                    font = ImageFont.load_default()
            
            # 绘制测试文字
            draw.text((10, 30), "测试", fill='black', font=font)
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                img.save(tmp_file.name)
                tmp_path = tmp_file.name
            
            # 使用PaddleOCR识别 - 使用推荐的predict方法
            try:
                result = self.paddle_ocr.predict(tmp_path)
            except AttributeError:
                # 如果predict方法不存在，回退到ocr方法
                result = self.paddle_ocr.ocr(tmp_path)
            
            # 清理临时文件
            import os
            os.unlink(tmp_path)
            
            # 检查识别结果 - 支持新版字典格式和传统列表格式
            # logger.debug(f"🔧 测试结果类型: {type(result)}")
            # logger.debug(f"🔧 测试结果内容: {str(result)[:200]}...")  # 限制输出长度
            
            # 处理新版PaddleOCR字典格式
            if result and isinstance(result, dict) and 'rec_texts' in result:
                logger.info("✅ PaddleOCR测试通过 (字典格式)")
                rec_texts = result.get('rec_texts', [])
                # logger.debug(f"识别到 {len(rec_texts)} 行文本")
                return True
            
            # 处理传统PaddleOCR列表格式
            elif result and isinstance(result, list) and len(result) > 0:
                # logger.debug(f"PaddleOCR测试结果: 列表格式，长度={len(result)}")
                
                # PaddleOCR标准格式检查
                if result[0] and isinstance(result[0], list):
                    # 标准格式 - 列表嵌套
                    logger.info("✅ PaddleOCR测试通过 (列表格式)")
                    return True
                else:
                    logger.info("✅ PaddleOCR测试通过 (未知格式但有返回结果)")
                    # logger.debug(f"🔧 result[0]的类型: {type(result[0])}，内容: {result[0]}")
                    return True  # 仍然认为测试通过，因为有返回结果
            else:
                logger.warning("PaddleOCR测试未返回有效结果")
                return False
                
        except Exception as e:
            logger.error(f"PaddleOCR测试失败: {str(e)}")
            return False

    def _perform_paddleocr(self, image_path: Path) -> str:
        """
        使用PaddleOCR执行图片文字识别 - 高性能中文识别
        
        Args:
            image_path: 图片路径
            
        Returns:
            识别到的文本字符串
        """
        if not hasattr(self, 'paddle_ocr') or self.paddle_ocr is None:
            logger.error("❌ PaddleOCR引擎未初始化")
            return ""
        
        import shutil
        import tempfile
            
        temp_image_path = None
        try:
            logger.info(f"🔍 使用PaddleOCR处理图片: {image_path.name}")
            # logger.debug(f"   原始图片路径: {image_path}")
            
            # 检查图片文件是否存在
            if not image_path.exists():
                logger.error(f"❌ 图片文件不存在: {image_path}")
                return ""
            
            # 解决中文路径问题：复制到临时目录
            # 创建临时文件，使用英文文件名
            temp_dir = tempfile.mkdtemp()
            temp_filename = f"temp_paddle_ocr_{hash(str(image_path)) % 10000}.{image_path.suffix}"
            temp_image_path = Path(temp_dir) / temp_filename
            
            # 复制图片到临时目录
            shutil.copy2(str(image_path), str(temp_image_path))
            # logger.debug(f"   临时图片路径: {temp_image_path}")
            
            # 验证临时文件是否创建成功
            if not temp_image_path.exists():
                logger.error(f"❌ 临时文件创建失败: {temp_image_path}")
                return ""
            
            # 执行OCR识别（使用临时文件路径）
            start_time = time.time()
            # 直接使用ocr方法（新版PaddleOCR API）
            result = self.paddle_ocr.ocr(str(temp_image_path))
            end_time = time.time()
            
            # 处理识别结果 - PaddleOCR的ocr方法返回格式
            extracted_text = ""
            text_lines = []
            
            # 调试：输出PaddleOCR返回的原始结果
            # logger.debug(f"🔧 PaddleOCR返回结果类型: {type(result)}")
            # logger.debug(f"🔧 PaddleOCR返回结果内容: {str(result)[:500]}...")  # 限制输出长度
            
            # 处理新版PaddleOCR返回的格式
            if result and isinstance(result, list) and len(result) > 0:
                # 检查是否是新版PaddleOCR返回的列表包含字典格式
                first_item = result[0]
                if isinstance(first_item, dict) and 'rec_texts' in first_item:
                    # logger.debug(f"🔧 PaddleOCR返回格式: 新版列表包含字典格式，包含rec_texts字段")
                    
                    rec_texts = first_item.get('rec_texts', [])
                    rec_scores = first_item.get('rec_scores', [])
                    
                    for i, text in enumerate(rec_texts):
                        if text and text.strip():
                            text_lines.append(text.strip())
                            confidence = rec_scores[i] if i < len(rec_scores) else 0.0
                            # logger.debug(f"  📝 {text.strip()} (置信度: {confidence:.3f})")
                    
                    if text_lines:
                        extracted_text = '\n'.join(text_lines)
                        
                        logger.info(f"✅ PaddleOCR识别成功，用时: {end_time - start_time:.2f}秒")
                        logger.info(f"   📄 识别文本行数: {len(text_lines)}")
                        logger.info(f"   📝 文本长度: {len(extracted_text)} 字符")
                        
                        # 输出详细的识别结果用于调试
                        # logger.debug("=" * 50)
                        # logger.debug("📄 PaddleOCR识别完整文本:")
                        # logger.debug(extracted_text)
                        # logger.debug("=" * 50)
                        
                        # 输出前300字符作为预览
                        preview_length = min(300, len(extracted_text))
                        logger.info(f"📄 OCR文本预览 (前{preview_length}字符): {extracted_text[:preview_length]}")
                        
                        return extracted_text
                    else:
                        logger.warning("❌ PaddleOCR未识别到任何有效文本")
                        return ""

            
            # 处理传统PaddleOCR列表格式（向后兼容）
            elif result and isinstance(result, list):
                # logger.debug(f"🔧 PaddleOCR返回格式: 列表，长度={len(result)}")
                
                # 标准PaddleOCR格式处理
                for line_result in result:
                    if line_result and isinstance(line_result, list):
                        for detection in line_result:
                            if detection and len(detection) >= 2:
                                # 坐标信息在detection[0]，文本信息在detection[1]
                                text_info = detection[1]
                                
                                if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                    text = text_info[0]
                                    confidence = text_info[1]
                                    
                                    if text and text.strip():
                                        text_lines.append(text.strip())
                                        # logger.debug(f"  📝 {text.strip()} (置信度: {confidence:.3f})")
                                elif isinstance(text_info, str) and text_info.strip():
                                    text_lines.append(text_info.strip())
                                    # logger.debug(f"  📝 {text_info.strip()}")
                
                if text_lines:
                    extracted_text = '\n'.join(text_lines)
                    
                    logger.info(f"✅ PaddleOCR识别成功，用时: {end_time - start_time:.2f}秒")
                    logger.info(f"   📄 识别文本行数: {len(text_lines)}")
                    logger.info(f"   📝 文本长度: {len(extracted_text)} 字符")
                    
                    # 输出详细的识别结果用于调试
                    # logger.debug("=" * 50)
                    # logger.debug("📄 PaddleOCR识别完整文本:")
                    # logger.debug(extracted_text)
                    # logger.debug("=" * 50)
                    
                    # 输出前300字符作为预览
                    preview_length = min(300, len(extracted_text))
                    logger.info(f"📄 OCR文本预览 (前{preview_length}字符): {extracted_text[:preview_length]}")
                    
                    return extracted_text
                else:
                    logger.warning("❌ PaddleOCR未识别到任何有效文本")
                    return ""
            else:
                logger.warning("❌ PaddleOCR返回空结果或格式不支持")
                # logger.debug(f"   返回结果类型: {type(result)}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ PaddleOCR识别异常: {str(e)}")
            # logger.debug(f"   异常类型: {type(e)}")
            import traceback
            # logger.debug(f"   异常堆栈: {traceback.format_exc()}")
            return ""
        finally:
            # 清理临时文件
            if temp_image_path and temp_image_path.exists():
                try:
                    temp_image_path.unlink()
                    temp_image_path.parent.rmdir()  # 删除临时目录
                    # logger.debug(f"✅ 临时文件已清理: {temp_image_path}")
                except Exception as cleanup_error:
                    pass
                    # logger.debug(f"⚠️ 临时文件清理失败: {str(cleanup_error)}")

    def _init_tesseract(self):
        """
        初始化Tesseract OCR引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("🚀 开始初始化Tesseract OCR引擎...")
            
            # 导入依赖
            import pytesseract
            from PIL import Image
            
            logger.info("✅ Tesseract OCR库导入成功")
            
            # 测试Tesseract是否正常工作
            try:
                # 创建一个简单的测试图片
                test_image = Image.new('RGB', (100, 50), color='white')
                
                # 尝试基础OCR识别
                pytesseract.image_to_string(test_image)
                
                logger.info("✅ Tesseract OCR引擎测试通过")
                logger.info("   🎯 支持多种语言包")
                logger.info("   ⚡ 成熟稳定的识别引擎")
                logger.info("   🔧 可自定义识别参数")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Tesseract OCR测试失败: {str(e)}")
                logger.info("💡 请确保Tesseract已正确安装")
                return False
                
        except ImportError as e:
            logger.error(f"❌ Tesseract OCR库导入失败: {str(e)}")
            logger.info("💡 解决方案: pip install pytesseract pillow")
            return False
        except Exception as e:
            logger.error(f"❌ Tesseract OCR初始化失败: {str(e)}")
            return False

    def _init_easyocr(self):
        """
        初始化EasyOCR引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("🚀 开始初始化EasyOCR引擎...")
            
            # 导入EasyOCR
            import easyocr
            
            logger.info("✅ EasyOCR库导入成功")
            
            # 初始化EasyOCR读取器
            self.easyocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            
            logger.info("✅ EasyOCR引擎初始化成功")
            logger.info("   🎯 支持中英文混合识别")
            logger.info("   ⚡ 高精度文本检测")
            logger.info("   🔧 基于深度学习")
            
            return True
            
        except ImportError as e:
            logger.error(f"❌ EasyOCR库导入失败: {str(e)}")
            logger.info("💡 解决方案: pip install easyocr")
            return False
        except Exception as e:
            logger.error(f"❌ EasyOCR初始化失败: {str(e)}")
            return False

    def _init_doubao_ocr(self):
        """
        初始化豆包Seed-1.6 OCR引擎
        
        Returns:
            DoubaoOCRProcessor实例或None
        """
        try:
            logger.info("🚀 开始初始化豆包Seed-1.6 OCR引擎...")
            
            # 硬编码豆包API Key
            DOUBAO_API_KEY = "d2145614-4e5b-4bb3-8371-ebe67222ea95"
            
            if not DOUBAO_AVAILABLE:
                logger.error("❌ 豆包Seed-1.6依赖未安装")
                logger.info("💡 解决方案: pip install openai")
                return None
            
            # 初始化豆包处理器
            doubao_processor = DoubaoOCRProcessor(DOUBAO_API_KEY)
            
            logger.info("✅ 豆包Seed-1.6引擎初始化成功")
            logger.info("   🎯 AI视觉理解，最优中文识别")
            logger.info("   🇨🇳 专门针对中文场景优化")
            logger.info("   ⚡ 国内服务，响应速度快")
            logger.info("   💰 极低成本，高性价比")
            
            return doubao_processor
            
        except Exception as e:
            logger.error(f"❌ 豆包Seed-1.6初始化失败: {str(e)}")
            return None

    def _perform_doubao_ocr(self, image_path: Path) -> Dict[str, str]:
        """
        使用豆包Seed-1.6执行图片AI视觉理解
        
        Args:
            image_path: 图片路径
            
        Returns:
            处理结果字典
        """
        try:
            logger.info(f"🤖 使用豆包Seed-1.6 AI处理图片: {image_path.name}")
            
            # 检查图片文件是否存在
            if not image_path.exists():
                logger.error(f"❌ 图片文件不存在: {image_path}")
                return {
                    'payer': '未知',
                    'account_last4': '0000',
                    'payment_source': '',
                    'payment_time': '',
                    'payment_amount': '',
                    'ocr_success': False
                }
            
            # 使用豆包处理器分析图片
            ai_result = self.ocr.analyze_payment_image(str(image_path))
            
            if ai_result.get('success'):
                ai_data = ai_result['data']
                
                # 按照用户要求处理各个字段
                # 1. 处理付款方姓名（保留星号）
                payer_name = ai_data.get('付款方姓名', '').strip()
                if not payer_name:
                    # 从文件名中提取姓名作为backup
                    filename_without_ext = image_path.stem
                    parsed_filename = self.parse_filename(filename_without_ext)
                    if parsed_filename.get('name'):
                        payer_name = parsed_filename['name']
                        logger.info(f"🔄 使用文件名中的姓名: {payer_name}")
                    else:
                        payer_name = '未知'  # 最后fallback
                
                # 2. 处理交易账户（后4位）
                account_info = ai_data.get('交易账户', '').strip()
                account_last4 = '0000'  # 默认值
                if account_info:
                    # 提取数字
                    account_digits = re.findall(r'\d+', account_info)
                    if account_digits:
                        # 取最长的数字串
                        longest_account = max(account_digits, key=len)
                        if len(longest_account) >= 4:
                            account_last4 = longest_account[-4:]  # 后4位
                        else:
                            account_last4 = longest_account
                
                # 3. 处理还款来源（基于备注附言中的代还关键词）
                remark = ai_data.get('备注附言', '').strip()
                payment_source = ''
                # 检查备注中是否有"代****还款"这类字样
                if re.search(r'代.{0,10}还款', remark):
                    payment_source = '他人代还'
                else:
                    payment_source = '本人还款'
                
                # 4. 处理还款时间
                payment_time = ai_data.get('交易时间', '').strip()
                
                # 5. 处理还款金额
                payment_amount = ai_data.get('交易金额', '').strip()
                
                logger.info(f"✅ 豆包AI分析成功: 👤{payer_name} 💳***{account_last4} 💰{payment_amount} ⏰{payment_time}")
                
                return {
                    'payer': payer_name,
                    'account_last4': account_last4,
                    'payment_source': payment_source,
                    'payment_time': payment_time,
                    'payment_amount': payment_amount,
                    'ocr_success': True
                }
                
            else:
                logger.error(f"❌ 豆包AI分析失败: {ai_result.get('error', '未知错误')}")
                return {
                    'payer': '未知',
                    'account_last4': '0000',
                    'payment_source': '',
                    'payment_time': '',
                    'payment_amount': '',
                    'ocr_success': False
                }
                
        except Exception as e:
            logger.error(f"❌ 豆包AI处理异常: {str(e)}")
            return {
                'payer': '未知',
                'account_last4': '0000', 
                'payment_source': '',
                'payment_time': '',
                'payment_amount': '',
                'ocr_success': False
            }

    def _perform_tesseract(self, image_path: Path) -> str:
        """
        使用Tesseract OCR执行图片文字识别
        
        Args:
            image_path: 图片路径
            
        Returns:
            识别到的文本字符串
        """
        try:
            logger.info(f"🔍 使用Tesseract OCR处理图片: {image_path.name}")
            
            # 导入依赖
            import pytesseract
            from PIL import Image
            
            # 检查图片文件是否存在
            if not image_path.exists():
                logger.error(f"❌ 图片文件不存在: {image_path}")
                return ""
            
            # 加载图片
            try:
                image = Image.open(str(image_path))
                # logger.debug(f"   图片尺寸: {image.size}")
            except Exception as e:
                logger.error(f"❌ 图片加载失败: {e}")
                return ""
            
            # 执行OCR识别 - 使用优化配置
            start_time = time.time()
            
            # 尝试不同的语言配置
            lang_configs = [
                ('chi_sim+eng', '简体中文+英文'),
                ('chi_sim', '简体中文'),
                ('eng', '英文'),
            ]
            
            best_result = ""
            best_config = ""
            
            for lang, desc in lang_configs:
                try:
                    # 使用自定义配置优化识别
                    custom_config = r'--oem 3 --psm 6'
                    
                    result = pytesseract.image_to_string(
                        image, 
                        lang=lang,
                        config=custom_config
                    )
                    
                    if result and result.strip() and len(result.strip()) > len(best_result):
                        best_result = result.strip()
                        best_config = desc
                        # logger.debug(f"   ✅ {desc}识别成功，文本长度: {len(result.strip())}")
                
                except Exception as e:
                    # logger.debug(f"   ⚠️ {desc}识别失败: {e}")
                    continue
            
            end_time = time.time()
            
            if best_result:
                logger.info(f"✅ Tesseract OCR识别成功，用时: {end_time - start_time:.2f}秒")
                logger.info(f"   📄 使用配置: {best_config}")
                logger.info(f"   📝 文本长度: {len(best_result)} 字符")
                
                # 输出详细的识别结果用于调试
                # logger.debug("=" * 50)
                # logger.debug("📄 Tesseract OCR识别完整文本:")
                # logger.debug(best_result)
                # logger.debug("=" * 50)
                
                # 输出前300字符作为预览
                preview_length = min(300, len(best_result))
                logger.info(f"📄 OCR文本预览 (前{preview_length}字符): {best_result[:preview_length]}")
                
                return best_result
            else:
                logger.warning("❌ Tesseract OCR未识别到任何有效文本")
                return ""
                
        except Exception as e:
            logger.error(f"❌ Tesseract OCR识别过程失败: {str(e)}")
            return ""

    def _perform_easyocr(self, image_path: Path) -> str:
        """
        使用EasyOCR执行图片文字识别
        
        Args:
            image_path: 图片路径
            
        Returns:
            识别到的文本字符串
        """
        if not hasattr(self, 'easyocr_reader') or self.easyocr_reader is None:
            logger.error("❌ EasyOCR引擎未初始化")
            return ""
        
        try:
            logger.info(f"🔍 使用EasyOCR处理图片: {image_path.name}")
            
            # 检查图片文件是否存在
            if not image_path.exists():
                logger.error(f"❌ 图片文件不存在: {image_path}")
                return ""
            
            # 执行OCR识别
            start_time = time.time()
            result = self.easyocr_reader.readtext(str(image_path))
            end_time = time.time()
            
            if result:
                # 提取所有文本
                text_lines = []
                for bbox, text, confidence in result:
                    if text and text.strip() and confidence > 0.1:  # 置信度阈值
                        text_lines.append(text.strip())
                        # logger.debug(f"  📝 {text.strip()} (置信度: {confidence:.3f})")
                
                if text_lines:
                    extracted_text = '\n'.join(text_lines)
                    
                    logger.info(f"✅ EasyOCR识别成功，用时: {end_time - start_time:.2f}秒")
                    logger.info(f"   📄 识别文本行数: {len(text_lines)}")
                    logger.info(f"   📝 文本长度: {len(extracted_text)} 字符")
                    
                    # 输出详细的识别结果用于调试
                    # logger.debug("=" * 50)
                    # logger.debug("📄 EasyOCR识别完整文本:")
                    # logger.debug(extracted_text)
                    # logger.debug("=" * 50)
                    
                    # 输出前300字符作为预览
                    preview_length = min(300, len(extracted_text))
                    logger.info(f"📄 OCR文本预览 (前{preview_length}字符): {extracted_text[:preview_length]}")
                    
                    return extracted_text
                else:
                    logger.warning("❌ EasyOCR未识别到有效文本（置信度过低）")
                    return ""
            else:
                logger.warning("❌ EasyOCR返回空结果")
                return ""
                
        except Exception as e:
            logger.error(f"❌ EasyOCR识别过程失败: {str(e)}")
            return ""


def main():
    """主函数"""
    # 配置文件夹路径
    base_folder = r"D:\民生4期回款"  # 请根据实际路径修改

    print("=== 民生4期回款凭证处理脚本 (增强版 v2.2) ===")
    print("🆕 最新更新 (v2.2):")
    print("   🔧 OCR引擎优化：Tesseract设为首选，提供详细状态检查")
    print("   🎯 OCR准确率验证：自动对比OCR识别与文件名解析结果")
    print("   📋 Excel智能标色：不一致记录自动标红/橙色警示")
    print("   🔗 文件路径超链接：Excel中可直接点击打开图片文件")
    print("   📊 准确率报告：生成详细的OCR识别质量分析报告")
    print("   ✅ 匹配状态追踪：新增OCR文件名匹配状态和准确度评估列")
    print()
    
    # 检查是否有命令行参数用于测试
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test" and len(sys.argv) > 2:
            test_image = sys.argv[2]
            test_ocr_recognition(test_image)
            return
        elif sys.argv[1] == "--help":
            print("使用方法:")
            print("  python payment_receipt_processor.py                    # 正常运行")
            print("  python payment_receipt_processor.py --test <图片路径>   # 测试OCR识别")
            print("  python payment_receipt_processor.py --help             # 显示帮助")
            return

    print("🆕 新增功能:")
    print("   📂 Excel文件唯一性验证（每个二级目录必须有且仅有一个Excel文件）")
    print("   👥 按人名统计功能（自动查询债务人信息，统计还款凭证数量和金额）")
    print("   🔍 债务人信息查询（通过身份证号或合同号自动匹配债务人明细）")
    print("   📊 多Sheet Excel输出（处理结果+人名统计+目录统计+处理摘要）")
    print("   🚀 DuckDB数据库集成（轻量级、高性能数据查询）")
    print()
    print("📋 原有功能:")
    print("   🎯 智能账户识别：自动排除收款账户(***1284)，专门识别付款账户")
    print("   ⚡ 文件名解析：姓名、金额、身份证后4位自动提取")
    print("   🔧 Ollama OCR引擎：llama3.2-vision:11b智能识别")
    print()
    
    # 检查DuckDB可用性
    if DUCKDB_AVAILABLE:
        print("✅ DuckDB数据库引擎可用 - 将提供高性能查询")
    else:
        print("⚠️  DuckDB数据库引擎不可用 - 使用pandas模式")
        print("   安装DuckDB以获得更好的性能: pip install duckdb")
    
    # 检查债务人明细文件
    debtor_file = "民生4期债务人明细.xlsx"
    if os.path.exists(debtor_file):
        print(f"✅ 债务人明细文件已找到: {debtor_file}")
    else:
        print(f"⚠️  债务人明细文件不存在: {debtor_file}")
        print("     部分功能（债务人信息查询）将无法使用")
    
    # 检查可用的OCR引擎（优先稳定性和兼容性）
    available_engines = []
    if PYTESSERACT_AVAILABLE:
        available_engines.append("Tesseract(成熟稳定)")
    if OLLAMA_OCR_AVAILABLE:
        available_engines.append("Ollama-OCR(智能增强)")
    if EASYOCR_AVAILABLE:
        available_engines.append("EasyOCR(备用)")
    
    if available_engines:
        print(f"🔍 可用OCR引擎: {', '.join(available_engines)}")
        selected_engine = 'auto'  # 自动选择最佳引擎（优先Tesseract）
        print("⚙️  使用模式: 智能OCR + 文件名解析 + 数据库查询")
        if PYTESSERACT_AVAILABLE:
            print("🎯 将优先使用Tesseract进行高精度OCR识别（新优先级）")
        elif PADDLEOCR_AVAILABLE:
            print("🎯 将使用PaddleOCR进行OCR识别（备用方案）")
    else:
        print("⚠️  未检测到OCR引擎，使用快速模式（仅文件名解析）")
        selected_engine = 'none'

    print()
    print("🎯 处理流程:")
    print("   1️⃣  验证二级目录Excel文件唯一性")
    print("   2️⃣  初始化DuckDB数据库（加载债务人明细）")
    print("   3️⃣  批量处理图片文件（OCR识别+文件名解析）")
    print("   4️⃣  从Excel文件中提取债务人身份证号、合同号等信息")
    print("   5️⃣  使用DuckDB快速查询债务人明细数据库")
    print("   6️⃣  按人名统计还款凭证数量和金额总计")
    print("   7️⃣  生成多Sheet Excel报告")
    print()
    print("开始处理...")

    try:
        processor = PaymentReceiptProcessor(base_folder, ocr_engine=selected_engine)
        
        # 显示数据库统计信息
        if processor.debtor_db:
            db_stats = processor.debtor_db.get_statistics()
            db_info = processor.debtor_db.get_database_info()
            
            print(f"\n📊 数据库统计信息:")
            print(f"   类型: {db_stats.get('database_type', 'unknown')}")
            print(f"   总记录数: {db_stats.get('total_records', 0):,}")
            print(f"   有身份证号: {db_stats.get('with_identity_no', 0):,}")
            print(f"   有合同号: {db_stats.get('with_case_no', 0):,}")
            
            print(f"\n💾 数据库文件信息:")
            print(f"   数据库文件: {db_info.get('database_file', 'N/A')}")
            if db_info.get('database_exists'):
                print(f"   文件大小: {db_info.get('database_size', 'N/A')}")
                print(f"   最后更新: {db_info.get('database_mtime', 'N/A')}")
            else:
                print(f"   状态: 新建数据库")
            
            print(f"   Excel文件: {db_info.get('excel_file', 'N/A')}")
            if db_info.get('excel_exists'):
                print(f"   Excel大小: {db_info.get('excel_size', 'N/A')}")
                print(f"   Excel更新时间: {db_info.get('excel_mtime', 'N/A')}")
        else:
            print("\n💾 数据库状态: 未初始化")
        
        output_file = processor.run()
        
        print(f"\n🎉 处理完成！增强版结果已保存到: {output_file}")
        print(f"📊 共处理了 {len(processor.results)} 个图片文件")
        print(f"📂 成功处理 {len(processor.directory_stats)} 个二级目录")
        
        # 显示人名统计信息
        person_stats = processor._generate_person_statistics()
        if person_stats:
            print(f"👥 统计到 {len(person_stats)} 个不同的还款人")
            found_debtor_info = len([p for p in person_stats if p['debtor_no']])
            not_found_count = len(person_stats) - found_debtor_info
            print(f"🔍 成功查询到债务人信息: {found_debtor_info}/{len(person_stats)} 人")
            
            if not_found_count > 0:
                print(f"⚠️  未找到债务人信息: {not_found_count} 人 (已在Excel中标红)")
            
            # 显示查询方法统计
            query_methods = {}
            for p in person_stats:
                if p['debtor_no']:
                    # 这里需要从查询结果中获取方法，暂时使用简化逻辑
                    if p['full_id_no']:
                        method = '身份证号'
                    elif p['contract_no']:
                        method = '合同号'
                    else:
                        method = '其他'
                    query_methods[method] = query_methods.get(method, 0) + 1
            
            if query_methods:
                print("📈 查询方法统计:")
                for method, count in query_methods.items():
                    print(f"   {method}: {count} 人")
        
        # 显示账户识别统计
        account_stats = analyze_account_results(processor.results)
        print_account_statistics(account_stats, selected_engine != 'none')
        
        # 🔧 显示OCR引擎状态检查（新增功能）
        print("\n" + "="*60)
        print("🔧 OCR引擎状态检查")
        print("="*60)
        
        ocr_status = processor.get_ocr_engine_status()
        print(f"引擎类型: {ocr_status['engine_type']}")
        print(f"初始化状态: {'✅ 成功' if ocr_status['is_initialized'] else '❌ 失败'}")
        print(f"中文支持: {'✅ 支持' if ocr_status['supports_chinese'] else '❌ 不支持'}")
        print(f"预估准确率: {ocr_status['estimated_accuracy']}")
        
        if ocr_status['recommended_settings']:
            print("推荐设置:")
            for key, value in ocr_status['recommended_settings'].items():
                print(f"  {key}: {value}")
        
        # 📊 生成和显示OCR准确率报告（新增功能）
        if selected_engine != 'none' and processor.results:
            print("\n正在生成OCR识别准确率报告...")
            accuracy_report = generate_ocr_accuracy_report(processor)
            print_ocr_accuracy_report(accuracy_report)
        
        print("\n📋 输出文件包含以下Sheet:")
        print("   📄 处理结果明细 - 所有图片文件的详细处理结果（含OCR匹配验证）")
        print("   👥 按人名统计 - 债务人还款凭证数量和金额统计")
        print("   📂 二级目录统计 - 各目录处理状态和Excel文件验证结果")
        print("   📊 处理摘要 - 整体处理情况汇总")
        
        print("\n🎨 Excel增强功能:")
        print("   🔗 文件路径超链接 - 可直接点击打开图片文件")
        print("   🎯 智能标色系统:")
        print("      ✅ 绿色：OCR与文件名一致")
        print("      🟡 橙色：OCR与文件名部分一致")
        print("      🔴 红色：OCR与文件名不一致")
        print("   📋 匹配状态列 - 显示详细的OCR验证结果")
        print("   📈 准确度评估 - 自动评估OCR识别质量")
        
        if DUCKDB_AVAILABLE:
            print("\n🚀 DuckDB性能优势:")
            print("   ⚡ 快速查询：SQL优化的高性能查询引擎")
            print("   💾 内存友好：轻量级，无需安装数据库服务器")
            print("   🔗 无缝集成：直接读取Excel文件，自动创建索引")

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"❌ 程序执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


def analyze_account_results(results: List[Dict]) -> Dict:
    """
    分析账户识别结果的统计信息
    
    Args:
        results: 处理结果列表
        
    Returns:
        统计信息字典
    """
    stats = {
        'total_files': len(results),
        'account_found': 0,
        'account_not_found': 0,
        'account_distribution': {},
        'filename_parsed': 0,
        'ocr_processed': 0
    }
    
    for result in results:
        # 统计账户识别情况
        account = result.get('还款账号', '')
        if account and account != '0000':
            stats['account_found'] += 1
            # 统计账户分布
            if account in stats['account_distribution']:
                stats['account_distribution'][account] += 1
            else:
                stats['account_distribution'][account] = 1
        else:
            stats['account_not_found'] += 1
        
        # 统计文件名解析情况
        if result.get('图片文件名姓名') or result.get('图片文件名金额'):
            stats['filename_parsed'] += 1
    
    return stats


def print_account_statistics(stats: Dict, ocr_enabled: bool):
    """
    打印账户识别统计信息
    
    Args:
        stats: 统计信息字典
        ocr_enabled: 是否启用了OCR
    """
    total = stats['total_files']
    found = stats['account_found']
    
    if total > 0:
        success_rate = (found / total) * 100
        print(f"\n📈 识别统计:")
        print(f"   📄 文件名解析: {stats['filename_parsed']}/{total} 个文件")
        
        if ocr_enabled:
            print(f"   🔢 账户号码识别: {found}/{total} 个文件 ({success_rate:.1f}%)")
            
            if stats['account_distribution']:
                print(f"   📊 账户分布:")
                # 按出现次数排序显示前5个账户
                sorted_accounts = sorted(
                    stats['account_distribution'].items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )
                for account, count in sorted_accounts[:5]:
                    print(f"      ***{account}: {count} 次")
                    
                if len(sorted_accounts) > 5:
                    print(f"      ... 还有 {len(sorted_accounts) - 5} 个其他账户")
        else:
            print(f"   ⚡ 快速模式: 跳过OCR账户识别")
            
        if stats['account_not_found'] > 0:
            print(f"   ⚠️  未识别账户: {stats['account_not_found']} 个文件（使用默认值0000）")


def generate_ocr_accuracy_report(processor) -> Dict:
    """
    生成OCR识别准确率报告
    
    Args:
        processor: PaymentReceiptProcessor实例
        
    Returns:
        准确率报告字典
    """
    if not processor.results:
        return {'error': '没有处理结果可以分析'}
    
    report = {
        'ocr_engine': processor.ocr_engine_type,
        'engine_status': processor.get_ocr_engine_status(),
        'total_files': len(processor.results),
        'ocr_processed': 0,
        'ocr_success': 0,
        'match_analysis': {
            '一致': 0,
            '部分一致': 0, 
            '不一致': 0,
            '无法比较': 0,
            '无法验证': 0
        },
        'accuracy_distribution': {
            '优秀': 0,
            '良好': 0,
            '一般': 0,
            '差': 0,
            'OCR失败': 0,
            '错误': 0
        },
        'field_success_rates': {
            'payer_extraction': 0,
            'account_extraction': 0,
            'amount_extraction': 0,
            'time_extraction': 0
        },
        'recommendations': []
    }
    
    try:
        # 统计各种匹配状态
        for result in processor.results:
            # OCR处理统计
            if result.get('还款人') or result.get('还款账号') or result.get('图片OCR识别金额'):
                report['ocr_processed'] += 1
                
                if (result.get('还款人') and result.get('还款人') != '未识别' and 
                    result.get('还款人') != '' and result.get('还款人') != '未知'):
                    report['ocr_success'] += 1
                    report['field_success_rates']['payer_extraction'] += 1
                
                if result.get('还款账号') and result.get('还款账号') != '0000' and result.get('还款账号') != '':
                    report['field_success_rates']['account_extraction'] += 1
                    
                if result.get('图片OCR识别金额') and result.get('图片OCR识别金额') != '':
                    report['field_success_rates']['amount_extraction'] += 1
                    
                if result.get('还款时间') and result.get('还款时间') != '':
                    report['field_success_rates']['time_extraction'] += 1
            
            # 匹配状态统计
            match_status = result.get('OCR文件名匹配状态', '无法验证')
            if match_status in report['match_analysis']:
                report['match_analysis'][match_status] += 1
            
            # 准确度分布统计
            accuracy = result.get('OCR准确度评估', 'OCR失败')
            if accuracy in report['accuracy_distribution']:
                report['accuracy_distribution'][accuracy] += 1
        
        # 计算成功率
        if report['ocr_processed'] > 0:
            for field in report['field_success_rates']:
                report['field_success_rates'][field] = (
                    report['field_success_rates'][field] / report['ocr_processed']
                ) * 100
        
        # 计算总体准确率
        consistent_count = report['match_analysis']['一致'] + report['match_analysis']['部分一致']
        comparable_count = sum(report['match_analysis'].values()) - report['match_analysis']['无法验证']
        
        if comparable_count > 0:
            report['overall_accuracy'] = (consistent_count / comparable_count) * 100
        else:
            report['overall_accuracy'] = 0
        
        # 生成建议
        if report['overall_accuracy'] < 70:
            report['recommendations'].append("OCR整体准确率偏低，建议检查图片质量或考虑更换OCR引擎")
        
        if report['field_success_rates']['payer_extraction'] < 80:
            report['recommendations'].append("付款方姓名识别率偏低，建议优化姓名提取算法")
            
        if report['field_success_rates']['account_extraction'] < 60:
            report['recommendations'].append("账户号码识别率偏低，建议优化账号提取关键词")
        
        if report['match_analysis']['不一致'] > report['total_files'] * 0.2:
            report['recommendations'].append("OCR与文件名不匹配率较高，建议检查文件命名规范")
            
        if not report['recommendations']:
            report['recommendations'].append("OCR识别效果良好，无需特别优化")
    
    except Exception as e:
        report['error'] = f"报告生成失败: {str(e)}"
    
    return report

def print_ocr_accuracy_report(report: Dict):
    """
    打印OCR准确率报告
    
    Args:
        report: 准确率报告字典
    """
    if 'error' in report:
        print(f"❌ {report['error']}")
        return
    
    print("\n" + "="*80)
    print("📊 OCR识别准确率报告")
    print("="*80)
    
    # OCR引擎信息
    engine_status = report['engine_status']
    print(f"🔧 OCR引擎信息:")
    print(f"   引擎类型: {report['ocr_engine']}")
    print(f"   是否初始化: {'✅' if engine_status['is_initialized'] else '❌'}")
    print(f"   中文支持: {'✅' if engine_status['supports_chinese'] else '❌'}")
    print(f"   预估准确率: {engine_status['estimated_accuracy']}")
    
    if engine_status['recommended_settings']:
        print(f"   推荐设置:")
        for key, value in engine_status['recommended_settings'].items():
            print(f"     {key}: {value}")
    
    # 处理统计
    print(f"\n📋 处理统计:")
    print(f"   总文件数: {report['total_files']}")
    print(f"   OCR处理数: {report['ocr_processed']}")
    print(f"   OCR成功数: {report['ocr_success']}")
    if report['ocr_processed'] > 0:
        success_rate = (report['ocr_success'] / report['ocr_processed']) * 100
        print(f"   OCR成功率: {success_rate:.1f}%")
    
    # 字段提取成功率
    print(f"\n🎯 字段提取成功率:")
    field_names = {
        'payer_extraction': '付款方姓名',
        'account_extraction': '账户号码',
        'amount_extraction': '还款金额', 
        'time_extraction': '还款时间'
    }
    
    for field, rate in report['field_success_rates'].items():
        field_name = field_names.get(field, field)
        status = "✅" if rate >= 80 else "⚠️" if rate >= 60 else "❌"
        print(f"   {status} {field_name}: {rate:.1f}%")
    
    # 匹配状态分析
    print(f"\n🔍 OCR与文件名匹配分析:")
    for status, count in report['match_analysis'].items():
        if count > 0:
            percentage = (count / report['total_files']) * 100
            status_icon = {
                '一致': '✅',
                '部分一致': '🟡', 
                '不一致': '❌',
                '无法比较': '⚪',
                '无法验证': '⚫'
            }.get(status, '❓')
            print(f"   {status_icon} {status}: {count} 个文件 ({percentage:.1f}%)")
    
    # 总体准确率
    if 'overall_accuracy' in report:
        accuracy = report['overall_accuracy']
        accuracy_icon = "✅" if accuracy >= 85 else "🟡" if accuracy >= 70 else "❌"
        print(f"\n🏆 总体匹配准确率: {accuracy_icon} {accuracy:.1f}%")
    
    # 推荐建议
    print(f"\n💡 优化建议:")
    for i, recommendation in enumerate(report['recommendations'], 1):
        print(f"   {i}. {recommendation}")
    
    print("="*80)

def test_ocr_recognition(image_path: str):
    """
    测试OCR识别功能的修复效果 - 增强调试版

    Args:
        image_path: 测试图片路径
    """
    print("=" * 60)
    print("🔧 OCR识别功能深度测试")
    print("=" * 60)
    print(f"📸 测试图片: {image_path}")

    try:
        # 统一使用Path对象处理路径
        image_path_obj = Path(image_path)

        # 检查文件是否存在
        if not image_path_obj.exists():
            print(f"❌ 测试图片不存在: {image_path}")
            print(f"   完整路径: {image_path_obj.absolute()}")
            print(f"   父目录存在: {'✅' if image_path_obj.parent.exists() else '❌'}")

            # 尝试列出父目录内容
            if image_path_obj.parent.exists():
                print(f"   父目录内容:")
                try:
                    for item in image_path_obj.parent.iterdir():
                        if item.is_file() and item.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
                            print(f"     📄 {item.name}")
                except Exception as e:
                    print(f"     ❌ 无法列出目录内容: {e}")
            return

        # 创建处理器实例
        processor = PaymentReceiptProcessor(".", ocr_engine='auto')

        # 测试OCR引擎初始化
        print(f"\n🔧 OCR引擎状态:")
        print(f"   引擎类型: {processor.ocr_engine_type}")
        print(f"   是否可用: {'✅ 可用' if processor.ocr else '❌ 不可用'}")

        if not processor.ocr and processor.ocr_engine_type != 'tesseract':
            print("❌ OCR引擎未初始化，请检查OCR引擎安装")
            return
        print(f"\n🔍 步骤1: 原始OCR识别测试...")

        # 根据当前OCR引擎调用相应的方法
        if processor.ocr_engine_type == 'tesseract':
            raw_text = processor._perform_tesseract(image_path_obj)
        elif processor.ocr_engine_type == 'paddleocr':
            raw_text = processor._perform_paddleocr(image_path_obj)
        elif processor.ocr_engine_type == 'easyocr':
            raw_text = processor._perform_easyocr(image_path_obj)
        elif processor.ocr_engine_type == 'ollama':
            raw_text = processor._perform_ollama_ocr(image_path_obj)
        else:
            print(f"   ❌ 未知的OCR引擎类型: {processor.ocr_engine_type}")
            return

        print(f"   原始OCR文本长度: {len(raw_text) if raw_text else 0} 字符")
        
        if raw_text:
            print(f"   ✅ 原始OCR成功")
            print(f"   📄 文本预览 (前200字符):")
            print(f"   {repr(raw_text[:200])}")
            
            # 测试付款方提取
            print(f"\n🔍 步骤2: 付款方信息提取测试...")
            payer = processor._extract_payer_info(raw_text)
            print(f"   提取结果: 【{payer}】")
            
            # 测试账户提取
            print(f"\n🔍 步骤3: 账户信息提取测试...")
            account = processor._extract_account_last4(raw_text)
            print(f"   提取结果: ***{account if account else '空'}")
            
            # 详细分析文本内容
            print(f"\n🔍 步骤4: 文本内容详细分析...")
            
            # 检查关键词
            payer_keywords = ['交易户名', '付款方', '付款人', '汇款人', '对方户名', '客户姓名']
            account_keywords = ['交易卡号', '付款卡号', '账户', '卡号']
            
            print("   🔎 付款方关键词检查:")
            for keyword in payer_keywords:
                if keyword in raw_text:
                    print(f"      ✅ 找到: {keyword}")
                    # 显示关键词周围的文本
                    start = max(0, raw_text.find(keyword) - 10)
                    end = min(len(raw_text), raw_text.find(keyword) + len(keyword) + 20)
                    context = raw_text[start:end].replace('\n', ' ')
                    print(f"         上下文: ...{context}...")
                else:
                    print(f"      ❌ 未找到: {keyword}")
            
            print("   🔎 账户关键词检查:")
            for keyword in account_keywords:
                if keyword in raw_text:
                    print(f"      ✅ 找到: {keyword}")
                    start = max(0, raw_text.find(keyword) - 10)
                    end = min(len(raw_text), raw_text.find(keyword) + len(keyword) + 30)
                    context = raw_text[start:end].replace('\n', ' ')
                    print(f"         上下文: ...{context}...")
                else:
                    print(f"      ❌ 未找到: {keyword}")
            
            # 检查数字模式
            print("   🔎 数字模式检查:")
            card_patterns = [
                r'\d{4}\s*\*+\s*\d{4}',  # 部分遮挡格式
                r'\d{16,19}',            # 完整卡号
                r'\d{4}\s+\d{4}',        # 分段数字
            ]
            
            for i, pattern in enumerate(card_patterns, 1):
                matches = re.findall(pattern, raw_text)
                if matches:
                    print(f"      ✅ 模式{i} 找到: {matches}")
                else:
                    print(f"      ❌ 模式{i} 未找到")
                    
        else:
            print("   ❌ 原始OCR失败，无法提取文本")
        
        # 测试完整流程
        print(f"\n🔍 步骤5: 完整OCR流程测试...")
        ocr_result = processor.ocr_extract_info(image_path_obj)
        
        print(f"\n📋 完整OCR识别结果:")
        print(f"   识别成功: {'✅' if ocr_result['ocr_success'] else '❌'}")
        print(f"   付款方: 【{ocr_result['payer']}】")
        print(f"   账户后4位: ***{ocr_result['account_last4']}") 
        print(f"   还款来源: 【{ocr_result['payment_source']}】")
        
    except Exception as e:
        print(f"❌ 测试过程异常: {str(e)}")
        import traceback
        print(f"📋 详细异常信息:\n{traceback.format_exc()}")
    
    print("=" * 60)


if __name__ == "__main__":
    main()

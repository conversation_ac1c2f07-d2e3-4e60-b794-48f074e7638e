#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署配置脚本
用于调整主程序文件路径以适应不同的部署环境
"""

import os
import re
import shutil
from pathlib import Path

def update_payment_processor_paths():
    """更新还款凭证处理器中的路径配置"""
    processor_file = "payment_receipt_processor.py"
    backup_file = "payment_receipt_processor.py.bak"
    
    if not os.path.exists(processor_file):
        print(f"警告: {processor_file} 文件不存在")
        return False
    
    # 备份原文件
    shutil.copy2(processor_file, backup_file)
    print(f"已备份原文件到: {backup_file}")
    
    # 读取文件内容
    with open(processor_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要修改的路径配置
    replacements = [
        # 债务人明细文件路径 - 改为相对路径
        (r'excel_file\s*=\s*r?"[^"]*民生4期债务人明细\.xlsx"', 
         'excel_file = os.path.join("data", "民生4期债务人明细.xlsx")'),
        
        # 基础文件夹路径 - 设为可配置
        (r'base_folder\s*=\s*r?"[^"]*"', 
         'base_folder = os.getenv("PAYMENT_BASE_FOLDER", r"D:\\民生4期回款")'),
        
        # DuckDB数据库文件路径
        (r'db_file\s*=\s*"debtors\.duckdb"', 
         'db_file = os.path.join(os.path.dirname(__file__), "debtors.duckdb")'),
    ]
    
    modified = False
    for pattern, replacement in replacements:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            modified = True
            print(f"已修改模式: {pattern}")
    
    if modified:
        # 在文件开头添加导入语句（如果不存在）
        if "import os" not in content[:500]:
            content = "import os\n" + content
        
        # 写入修改后的内容
        with open(processor_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ {processor_file} 路径配置已更新")
    else:
        print(f"○ {processor_file} 无需修改")
        # 删除备份文件
        os.remove(backup_file)
    
    return True

def update_registration_bot_paths():
    """更新还款登记机器人中的路径配置"""
    bot_file = "payment_registration_bot_playwright.py"
    backup_file = "payment_registration_bot_playwright.py.bak"
    
    if not os.path.exists(bot_file):
        print(f"警告: {bot_file} 文件不存在")
        return False
    
    # 备份原文件
    shutil.copy2(bot_file, backup_file)
    print(f"已备份原文件到: {backup_file}")
    
    # 读取文件内容
    with open(bot_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要修改的路径配置
    replacements = [
        # 默认文件夹路径 - 设为可配置
        (r'folder_path\s*=\s*r?"D:\\\\还款登记操作"', 
         'folder_path = os.getenv("PAYMENT_FOLDER_PATH", r"D:\\还款登记操作")'),
        
        # 配置文件路径 - 改为相对路径
        (r'config_path:\s*str\s*=\s*"config\.json"', 
         'config_path: str = os.path.join(os.path.dirname(__file__), "config.json")'),
    ]
    
    modified = False
    for pattern, replacement in replacements:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            modified = True
            print(f"已修改模式: {pattern}")
    
    if modified:
        # 写入修改后的内容
        with open(bot_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ {bot_file} 路径配置已更新")
    else:
        print(f"○ {bot_file} 无需修改")
        # 删除备份文件
        os.remove(backup_file)
    
    return True

def create_env_file():
    """创建环境变量配置文件"""
    env_content = """# 部署环境配置文件
# 复制此文件为 .env 并根据实际部署环境修改路径

# 还款凭证处理器基础文件夹路径
PAYMENT_BASE_FOLDER=D:\\民生4期回款

# 还款登记操作文件夹路径
PAYMENT_FOLDER_PATH=D:\\还款登记操作

# 数据库文件路径（相对于程序目录）
DB_FILE_PATH=debtors.duckdb

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 浏览器运行模式 (true=无头模式, false=显示浏览器)
HEADLESS_MODE=false
"""
    
    env_file = ".env.example"
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print(f"✓ 已创建环境配置示例文件: {env_file}")

def main():
    """主函数"""
    print("=" * 50)
    print("部署配置脚本")
    print("=" * 50)
    print()
    
    print("1. 更新还款凭证处理器路径配置...")
    update_payment_processor_paths()
    print()
    
    print("2. 更新还款登记机器人路径配置...")
    update_registration_bot_paths()
    print()
    
    print("3. 创建环境配置文件...")
    create_env_file()
    print()
    
    print("=" * 50)
    print("部署配置完成!")
    print("=" * 50)
    print()
    print("下一步:")
    print("1. 根据实际环境修改 .env.example 文件并重命名为 .env")
    print("2. 将债务人明细Excel文件放入 data/ 目录")
    print("3. 修改 config.json 中的用户配置")
    print("4. 运行相应的启动脚本")

if __name__ == "__main__":
    main() 